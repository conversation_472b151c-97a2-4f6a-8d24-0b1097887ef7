<!--<< Breadcrumb Section Start >>-->
<div class="breadcrumb-wrapper bg-cover" style="background-image: url('<?php echo base_url() ?>backend/themes/kidsa/assets/img/breadcrumb.png');">
    <div class="line-shape">
        <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/breadcrumb-shape/line.png" alt="shape-img">
    </div>
    <div class="plane-shape float-bob-y">
        <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/breadcrumb-shape/plane.png" alt="shape-img">
    </div>
    <div class="doll-shape float-bob-x">
        <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/breadcrumb-shape/doll.png" alt="shape-img">
    </div>
    <div class="parasuit-shape float-bob-y">
        <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/breadcrumb-shape/parasuit.png" alt="shape-img">
    </div>
    <div class="frame-shape">
        <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/breadcrumb-shape/frame.png" alt="shape-img">
    </div>
    <div class="bee-shape float-bob-x">
        <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/breadcrumb-shape/bee.png" alt="shape-img">
    </div>
    <div class="container">
        <div class="page-heading">
            <nav aria-label="breadcrumb" class="breadcrumb-nav wow fadeInUp" data-wow-delay=".2s">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo base_url(); ?>">Home</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Gallery</li>
                </ol>
            </nav>
            <h1 class="wow fadeInUp" data-wow-delay=".3s">Gallery</h1>
            <p class="wow fadeInUp" data-wow-delay=".4s">Discover the vibrant moments and memories from our school community</p>
        </div>
    </div>
</div>
<?php  

    $cms_program = $this->db->get_where('front_cms_programs' , array('school_id' => $this->school_id , 'url !=' => ''))->result_array();
?>
<!-- Event Section Start -->
        <section class="event-section section-padding bg-cover" style="background-image: url('<?php echo base_url('backend/themes/kidsa/') ?>assets/img/event-bg.jpg');" id="event">
            <div class="event-top-shape">
                <div class="wave" style="background-image: url('<?php echo base_url('backend/themes/kidsa/') ?>assets/img/event-top-shape.png');"></div>
                <div class="wave" style="background-image: url('<?php echo base_url('backend/themes/kidsa/') ?>assets/img/event-top-shape.png');"></div>
            </div>
            <div class="container">
                <!-- Gallery Filter Section -->
                <div class="gallery-filter-section text-center mb-5">
                    <div class="section-title">
                        <span class="wow fadeInUp">Our Gallery</span>
                        <h2 class="wow fadeInUp" data-wow-delay=".3s">Explore Our School Life</h2>
                    </div>
                    <div class="gallery-search-box wow fadeInUp" data-wow-delay=".4s">
                        <div class="search-input-wrapper">
                            <input type="text" id="gallery-search" placeholder="Search gallery..." class="gallery-search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>

                    <div class="gallery-filter-buttons wow fadeInUp" data-wow-delay=".5s">
                        <button class="filter-btn active" data-filter="*">All</button>
                        <button class="filter-btn" data-filter=".events">Events</button>
                        <button class="filter-btn" data-filter=".academics">Academics</button>
                        <button class="filter-btn" data-filter=".sports">Sports</button>
                        <button class="filter-btn" data-filter=".activities">Activities</button>
                    </div>
                </div>

                <div class="row gallery-grid">
                    <?php if($cms_program != null) :
                        foreach($cms_program as $index => $gallery) :
                            $feature_image = $gallery['feature_image'] != '' ? $gallery['feature_image'] : base_url('backend/themes/kidsa/assets/img/117998_sun_512x512.png');

                            // Assign categories based on title or create a simple rotation
                            $categories = ['events', 'academics', 'sports', 'activities'];
                            $category = $categories[$index % count($categories)];
                            $delay = ($index % 3) * 0.2 + 0.3;
                    ?>
                    <div class="col-xl-4 col-lg-6 col-md-6 gallery-item <?php echo $category; ?> wow fadeInUp" data-wow-delay="<?php echo $delay; ?>s">
                        <div class="gallery-item-enhanced">
                            <div class="gallery-image-wrapper">
                                <img src="<?php echo $feature_image ?>" alt="<?php echo $gallery['title'] ?>" class="gallery-image img-popup" loading="lazy" tabindex="0">
                                <div class="gallery-overlay">
                                    <div class="gallery-overlay-content">
                                        <a href="<?php echo $feature_image ?>" class="gallery-zoom img-popup" title="View <?php echo $gallery['title'] ?> in full size" aria-label="Open image in lightbox">
                                            <i class="fas fa-search-plus" aria-hidden="true"></i>
                                        </a>
                                        <a href="<?php echo base_url().$gallery['url']; ?>" class="gallery-link" title="View <?php echo $gallery['title'] ?> details" aria-label="View gallery details">
                                            <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                                        </a>
                                    </div>
                                </div>
                                <div class="gallery-shape">
                                    <img src="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/event/shape.png" alt="shape-img">
                                </div>
                            </div>
                            <div class="gallery-content">
                                <h3 class="gallery-title">
                                    <a href="<?php echo base_url().$gallery['url']; ?>"><?php echo $gallery['title'] ?></a>
                                </h3>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; endif; ?>
                </div>

                <!-- Load More Button -->
                <?php if(count($cms_program) > 6): ?>
                <div class="text-center mt-5">
                    <button id="load-more-gallery" class="theme-btn" data-loaded="6" data-total="<?php echo count($cms_program); ?>">
                        Load More <i class="fas fa-arrow-down"></i>
                    </button>
                </div>
                <?php endif; ?>

                <!-- No Results Message -->
                <div id="no-results" class="text-center mt-5" style="display: none;">
                    <div class="no-results-content">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4>No Results Found</h4>
                        <p>Try adjusting your search or filter criteria.</p>
                    </div>
                </div>
            </div>
        </section>




