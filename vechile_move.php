<?php

// Firebase endpoint URL
$firebaseUrl = "https://schoolxon-5bbb5-default-rtdb.firebaseio.com/trips/trip_6/3.json";

// Vehicle ID
$vehicle_id = "3";

// Driver ID
$driver_id = "*********";

// Start Point
$start_lat = 25.6175504;
$start_lng = 85.**************;

// End Point
$end_lat = 25.6207961;
$end_lng = 85.1719948;

// --- Placeholder for "between data of Ashok Rajpath" ---
// To get actual points along Ashok Rajpath, you would typically use a geocoding
// or routing API (e.g., Google Maps Directions API, OpenStreetMap Nominatim/Routing).
// For demonstration, I'm just creating a few hypothetical points.
// You would need to replace this with your actual logic to generate path coordinates.
$ashokRajpathPoints = [];
// Example: Manually adding a few points along a hypothetical route on Ashok Rajpath
// These are just illustrative.
$ashokRajpathPoints[] = ['lat' => 25.6180000, 'lng' => 85.1470000];
$ashokRajpathPoints[] = ['lat' => 25.6185000, 'lng' => 85.1500000];
$ashokRajpathPoints[] = ['lat' => 25.6190000, 'lng' => 85.1530000];
$ashokRajpathPoints[] = ['lat' => 25.6195000, 'lng' => 85.1560000];
$ashokRajpathPoints[] = ['lat' => 25.6200000, 'lng' => 85.1590000];


// Prepare the data to be sent to Firebase
$data = [
    "vehicle_id" => $vehicle_id,
    "driver_id" => $driver_id,
    "start_point" => [
        "latitude" => $start_lat,
        "longitude" => $start_lng
    ],
    "end_point" => [
        "latitude" => $end_lat,
        "longitude" => $end_lng
    ],
    "path_points" => [], // Array to store all path points, including start, end, and between
    "timestamp" => time() // Unix timestamp for when this data was pushed
];

// Add the start point to path_points
$data['path_points'][] = [
    'latitude' => $start_lat,
    'longitude' => $start_lng,
    'point_type' => 'start'
];

// Add the "between" points (Ashok Rajpath points)
foreach ($ashokRajpathPoints as $point) {
    $data['path_points'][] = [
        'latitude' => $point['lat'],
        'longitude' => $point['lng'],
        'point_type' => 'intermediate'
    ];
}

// Add the end point to path_points
$data['path_points'][] = [
    'latitude' => $end_lat,
    'longitude' => $end_lng,
    'point_type' => 'end'
];


// Encode the data as JSON
$jsonData = json_encode($data);

// Initialize cURL session
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $firebaseUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return the response as a string
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT"); // Use PUT to overwrite, or POST to push a new unique ID
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData); // Set the JSON data
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);

// Execute the cURL request
$response = curl_exec($ch);

// Check for cURL errors
if (curl_errno($ch)) {
    echo 'cURL error: ' . curl_error($ch);
} else {
    // Decode the Firebase response
    $firebaseResponse = json_decode($response, true);
    if ($firebaseResponse === null && json_last_error() !== JSON_ERROR_NONE) {
        echo "Firebase response (not JSON or empty): " . $response . "\n";
    } else {
        echo "Data successfully pushed to Firebase.\n";
        echo "Firebase Response:\n";
        print_r($firebaseResponse);
    }
}

// Close cURL session
curl_close($ch);

?>