<?php
// Firebase base URL (without vehicle id)
$firebase_url = "https://schoolxon-5bbb5-default-rtdb.firebaseio.com/trips/trip_5/";

// Vehicle ID
$vehicle_id = "3";

// Driver ID
$driver_id = "705000071";

// Start Point
$start_lat = 25.6175504;
$start_lng = 85.14511019999999;

// End Point
$end_lat = 25.6207961;
$end_lng = 85.1719948;

// Total steps (movements)
$total_steps = 100;

// Calculate increments
$lat_step = ($end_lat - $start_lat) / $total_steps;
$lng_step = ($end_lng - $start_lng) / $total_steps;

// Current position
$current_lat = $start_lat;
$current_lng = $start_lng;

for ($i = 0; $i <= $total_steps; $i++) {
    // Prepare data array
    $data = [
        "driver_id" => $driver_id,
        "vehicle_id" => $vehicle_id,
        "latitude" => round($current_lat, 6),
        "longitude" => round($current_lng, 6),
        "speed" => rand(25, 40),
        "timestamp" => gmdate("Y-m-d\TH:i:s\Z")
    ];

    // Convert to JSON
    $json_data = json_encode($data);

    // Set cURL request to Firebase — now POST for adding new record
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $firebase_url . $vehicle_id . ".json");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    $result = curl_exec($ch);
    curl_close($ch);

    // Show output
    echo "Pushed location $i → Lat: {$data['latitude']} | Lng: {$data['longitude']} | Speed: {$data['speed']} | " . date("H:i:s") . "\n";

    // Increment location
    $current_lat += $lat_step;
    $current_lng += $lng_step;

    // Wait 5 seconds
    sleep(5);
}

echo "Trip completed!\n";
