<?php
$teacher_details = [
      'principal'                     => 'Principal',
      'total_teachers'                => 'Total no. of Teachers',
      'pgt'                           => 'PGT',
      'tgt'                           => 'TGT',
      'prt'                           => 'PRT',
      'teacher_section_ratio'         => 'Teachers section ratio',
      'special_educator'              => 'Details of special educator',
      'counsellor_wellness_teacher'   => 'Details of counsellor and wellness Teachers'
  ];

  $school_infrastructure = [
      'total_campus_area'       => 'Total campus area of the school (In sq mtr)',
      'classrooms'              => 'No. and size of the classrooms (In sq mtr)',
      'laboratories'            => 'No. and size of laboratories including computer labs (In sq mtr)',
      'internet_facility'       => 'Internet facility',
      'girls_toilets'           => 'No. of girls toilets',
      'boys_toilets'            => 'No. of boys toilets',
      'cwsn_toilets_boys'       => 'CWSN Toilets Boys',
      'cwsn_toilets_girls'      => 'CWSN Toilets Girls',
      'youtube_video_link'      => 'Link of YouTube video of the inspection of school covering the infrastructure of the school'
  ];

?>
<div class="" style="padding:5px">
    <section class="genarel-information-form-title" style="margin-bottom:10px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="section-title">
                      <!-- <h2 style="text-align: center;">
                          APPENDIX-IX</h2> -->
                      <h3 style="text-align: center;">
                          Mandatory Public Disclosure</h3>
                      <!-- <p>About Us</p> --></div>
              </div>
          </div>
      </div>
  </section>
  <section class="genarel-information-details" style="margin-bottom:10px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header">
                        <h3 class="">
                          A. General Information  </h3>
                      </div>
                      <div class="card-body">
                        <div style="overflow-x:auto;">
                          <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th>SL No.</th>
                                    <th>Information</th>
                                    <th>Details</th>
                                </tr>
                                <tr>
                                    <td>1</td>
                                    <td>Name of the School</td>
                                    <td><?= isset($settingdata['name']) ? htmlspecialchars($settingdata['name']) : '' ?></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Affiliation No. (If applicable)</td>
                                    <td><?= isset($settingdata['affiliation_no']) ? htmlspecialchars($settingdata['affiliation_no']) : '' ?></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>School Code (If applicable)</td>
                                    <td><?= isset($settingdata['udise']) ? htmlspecialchars($settingdata['udise']) : '' ?></td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>Complete Address With Pincode</td>
                                    <td><?= isset($settingdata['address']) ? nl2br(htmlspecialchars($settingdata['address'])) : '' ?></td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>Principal Name</td>
                                    <td><?= isset($settingdata['principal_name']) ? htmlspecialchars($settingdata['principal_name']) : '' ?></td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>Principal Qualification</td>
                                    <td><?= isset($settingdata['principal_qualification']) ? htmlspecialchars($settingdata['principal_qualification']) : '' ?></td>
                                </tr>
                                <tr>
                                    <td>7</td>
                                    <td>School Email Id</td>
                                    <td style="text-transform:lowercase;">
                                        <?= isset($settingdata['email']) ? htmlspecialchars($settingdata['email']) : '' ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>8</td>
                                    <td>Contact Details (Landline/Mobile)</td>
                                    <td><?= isset($settingdata['phone']) ? htmlspecialchars($settingdata['phone']) : '' ?></td>
                                </tr>
                            </tbody>
                        </table>
                      </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>

  <section class="genarel-information-details" style="margin-bottom:10px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header">
                        <h3> B. Documents And Information</h3>
                      </div>
                      <div class="card-body"  style="overflow-x:auto;">
                          <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th>SL No.</th>
                                    <th>Documents / Information</th>
                                    <th>Links of uploaded documents on your school's website</th>
                                </tr>
                                <?php $sl = 1; ?>
                                <?php foreach ($document_fields as $key => $label): ?>
                                    <tr>
                                        <td><?php echo $sl++; ?></td>
                                        <td><?php echo $label; ?></td>
                                        <td>
                                            <div class="button-sec">
                                                <?php 
                                                  // Find if this document exists
                                                  $existingDocument = array_filter($documents, function($doc) use ($label) {
                                                      return $doc['label'] === $label;
                                                  });

                                                  if (!empty($existingDocument)) {
                                                      $existingDocument = array_values($existingDocument)[0]; // Get first match
                                                  ?>
                                                      <a href="<?php echo $existingDocument['document_link']; ?>" 
                                                         download="<?php echo $existingDocument['document_name']; ?>" 
                                                         data-toggle="tooltip" 
                                                         title="Download Document" 
                                                         class='btn btn-info btn-sm'>
                                                          <i class="fa fa-download"></i>
                                                      </a>

                                                  <?php } else{ echo "N/A" ; } ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                      </div>
                  </div>
              </div>
          </div>
          <div class="row">
              <div class="col-md-12">
                  <div class="discloser-content">
                      <p>
                          the schools needs to upload the self attested copies of above listed documetns by chairman /manager/ secretary and principal. incase, it is notice datlater stageth at uploaded documents are not genuine then school shall be liable for action aspernorms.</p>
                  </div>
              </div>
          </div>
      </div>
  </section>
  <section class="genarel-information-details" style="margin-bottom:20px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header">
                        <h3> C. Results And Academy</h3>
                      </div>
                      <div class="card-body" style="overflow-x:auto;">
                          <table class="table table-bordered">
                              <tbody>
                                <tr>
                                    <th>SL No.</th>
                                    <th>Documents / Information</th>
                                    <th>Links of uploaded documents on your school's website</th>
                                </tr>
                                <?php $sl = 1; ?>
                                <?php foreach ($additional_documents as $key => $label): ?>
                                    <tr>
                                        <td><?php echo $sl++; ?></td>
                                        <td><?php echo $label; ?></td>
                                        <td>
                                            <div class="button-sec">
                                                <?php 
                                                  // Find if this document exists
                                                  $existingDocument = array_filter($documents, function($doc) use ($label) {
                                                      return $doc['label'] === $label;
                                                  });

                                                  if (!empty($existingDocument)) {
                                                      $existingDocument = array_values($existingDocument)[0]; // Get first match
                                                  ?>
                                                      <a href="<?php echo $existingDocument['document_link']; ?>" 
                                                         download="<?php echo $existingDocument['document_name']; ?>" 
                                                         data-toggle="tooltip" 
                                                         title="Download Document" 
                                                         class='btn btn-info btn-sm'>
                                                          <i class="fa fa-download"></i>
                                                      </a>

                                                  <?php } else{ echo "N/A" ; } ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                              </tbody>
                          </table>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
  <section class="genarel-information-details" style="margin-bottom:20px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header">
                        <h3> Result class: X</h3>
                      </div>
                      <div class="card-body">
                        <div style="overflow-x:auto;">
                            <table>
                                <tbody>
                                    <tr>
                                        <th>
                                            SL No.</th>
                                        <th>
                                            Year</th>
                                        <th>
                                            No.Of Registered students</th>
                                        <th>
                                            No.Of students Passed</th>
                                        <th>
                                            Pass Percentage</th>
                                        <th>
                                            Remarks</th>
                                    </tr>
                                    <tr>
                                        <td>
                                            </td>
                                        <td>
                                            </td>
                                        <td>
                                            </td>
                                        <td>
                                            </td>
                                        <td>
                                            </td>
                                        <td>
                                            -</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
  <section class="genarel-information-details" style="margin-bottom:20px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header"><h3> Result class: XII</h3></div>
                      <div class="card-body" style="overflow-x:auto;">
                          <table>
                              <tbody>
                                  <tr>
                                      <th>
                                          SL No.</th>
                                      <th>
                                          No.Of Registered students</th>
                                      <th>
                                          No.Of students Passed</th>
                                      <th>
                                          Pass Percentage</th>
                                      <th>
                                          Remarks</th>
                                  </tr>
                                  <tr>
                                      <td>
                                          </td>
                                      <td>
                                          </td>
                                      <td>
                                          </td>
                                      <td>
                                          </td>
                                      <td>
                                          </td>
                                  </tr>
                              </tbody>
                          </table>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
  <section class="genarel-information-details" style="margin-bottom:20px;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header"><h3>
                          D. Staff (Teaching)</h3></div>
                      <div class="card-body">
                          <table class="table">
                            <thead>
                                <tr>
                                    <th>SL No.</th>
                                    <th>Information</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $sl_no = 1; 
                                foreach ($teacher_details as $field_name => $label): 
                                    // Find the matching field_value from $resultdata
                                    $value = '-'; // Default value if not found
                                    foreach ($resultdata as $data) {
                                        if ($data['field_name'] === $field_name) {
                                            $value = htmlspecialchars($data['field_value'], ENT_QUOTES, 'UTF-8');
                                            break;
                                        }
                                    }
                                ?>
                                <tr>
                                    <td><?= $sl_no++; ?></td>
                                    <td><?= $label; ?></td>
                                    <td><?= $value; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>

                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
  <section class="genarel-information-details" style="margin-bottom:2rem;">
      <div class="container">
          <div class="row">
              <div class="col-md-12">
                  <div class="table-information card">
                      <div class="card-header"><h3>
                          E. School Infrastructure</h3></div>
                      <div style="overflow-x:auto;" class="card-body">
                            <table class="table table-bordered">
                              <thead>
                                  <tr>
                                      <th>SL No.</th>
                                      <th>Information</th>
                                      <th>Details</th>
                                  </tr>
                              </thead>
                              <tbody>
                                  <?php 
                                  $sl_no = 1; 
                                  foreach ($school_infrastructure as $field_name => $label): 
                                      // Find the matching field_value from $resultdata
                                      $value = '-'; // Default value if not found
                                      foreach ($resultdata as $data) {
                                          if ($data['field_name'] === $field_name) {
                                              $value = htmlspecialchars($data['field_value'], ENT_QUOTES, 'UTF-8');
                                              break;
                                          }
                                      }
                                  ?>
                                  <tr>
                                      <td><?= $sl_no++; ?></td>
                                      <td><?= $label; ?></td>
                                      <td>
                                          <?php if ($field_name === 'youtube_video_link' && $value !== '-'): ?>
                                              <div class="button-sec">
                                                  <a allow="autoplay" class="link-btn" href="<?= $value ?>" target="_blank">Link</a>
                                              </div>
                                          <?php else: ?>
                                              <?= $value; ?>
                                          <?php endif; ?>
                                      </td>
                                  </tr>
                                  <?php endforeach; ?>
                              </tbody>
                          </table>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
</div>