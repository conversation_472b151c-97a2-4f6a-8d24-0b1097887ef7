<?php
/**
 * Test script to verify Firebase URL generation fixes
 * This script helps debug the double slash issue in Firebase URLs
 */

echo "<h2>Firebase URL Generation Test</h2>";

// Test different database URL formats
$test_urls = [
    "https://schoolxon-5bbb5-default-rtdb.firebaseio.com",
    "https://schoolxon-5bbb5-default-rtdb.firebaseio.com/",
    "https://schoolxon-5bbb5-default-rtdb.firebaseio.com//",
];

$trip_id = 5;
$vehicle_id = 3;
$firebase_path = "trips/trip_{$trip_id}/{$vehicle_id}";

echo "<h3>Testing URL Generation</h3>";
foreach ($test_urls as $base_url) {
    echo "<p><strong>Base URL:</strong> <code>{$base_url}</code></p>";
    
    // Old method (with double slash issue)
    $old_url = $base_url . '/' . $firebase_path . '.json';
    echo "<p><strong>Old Method:</strong> <a href='{$old_url}' target='_blank'>{$old_url}</a></p>";
    
    // New method (fixed)
    $database_url = rtrim($base_url, '/');
    $new_url = $database_url . '/' . $firebase_path . '.json';
    echo "<p><strong>New Method:</strong> <a href='{$new_url}' target='_blank'>{$new_url}</a></p>";
    
    echo "<hr>";
}

echo "<h3>Your Specific URL</h3>";
$your_base = "https://schoolxon-5bbb5-default-rtdb.firebaseio.com";
$fixed_url = rtrim($your_base, '/') . '/trips/trip_5/3.json';
echo "<p><strong>Fixed URL:</strong> <a href='{$fixed_url}' target='_blank'>{$fixed_url}</a></p>";

echo "<h3>What Was Fixed</h3>";
echo "<ul>";
echo "<li><strong>Problem:</strong> Double slash (//) in Firebase URLs</li>";
echo "<li><strong>Cause:</strong> Base URL ending with '/' + adding another '/' in code</li>";
echo "<li><strong>Solution:</strong> Use <code>rtrim(\$database_url, '/')</code> to remove trailing slashes</li>";
echo "<li><strong>Files Updated:</strong> Trip_model.php, Trip.php (API), trip_list.php</li>";
echo "</ul>";

echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li>Run the database migration to add firebase_json_url field</li>";
echo "<li>Configure Google Maps API key in system settings</li>";
echo "<li>Test creating new trips via API</li>";
echo "<li>Check that Firebase URLs are generated correctly</li>";
echo "<li>Test live tracking functionality</li>";
echo "</ol>";
?>
