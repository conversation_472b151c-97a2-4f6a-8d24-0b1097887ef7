<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> <?php echo $this->lang->line('transport'); ?>
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-info-circle"></i> <?php echo $this->lang->line('trip_details'); ?></h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo site_url('admin/vehicle/trips/' . $trip['vechile_id']); ?>" class="btn btn-primary btn-sm"><i class="fa fa-arrow-left"></i> <?php echo $this->lang->line('back'); ?></a>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="box box-widget widget-user-2">
                                    <div class="widget-user-header bg-blue">
                                        <h4><?php echo $this->lang->line('trip_information'); ?></h4>
                                    </div>
                                    <div class="box-footer no-padding">
                                        <ul class="nav nav-stacked">
                                            <li><a><strong><?php echo $this->lang->line('trip_id'); ?>:</strong> <?php echo $trip['id']; ?></a></li>
                                            <li><a><strong><?php echo $this->lang->line('vehicle'); ?>:</strong> <?php echo $trip['vehicle_no']; ?> (<?php echo $trip['vehicle_model']; ?>)</a></li>
                                            <li><a><strong><?php echo $this->lang->line('driver'); ?>:</strong> <?php echo $trip['driver_name'] . ' ' . $trip['driver_surname']; ?></a></li>
                                            <li><a><strong><?php echo $this->lang->line('start_time'); ?>:</strong> <?php echo date($this->customlib->getSchoolDateFormat() . ' H:i:s', strtotime($trip['trip_start_time'])); ?></a></li>
                                            <li><a><strong><?php echo $this->lang->line('end_time'); ?>:</strong> 
                                                <?php 
                                                if (!empty($trip['trip_end_time'])) {
                                                    echo date($this->customlib->getSchoolDateFormat() . ' H:i:s', strtotime($trip['trip_end_time']));
                                                } else {
                                                    echo "-";
                                                }
                                                ?>
                                            </a></li>
                                            <li><a><strong><?php echo $this->lang->line('status'); ?>:</strong> 
                                                <?php 
                                                if ($trip['status'] == 'ongoing') {
                                                    echo '<span class="label label-success">Ongoing</span>';
                                                } else {
                                                    echo '<span class="label label-default">Completed</span>';
                                                }
                                                ?>
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="map" style="height: 300px; width: 100%;"></div>
                                <input type="hidden" id="trip_id" value="<?php echo $trip['id']; ?>">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="box box-primary">
                                    <div class="box-header with-border">
                                        <h3 class="box-title"><?php echo $this->lang->line('trip_path'); ?></h3>
                                    </div>
                                    <div class="box-body">
                                        <div id="trip_path_map" style="height: 400px; width: 100%;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="[https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap"](https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap") async defer></script>
<script src="[https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script](https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script)