<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-fire"></i> <?php echo $this->lang->line('firebase_configuration'); ?>
        </h1>
        <ol class="breadcrumb">
            <li><a href="<?php echo base_url(); ?>admin/dashboard"><i class="fa fa-dashboard"></i> <?php echo $this->lang->line('dashboard'); ?></a></li>
            <li><a href="<?php echo base_url(); ?>admin/vehicle"><?php echo $this->lang->line('vehicle'); ?></a></li>
            <li class="active"><?php echo $this->lang->line('firebase_configuration'); ?></li>
        </ol>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <?php if ($this->session->flashdata('msg')) { ?>
                    <div class="alert alert-success alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h4><i class="icon fa fa-check"></i> <?php echo $this->lang->line('success'); ?>!</h4>
                        <?php echo $this->session->flashdata('msg'); ?>
                    </div>
                <?php } ?>
                <?php if ($this->session->flashdata('error')) { ?>
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                        <h4><i class="icon fa fa-ban"></i> <?php echo $this->lang->line('error'); ?>!</h4>
                        <?php echo $this->session->flashdata('error'); ?>
                    </div>
                <?php } ?>

                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-cog"></i> Firebase Configuration</h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo base_url('admin/vehicle'); ?>" class="btn btn-primary btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to Vehicles
                            </a>
                        </div>
                    </div>

                    <form id="firebase-config-form" action="<?php echo site_url('admin/vehicle/firebase_config'); ?>" method="post" accept-charset="utf-8">
                        <div class="box-body">
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> 
                                <strong>Note:</strong> Configure your Firebase project settings here to enable live tracking and real-time trip data.
                                You can find these values in your Firebase project console under Project Settings.
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="project_name">Project Name <span class="req">*</span></label>
                                        <input type="text" class="form-control" name="project_name" id="project_name" 
                                               value="<?php echo set_value('project_name', !empty($firebase_config) ? $firebase_config->project_name : ''); ?>" 
                                               placeholder="your-project-name" required>
                                        <span class="text-danger"><?php echo form_error('project_name'); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="project_url">Project URL</label>
                                        <input type="url" class="form-control" name="project_url" id="project_url" 
                                               value="<?php echo set_value('project_url', !empty($firebase_config) ? $firebase_config->project_url : ''); ?>" 
                                               placeholder="https://your-project-name.web.app">
                                        <span class="text-danger"><?php echo form_error('project_url'); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="api_key">API Key <span class="req">*</span></label>
                                        <input type="text" class="form-control" name="api_key" id="api_key" 
                                               value="<?php echo set_value('api_key', !empty($firebase_config) ? $firebase_config->api_key : ''); ?>" 
                                               placeholder="AIzaSyC..." required>
                                        <span class="text-danger"><?php echo form_error('api_key'); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="database_url">Database URL <span class="req">*</span></label>
                                        <input type="url" class="form-control" name="database_url" id="database_url" 
                                               value="<?php echo set_value('database_url', !empty($firebase_config) ? $firebase_config->database_url : ''); ?>" 
                                               placeholder="https://your-project-name-default-rtdb.firebaseio.com/" required>
                                        <span class="text-danger"><?php echo form_error('database_url'); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="messaging_sender_id">Messaging Sender ID</label>
                                        <input type="text" class="form-control" name="messaging_sender_id" id="messaging_sender_id" 
                                               value="<?php echo set_value('messaging_sender_id', !empty($firebase_config) ? $firebase_config->messaging_sender_id : ''); ?>" 
                                               placeholder="123456789012">
                                        <span class="text-danger"><?php echo form_error('messaging_sender_id'); ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="app_id">App ID</label>
                                        <input type="text" class="form-control" name="app_id" id="app_id" 
                                               value="<?php echo set_value('app_id', !empty($firebase_config) ? $firebase_config->app_id : ''); ?>" 
                                               placeholder="1:123456789012:web:abcdef123456">
                                        <span class="text-danger"><?php echo form_error('app_id'); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="server_key">Server Key (for Push Notifications)</label>
                                        <input type="text" class="form-control" name="server_key" id="server_key" 
                                               value="<?php echo set_value('server_key', !empty($firebase_config) ? $firebase_config->server_key : ''); ?>" 
                                               placeholder="AAAA...">
                                        <span class="text-danger"><?php echo form_error('server_key'); ?></span>
                                        <small class="text-muted">This is used for sending push notifications to mobile apps.</small>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <i class="fa fa-warning"></i> 
                                <strong>Security Note:</strong> Keep your Firebase configuration secure. These keys provide access to your Firebase project.
                            </div>
                        </div>

                        <div class="box-footer">
                            <button type="submit" name="submit" value="submit" class="btn btn-info pull-right">
                                <i class="fa fa-save"></i> Save Configuration
                            </button>
                        </div>
                    </form>
                </div>

                <?php if (!empty($firebase_config)) { ?>
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-check-circle"></i> Current Configuration Status</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Project Name:</strong> <?php echo $firebase_config->project_name; ?></p>
                                <p><strong>Database URL:</strong> <?php echo $firebase_config->database_url; ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>API Key:</strong> <?php echo substr($firebase_config->api_key, 0, 10) . '...'; ?></p>
                                <p><strong>Last Updated:</strong> <?php echo date('Y-m-d H:i:s', strtotime($firebase_config->created_at)); ?></p>
                            </div>
                        </div>
                        <div class="alert alert-success">
                            <i class="fa fa-check"></i> Firebase is configured and ready for live tracking!
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    $('#firebase-config-form').on('submit', function(e) {
        // Basic validation
        var projectName = $('#project_name').val().trim();
        var apiKey = $('#api_key').val().trim();
        var databaseUrl = $('#database_url').val().trim();
        
        if (!projectName || !apiKey || !databaseUrl) {
            e.preventDefault();
            alert('Please fill in all required fields (Project Name, API Key, and Database URL).');
            return false;
        }
        
        // Show loading state
        $('button[type="submit"]').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
    });
});
</script>
