index.php

application/config/database.php
application/config/config.php



application/third_party_old/
smart_school2.rar
smart_school.zip
smart_school0202.zip
smartschool-701nulled.rar
smartschool-701nulled/
backend/captcha_images/
application/views/dmin/stuattendence/index.php
application/views/themes/kidsa1/
_2024A-smart-school.zip

git update-index --assume-unchanged application/config/config.php

git update-index --assume-unchanged application/config/database.php

git reset uploads

uploads/

application/tmp/
edulearn-template/

# === General ===
*.log
*.md
*.env
*.env.*
*.bak
*.sql
*.swp
*.zip
*.sh
.DS_Store
Thumbs.db

# === Node (package.json) ===
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-lock.yaml
package-lock.json

# === PHP Composer ===
vendor/
composer.lock

 

# === CodeIgniter (optional) ===
application/logs/
application/cache/

# === IDEs ===
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

 

# === Tests ===
coverage/
phpunit-result.cache

# === Git ===
.git/
