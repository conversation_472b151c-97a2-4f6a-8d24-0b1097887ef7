<script src="<?php echo base_url('backend/dist/js/moment.min.js'); ?>"></script>

<?php if (!$is_exam_result): ?>
    <div class="alert alert-danger">
        <?php echo $this->lang->line('exam_result_disable_please_contact_to_administrator'); ?>
    </div>
    <?php return; ?>
<?php endif; ?>

<div class="row" style="margin: 15px;">
    <div class=" p-4 shadow-sm">
        <div class="row justify-content-center align-items-center pt-3">
            <div class="col-md-12">
                <h3 class="entered mt-0 text-center"><?php echo $this->lang->line('exam_result'); ?></h3>
            </div>
        </div>

        <!-- Search Form -->
        <form id="form1" class="pt-3" action="<?php echo current_url(); ?>" method="post" enctype="multipart/form-data">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('admission_no'); ?></label><small class="req"> *</small>
                        <input type="text" class="form-control" value="<?php echo set_value('admission_no'); ?>" id="admission_no" name="admission_no">
                        <span class="text-danger"><?php echo form_error('admission_no'); ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('exam'); ?></label><small class="req"> *</small>
                        <select id="exam_id" name="exam_id" class="form-control">
                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                        </select>
                        <span class="text-danger"><?php echo form_error('exam_id'); ?></span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <button type="submit" class="btn btn-success mt-4" name="search" id="search_btn">
                            <?php echo $this->lang->line('search'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <?php if ($this->session->flashdata('msg')): ?>
            <div class="alert alert-info"><?php echo $this->session->flashdata('msg'); ?></div>
        <?php endif; ?>

        <?php if (isset($_POST['search']) && !empty($exam_result)): ?>
            <?php foreach ($exam_result as $examresult): //var_dump($examresult);  

               $exam = $examresult->exam_result['exam'];
               ?>
                <div class="card mt-4 p-3">
                    <h5 class="text-center font-weight-bold"><?php echo $examresult->exam; ?></h5>
                    <div class="print" >
                       <button style="float:right;" class="btn btn-info btn-sm printSelected pull-right" type="submit" name="generate" title="generate multiple certificate" id="printMarksheet"  autocomplete="off"> Print Marksheet </button>
                    </div>
                    <table class="table table-bordered mt-3">
                        <tr>
                            <td>
                                <strong><?php echo $this->lang->line('name_prefix'); ?>:</strong>
                                <span class="font-weight-bold">
                                    <?php echo $this->customlib->getFullName(
                                        $examresult->exam_result->student->firstname ?? '',
                                        $examresult->exam_result->student->middlename ?? '',
                                        $examresult->exam_result->student->lastname ?? '',
                                        $sch_setting->middlename ?? '',
                                        $sch_setting->lastname ?? ''
                                    ); ?>
                                </span>
                            </td>
                            <td>
                                <strong><?php echo $this->lang->line('class'); ?>:</strong>
                                <span class="font-weight-bold">
                                    <?php echo $examresult->exam_result->student->class ?? ''; ?> 
                                    (<?php echo $examresult->exam_result->student->section ?? ''; ?>)
                                </span>
                            </td>
                            <td rowspan="3" class="text-center">
                                <?php
                                if ($template[0]->is_photo) {
                                    $studentImage = $examresult->exam_result->student->image ?? '';
                                    if (!empty($studentImage)) {
                                        echo '<img src="' . $this->media_storage->getImageURL($studentImage) . '" width="90" height="100">';
                                    } else {
                                        $default_image = ($examresult->exam_result->student->gender ?? 'Male') === 'Female' ? 
                                            "uploads/student_images/default_female.jpg" : 
                                            "uploads/student_images/default_male.jpg";
                                        echo '<img src="' . $this->media_storage->getImageURL($default_image) . '" width="90" height="100">';
                                    }
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <?php if ($template[0]->is_father_name): ?>
                                    <strong>Father's Name:</strong>
                                    <span class="font-weight-bold">
                                        <?php echo $examresult->exam_result->student->father_name ?? 'N/A'; ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($template[0]->is_mother_name): ?>
                                    <strong>Mother's Name:</strong>
                                    <span class="font-weight-bold">
                                        <?php echo $examresult->exam_result->student->mother_name ?? 'N/A'; ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <strong>Roll No / ADM No.:</strong>
                                <span class="font-weight-bold">
                                    <?php echo $examresult->exam_result->student->student_roll_no ?? ''; ?> /
                                    <?php echo $examresult->exam_result->student->admission_prefix ?? ''; ?>
                                    <?php echo $examresult->exam_result->student->admission_no ?? ''; ?>
                                </span>
                            </td>
                            <td>
                                <strong>DOB:</strong>
                                <span class="font-weight-bold">
                                    <?php echo $this->customlib->dateformat($examresult->exam_result->student->dob ?? ''); ?>
                                </span>
                            </td>
                        </tr>

                    </table>

                     <table  class="table table-bordered mt-3" width="96%" cellpadding="0" cellspacing="0" class="denifittable marks" style="text-align: center; text-transform: uppercase; margin: 0 auto;">
                        <col width="100"><col width="100"><col width="100">
                        <tbody>
                            <tr style="font-size:10px; font-weight: 400;">
                                <th><?php echo $this->lang->line('subjects'); ?></th> 
                                    <th style="width:120px"> <?php echo $this->lang->line('max_marks'); ?> </th>
                                    <?php if (!empty($query) && $query->num_rows() != 0): ?>
                                        <?php foreach ($rows as $value): ?>
                                            <th style="width:120px"> <?php echo $value['name']; ?> </th>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    <th style="width:120px"> Mark Obtained </th>
                                    <th style="width:55px"> <?php echo $this->lang->line('grade'); ?> </th>
                                
                                <th style="text-align: left; border-right:1px solid #999; max-width:50px; width:15%">
                                    <?php echo $this->lang->line('note'); ?>
                                </th>
                            </tr>
                            <?php
                            $total_max_marks = $total_obtain_marks = $total_points = $total_hours = $total_quality_point = 0;
                            $grade_type = 0;
                            if (!empty($examresult->exam_result['result'])) {
                                foreach ($examresult->exam_result['result'] as $exam_result) {
                                    if ($exam_result->exam_type == "number") {
                                        $total_max_marks += $exam_result->max_marks;
                                        $total_obtain_marks += $exam_result->get_marks;
                            ?>
                            <tr>
                                <td class="subject_name"> <?php echo $exam_result->name; ?> </td>
                                <?php if ('gpaa' != "gpa") { ?>
                                    <td class="subject_name"> <?php echo $exam_result->max_marks; ?> </td>
                                    
                                    <td class="subject_name">
                                        <?php echo $exam_result->get_marks; ?>
                                        <?php if ($exam_result->attendence == "absent") {
                                            echo "&nbsp;" . $this->lang->line('exam_absent');
                                        }
                                        if ($exam_result->get_marks < $exam_result->min_marks) {
                                            $result_status = 0;
                                        } ?>
                                    </td>
                                    <td class="subject_name" style="border-right:0">
                                        <?php
                                        $percentage_grade = ($exam_result->get_marks * 100) / $exam_result->max_marks;
                                        echo findGrade($exam_grades, $percentage_grade);
                                        ?>
                                    </td>
                                <?php } else { ?>
                                    <td class="subject_name">
                                        <?php
                                        $percentage_grade = ($exam_result->get_marks * 100) / $exam_result->max_marks;
                                        $point = findGradePoints($exam_grades, $percentage_grade);
                                        $total_points += $point;
                                        echo $point;
                                        ?>
                                    </td>
                                    <td class="subject_name">
                                        <?php $total_hours += $exam_result->credit_hours; echo $exam_result->credit_hours; ?>
                                    </td>
                                    <td class="subject_name">
                                        <?php
                                        echo ($exam_result->credit_hours * $point);
                                        $total_quality_point += ($exam_result->credit_hours * $point);
                                        ?>
                                    </td>
                                <?php } ?>
                                <td class="subject_name" style="text-align: left; border-right:1px solid #999">
                                    <?php 
                                    if ($exam_result->get_marks < $exam_result->min_marks) {
                                        $result_status = 0;
                                        echo "FAIL";
                                    } elseif (!empty($exam_result->note)) {
                                        echo $exam_result->note;
                                    } else {
                                        echo findGradeDescription($exam_grades, $percentage_grade);
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php } else { $grade_type ++; } } } ?>
                        </tbody>
                     </table>
                     <table class="table-bordered denifittable_marks"   cellpadding="0" cellspacing="0" width="100%"  style="text-align: center;margin-top: 20px; text-transform: uppercase;" >
                                    <tr class="overall_mark_details">
                                            <td style="font-size:14px">
                                                 Total Marks   
                                            </td>
                                            <td><b><?php echo $total_max_marks; ?></b></td>
                                            <td style="" class="text-center">
                                                Percentage 
                                            </td>
                                            <td> 
                                                    <?php
                                                         $percentage_grade = ($total_obtain_marks * 100) / $total_max_marks;
                                                    
                                                        echo round($percentage_grade, 2).'%'; 
                                                    
                                                    
                                                     findGrade($exam_grades, $percentage_grade); ?>
                                            </td>
                                        </tr>
                                        <tr class="overall_mark_details">
                                            <td class="text-center" >
                                                <?php echo $this->lang->line('marks_obtained') ?>
                                            </td>
                                            <td><b><?php echo number_format($total_obtain_marks, 0, '.', ''); ?></b></td>
                                            
                                            <td>
                                                Rank
                                            </td>
                                            <td class="text-center" colspan="2">
                                                <?php echo $examresult->exam_result['result'][0]->rank ?>
                                                    
                                            </td>
                                        </tr>
                                </table>
                                <?php if($grade_type = '0') : ?>
                                    <table class="table table-bordered  font-size: 14px;">
                                            <tr>
                                                <th colspan="" style="background: #bdd7ff;font-size: 10px;">
                                                    CO-SCHOLASTIC : (3 POINT GRDADING SCALE A , B , C)
                                                </th> 
                                                <th>GRADE</th>
                                                <th colspan="" style="background: #bdd7ff;font-size: 10px;">
                                                    CO-SCHOLASTIC : (3 POINT GRDADING SCALE A , B , C)
                                                </th> 
                                                <th>GRADE</th>
                                            </tr>
                                            
                                            <?php 
                                                $i = 1;
                                                foreach ($student_value['exam_result'] as $exam_result_key => $exam_result_value)  : 
                                                if ($exam_result_value->exam_type != "number") : 
                                                 
                                                if($i % 2 == 1) :
                                                    echo "<tr>"; 
                                                endif;
                                                    
                                                    
                                            ?>
                                            
                                                
                                                
                                                    <td>
                                                        <?php echo $exam_result_value->name; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo $exam_result_value->grade; ?>
                                                    </td>
                                                
                                                    
                                                
                                                
                                            <?php
                                            
                                                if($i % 2 == 0) :
                                                    echo "</tr>";
                                                endif;
                                                
                                               $i++; endif; endforeach; 
                                            ?>
                                            
                                            
                                    </table>  
                                <?php endif; ?> 
                                <table class="table table-bordered" valign="top" cellpadding="0" cellspacing="0" style="margin-top: 20px;">
                                    <tr>
                                        <td width="20%" style="margin:0 auto;vertical-align: middle;" rowspan="2">
                                            <div style="font-size:20px;font-weight: 600;text-transform: uppercase; border-bottom: 1px solid;width: 200px;text-align: center;">DATE</div>
                                            <div style="width:200px;text-align: center;"><?php echo date('d-m-Y') ?></div>
                                        </td>
                                        <td style="height:45px" valign="bottom">
                                            <?php
                                             if ($template[0]->left_sign != "") {
                                                ?>
                                                 
                                            <?php } ?>
                                        </td>
                                        <td style="height:45px" valign="bottom">
                                            <?php
                                             if ($template[0]->middle_sign != "") {
                                                ?>
                                                 <img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template[0]->middle_sign); ?>"  width="200" height="50">
                                            <?php } ?>
                                        </td>
                                        <?php if ($template[0]->right_sign != "") { ?>
                                        <td style="height:45px" valign="bottom">
                                            <img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template[0]->right_sign); ?>"  width="200" height="50">
                                            
                                        </td>
                                        <?php } ?>
                                    </tr>
                                    <tr>
                                        <td style="font-size:12px">
                                            SIGNATURE OF CLASS TEACHER
                                        </td>
                                        <td style="font-size:12px">
                                            SIGNATURE OF PRINCIPAL
                                        </td>
                                        <?php if ($template[0]->right_sign != "") { ?>
                                        <td style="font-size:12px">
                                            EXAM CONTROLLER
                                        </td>
                                        <?php } ?>
                                    </tr>
                                    
                                    <?php
                                    if ($template[0]->content_footer != "") {
                                        ?>
                                        <tr>
                                           <td valign="bottom" style="font-size: 12px;">
                                               <?php echo $template[0]->content_footer ?>
                                           </td>
                                       </tr>
                                       <?php
                                    }
                                    ?>  
                                </table>
                                <table class="table table-bordered" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <th class="mark_range">Mark Range In (%) </th>
                                        <?php foreach ($exam_grades as $grade):
                                            ?>
                                            <td style="font-size:11px"><?php echo $grade->mark_upto .' - '. $grade->mark_from; ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <tr>

                                        <th class="mark_range">Grade</th>
                                        <?php foreach ($exam_grades as $grade):
                                            ?>
                                            <td style="font-size:14px"><?php echo $grade->name; ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                </table>

                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>


<?php 
function findExamDivision( $marks_division, $percentage)
{  
   if (!empty($marks_division)) {
      foreach ($marks_division as $division_key => $division_value) {
         if ($division_value->percentage_from >= $percentage && $division_value->percentage_to <= $percentage) {
            return $division_value->name;
         }
      }
   }      

   return "";
}
?>

<script type="text/javascript">  

$(document).ready(function() {
    var admission_no = $("#admission_no").val().trim();

    if (admission_no !== "") { 
        $.ajax({
            url: '<?php echo base_url(); ?>welcome/getstudentexam',
            type: "POST",
            dataType: 'JSON',
            data: { admission_no: admission_no },
            success: function(res) {
                var div_data = "";
                var exam_id = "<?php echo $exam_id; ?>";

                $.each(res, function(i, obj) {
                    var sel = (exam_id && exam_id == obj.id) ? "selected" : "";
                    div_data += "<option value='" + obj.id + "' " + sel + ">" + obj.exam + "</option>";
                });

                $("#exam_id").html("<option value=''>Select</option>").append(div_data);
            }
        });
    }
});

$(document).on('keyup', '#admission_no', function(e) {
    var admission_no = $("#admission_no").val().trim();

    if (admission_no !== "") { 
        $.ajax({
            url: '<?php echo base_url(); ?>welcome/getstudentexam',
            type: "POST",
            dataType: 'JSON',
            data: { admission_no: admission_no },
            success: function(res) {
                var div_data = "";
                var exam_id = "<?php echo $exam_id; ?>";

                $.each(res, function(i, obj) {
                    var sel = (exam_id && exam_id == obj.id) ? "selected" : "";
                    div_data += "<option value='" + obj.id + "' " + sel + ">" + obj.exam + "</option>";
                });

                $("#exam_id").html("<option value=''>Select</option>").append(div_data);
            }
        });
    }
});


</script>
<script>
   function printDiv()
   {   
      $("#printbtn").css('display','none');     
      var printContents=document.getElementById('divtoprint').innerHTML;
      var originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents; 
      window.print();
      document.body.innerHTML = originalContents;
      location.reload(true);
   }
</script>
<?php
function findGradePoints($exam_grade, $exam_type, $percentage)
{
   foreach ($exam_grade as $exam_grade_key => $exam_grade_value) {
      if ($exam_grade_value['exam_key'] == $exam_type) {
         if (!empty($exam_grade_value['exam_grade_values'])) {
            foreach ($exam_grade_value['exam_grade_values'] as $grade_key => $grade_value) {
               if ($grade_value->mark_from >= $percentage && $grade_value->mark_upto <= $percentage) {
                  return $grade_value->point;
               }
            }
         }
      }
   }
   return 0;
}

function findExamGrade($exam_grade, $exam_type, $percentage)
{
   foreach ($exam_grade as $exam_grade_key => $exam_grade_value) {
      if ($exam_grade_value['exam_key'] == $exam_type) {

         if (!empty($exam_grade_value['exam_grade_values'])) {
           foreach ($exam_grade_value['exam_grade_values'] as $grade_key => $grade_value) {
             if ($grade_value->mark_from >= $percentage && $grade_value->mark_upto <= $percentage) {
               return $grade_value->name;
            }
         }
      }
   }
}
return "";
}



function getConsolidateRatio($exam_connection_list, $examid, $get_marks,$exam_get_percentage)
{

  if (!empty($exam_connection_list)) {
    foreach ($exam_connection_list as $exam_connection_key => $exam_connection_value) {

      if ($exam_connection_value->exam_group_class_batch_exams_id == $examid) {
         return ['exam_weightage'=>$exam_connection_value->exam_weightage,
         'exam_consolidate_marks'=>($get_marks * $exam_connection_value->exam_weightage) / 100,
         'exam_consolidate_percentage'=> ($exam_get_percentage * $exam_connection_value->exam_weightage) / 100];
      }
   }
}
return 0;
}

function getCalculatedExamGradePoints($array, $exam_id, $exam_grade, $exam_type)
{
  $object              = new stdClass();
  $return_total_points = 0;
  $return_total_exams  = 0;
  $return_max_marks  = 0;
  $return_quality_point  = 0;
  $return_get_marks  = 0;
  $return_credit_hours  = 0;
  if (!empty($array)) {

    if (!empty($array['exam_result_' . $exam_id])) {

      foreach ($array['exam_result_' . $exam_id] as $exam_key => $exam_value) {
        $return_total_exams++;
        $percentage_grade    = ($exam_value->get_marks * 100) / $exam_value->max_marks;
        $point               = findGradePoints($exam_grade, $exam_type, $percentage_grade);
        $return_total_points = $return_total_points + $point;
        $return_quality_point += ($point*$exam_value->credit_hours);
        $return_credit_hours += $exam_value->credit_hours;
        $return_max_marks += $exam_value->max_marks;
        $return_get_marks += $exam_value->get_marks;
     }
  }
}

$object->total_max_marks = $return_max_marks;
$object->total_get_marks = $return_get_marks;
$object->total_points = $return_total_points;
$object->total_exams  = $return_total_exams;
$object->return_quality_point  = $return_quality_point;
$object->return_credit_hours  = $return_credit_hours;

return $object;
}

function getCalculatedExam($array, $exam_id)
{

   $object              = new stdClass();
   $return_max_marks    = 0;
   $return_get_marks    = 0;
   $return_credit_hours = 0;
   $return_exam_status  = false;
   if (!empty($array)) {
      $return_exam_status = 'pass';
      if (!empty($array['exam_result_' . $exam_id])) {
         foreach ($array['exam_result_' . $exam_id] as $exam_key => $exam_value) {

            if ($exam_value->get_marks < $exam_value->min_marks || $exam_value->attendence != "present") {
               $return_exam_status = "fail";
            }

            $return_max_marks    = $return_max_marks + ($exam_value->max_marks);
            $return_get_marks    = $return_get_marks + ($exam_value->get_marks);
            $return_credit_hours = $return_credit_hours + ($exam_value->credit_hours);
         }
      }
   }
   $object->credit_hours = $return_credit_hours;
   $object->get_marks    = $return_get_marks;
   $object->max_marks    = $return_max_marks;
   $object->exam_status  = $return_exam_status;
   return $object;
}
?>
<script>

$(document).on('click', '#printMarksheet', function (e) { 
    var subsubmit_button = $(this);
    var formdata = []; // Initialize an empty array for form data

    // Manually adding required POST variables
    formdata.push(
        { name: 'marksheet_template', value: <?php echo $template[0]->id; ?> },
        { name: 'post_exam_id', value: <?php echo $examresult->exam_group_class_batch_exam_id; ?> },
        { name: 'post_exam_group_id', value: <?php echo $examresult->exam_group_id; ?> },
        { name: 'exam_group_class_batch_exam_student_id[]', value: <?php echo $examresult->id; ?> } // Fixed Student ID
    );

    $.ajax({
        type: "POST",
        url: "<?php echo base_url() ?>admin/examresult/printmarksheet",
        data: $.param(formdata), // Serialize the array for proper transmission
        beforeSend: function () {
            subsubmit_button.prop('disabled', true).text('Loading...');
        },
        success: function (response) {
            Popup(response);
        },
        error: function () {
            alert("Error Occurred. Please Try Again.");
        },
        complete: function () {
            subsubmit_button.prop('disabled', false).text('Print Marksheet');
        }
    });
});



</script>
<script type="text/javascript">
   var base_url = '<?php echo base_url() ?>'; 
    function Popup(data, winload = false)
    {
        var frameDoc=window.open('', 'Print-Window');
        frameDoc.document.open();
        //Create a new HTML document.
        frameDoc.document.write('<html>');
        frameDoc.document.write('<head>');
        frameDoc.document.write('<title></title>');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/bootstrap/css/bootstrap.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/font-awesome.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/ionicons.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/AdminLTE.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/dist/css/skins/_all-skins.min.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/iCheck/flat/blue.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/morris/morris.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/jvectormap/jquery-jvectormap-1.2.2.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/datepicker/datepicker3.css">');
        frameDoc.document.write('<link rel="stylesheet" href="' + base_url + 'backend/plugins/daterangepicker/daterangepicker-bs3.css">');
        frameDoc.document.write('</head>');
        frameDoc.document.write('<body onload="window.print()">');
        frameDoc.document.write(data);
        frameDoc.document.write('</body>');
        frameDoc.document.write('</html>');
        frameDoc.document.close();
        

        return true;
    }
</script>