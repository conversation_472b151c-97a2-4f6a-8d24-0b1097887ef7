

        <!-- Preloader Start -->
        <!-- <div id="preloader" class="preloader">
            <div class="animation-preloader">
                <div class="spinner">                
                </div>
                <div class="txt-loading">
                    <span data-text-preloader="K" class="letters-loading">
                        K
                    </span>
                    <span data-text-preloader="I" class="letters-loading">
                        I
                    </span>
                    <span data-text-preloader="D" class="letters-loading">
                        D
                    </span>
                    <span data-text-preloader="S" class="letters-loading">
                        S
                    </span>
                    <span data-text-preloader="A" class="letters-loading">
                        A
                    </span>
                </div>
                <p class="text-center">Loading</p>
            </div>
            <div class="loader">
                <div class="row">
                    <div class="col-3 loader-section section-left">
                        <div class="bg"></div>
                    </div>
                    <div class="col-3 loader-section section-left">
                        <div class="bg"></div>
                    </div>
                    <div class="col-3 loader-section section-right">
                        <div class="bg"></div>
                    </div>
                    <div class="col-3 loader-section section-right">
                        <div class="bg"></div>
                    </div>
                </div>
            </div>
        </div> -->
        <!-- Offcanvas Area Start -->
<style>
.datenews { 
    display: inline-block;
    font-weight: 600;
    color: #FFEB3B;
}
</style>
    <div class="fix-area">
        <div class="offcanvas__info">
            <div class="offcanvas__wrapper">
                <div class="offcanvas__content">
                    <div class="offcanvas__top mb-5 d-flex justify-content-between align-items-center">
                        <div class="offcanvas__logo">
                            <a href="index.html">
                                <img src="<?php echo base_url($front_setting->logo); ?>" alt="logo-img">
                            </a>
                        </div>
                        <div class="offcanvas__close">
                            <button>
                            <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <p class="text d-none d-xl-block">
                        Nullam dignissim, ante scelerisque the  is euismod fermentum odio sem semper the is erat, a feugiat leo urna eget eros. Duis Aenean a imperdiet risus.
                    </p>
                    <div class="mobile-menu fix mb-3"></div>
                    <div class="offcanvas__contact">
                        <h4>Contact Info</h4>
                        <ul>
                            <li class="d-flex align-items-center">
                                <div class="offcanvas__contact-icon">
                                    <i class="fal fa-map-marker-alt"></i>
                                </div>
                                <div class="offcanvas__contact-text">
                                    <a target="_blank" href="#"><?php echo $school_setting->address; ?></a>
                                </div>
                            </li>
                            <li class="d-flex align-items-center">
                                <div class="offcanvas__contact-icon mr-15">
                                    <i class="fal fa-envelope"></i>
                                </div>
                                <div class="offcanvas__contact-text">
                                    <a href="mailto:<?php echo $school_setting->email; ?>"><?php echo $school_setting->email; ?></a>
                                </div>
                            </li>
                            <li class="d-flex align-items-center">
                                <div class="offcanvas__contact-icon mr-15">
                                    <i class="fal fa-clock"></i>
                                </div>
                                <div class="offcanvas__contact-text">
                                    <a target="_blank" href="#">Mod-friday, 09am -05pm</a>
                                </div>
                            </li>
                            <li class="d-flex align-items-center">
                                <div class="offcanvas__contact-icon mr-15">
                                    <i class="far fa-phone"></i>
                                </div>
                                <div class="offcanvas__contact-text">
                                    <a href="tel:<?php echo $school_setting->phone; ?>"><?php echo $school_setting->phone; ?></a>
                                </div>
                            </li>
                        </ul>
                        <div class="header-button mt-4">
                            <a href="<?php echo base_url('site/login') ?>" class="theme-btn">
                                <span>
                                    login
                                    <i class="fa-solid fa-arrow-right-long"></i>
                                </span>
                            </a>
                        </div>
                        <div class="social-icon d-flex align-items-center">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                            <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    <div class="offcanvas__overlay"></div>

    <div class="header-top-section" style="background-color: #68879d;">
        <div class="container-fluid">
            <div class="header-top-wrapper"> 
                <div class="header-button float-right" style="float:right;">
                    <a href="<?php echo base_url('mandatory_public_disclosure') ?>" class="btn btn-success btn-sm">
                        <span>
                            Mandatory Public Disclosure 
                        </span>
                    </a>
                    <?php if(!empty($online_admission_application_form)){ ?>
                    <a href="<?php echo base_url('uploads/admission_form/'.$online_admission_application_form) ?>" class="btn btn-success btn-sm">
                        <span>
                            Online Admission Form
                        </span>
                    </a>
                    <?php } ?>
                </div>
                <div class="header-button float-right">
                    <?php
                    if (in_array('news', json_decode($front_setting->sidebar_options))) {
                    ?> 
                    <div class="newscontent">
                        <marquee behavior="scroll" direction="left" onmouseover="this.stop();" onmouseout="this.start();">
                            <ul style="display: inline; padding-left: 0; margin: 0;">
                                <?php
                                if (!empty($banner_notices)) {
                                    foreach ($banner_notices as $banner_notice_key => $banner_notice_value) {
                                ?>
                                <li style="display: inline; list-style: none; margin-right: 20px;">
                                    <a href="<?php echo site_url('read/' . $banner_notice_value['slug']) ?>">
                                        <span class="datenews">
                                            <?php
                                            echo date('d', strtotime($banner_notice_value['date'])) . " " . $this->lang->line(strtolower(date('F', strtotime($banner_notice_value['date'])))) . " " . date('Y', strtotime($banner_notice_value['date']));
                                            ?> 
                                        </span>
                                        : <?php echo $banner_notice_value['title']; ?>
                                    </a>
                                </li>
                                <?php
                                    }
                                }
                                ?>
                            </ul>
                        </marquee>
                    </div><!-- ./newscontent -->
                    <?php
                    }
                    ?>

                </div> 
            </div>

        </div>
    </div>
 
        <!-- Header Section Start -->
    <header id="header-sticky" class="header-1">
        <div class="container-fluid">
            <div class="mega-menu-wrapper">
                <div class="header-main style-2">
                    <div class="header-left">
                        <div class="logo">
                            <a href="<?php echo base_url(); ?>" class="header-logo">
                                <img src="<?php echo base_url($front_setting->logo); ?>" alt="logo-img" style=" width: 200px; ">
                            </a>
                        </div> 
                    </div>
                    <div class="header-right d-flex justify-content-end align-items-center">
                        <div class="mean__menu-wrapper">
                            <div class="main-menu">
                                <nav id="mobile-menu">
                                    <ul>
                                        <?php
                                            foreach ($main_menus as $menu_key => $menu_value) {
                                                $submenus          = false;
                                                $cls_menu_dropdown = "";
                                                $menu_selected     = "";
                                                if ($menu_value['page_slug'] == $active_menu) {
                                                    $menu_selected = "active";
                                                }
                                                if (!empty($menu_value['submenus'])) {
                                                    $submenus          = true;
                                                    $cls_menu_dropdown = "dropdown";
                                                }
                                                ?>

                                                <li class="<?php echo $menu_selected . " " . $cls_menu_dropdown; ?>" >
                                                    <?php
                                                    if (!$submenus) {
                                                        $top_new_tab = '';
                                                        $url         = '#';
                                                        if ($menu_value['open_new_tab']) {
                                                            $top_new_tab = "target='_blank'";
                                                        }
                                                        if ($menu_value['ext_url']) {
                                                            $url = $menu_value['ext_url_link'];
                                                        } else {
                                                            $url = site_url($menu_value['page_url']);
                                                        }
                                                        ?>
                                                        <a href="<?php echo $url; ?>" <?php echo $top_new_tab; ?>><?php echo $menu_value['menu']; ?></a>

                                                        <?php
                                                    } else {
                                                        $child_new_tab = '';
                                                        $url           = '#';
                                                        ?>
                                                        <a href="#" class="dropdown-toggle" data-toggle="dropdown"><?php echo $menu_value['menu']; ?> <b class="caret"></b></a>
                                                        <ul class="submenu">
                                                            <?php
                                                            foreach ($menu_value['submenus'] as $submenu_key => $submenu_value) {
                                                                if ($submenu_value['open_new_tab']) {
                                                                    $child_new_tab = "target='_blank'";
                                                                }
                                                                if ($submenu_value['ext_url']) {
                                                                    $url = $submenu_value['ext_url_link'];
                                                                } else {
                                                                    $url = site_url($submenu_value['page_url']);
                                                                }
                                                                ?>
                                                                <li><a href="<?php echo $url; ?>" <?php echo $child_new_tab; ?> ><?php echo $submenu_value['menu'] ?></a></li>
                                                                <?php
                                                            }
                                                            ?>
                                                        </ul>
                                                        <?php
                                                    }
                                                    ?>
                                                </li>
                                                <?php
                                            }    
                                        ?> 
                                    </ul>
                                </nav>
                            </div>
                        </div> 
                        <div class="header-button">
                            <a href="<?php echo base_url('site/login') ?>" class="theme-btn">
                                <span>
                                    login
                                    <i class="fa-solid fa-arrow-right-long"></i>
                                </span>
                            </a>
                        </div>
                        <div class="header__hamburger d-xl-none my-auto">
                            <div class="sidebar__toggle">
                                <i class="fas fa-bars"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
        