<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Edulearn Education - Education HTML5 Template</title>
<meta name="description" content="">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<link href="assets/css/bootstrap.css" rel="stylesheet">
<link href="assets/js/google-code-prettify/prettify.css" rel="stylesheet">
<link href="assets/css/bootstrap-responsive.css" rel="stylesheet">
<link href="assets/css/documenter_style.css" rel="stylesheet">
<link rel="shortcut icon" href="assets/img/favicon.png" type="image/x-icon">
<!-- Le HTML5 shim, for IE6-8 support of HTML5 elements -->
<!--[if lt IE 9]>
      <script src="//html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
<style>
html, body {
	background: #000000 ;
	color: #ffffff;
	font-size: 16px;
	background-repeat: no-repeat;
	background-attachment: fixed;
	width: 100%;
	height: auto;
}
h1, h2, h3, h4, h5, h6 {
	color: #ffffff;
}
section table {
	background-color: #FFFFFF;
}
pre {
	padding: 20px 0 10px!important;
}
::-moz-selection {
background:#444444;
color:#DDDDDD;
}
::selection {
	background: #444444;
	color: #DDDDDD;
}
.hero-unit a {
	color: #000000;
}
a.brand {
	background-image: url(assets/images/image_1.png);
}
a {
	color: #ffffff;
}
#thanks h2 {
	color: #ffffff;
}
 a:hover, a:active {
	color: #8ec93e;
}
hr {
	border-top: 1px solid #EBEBEB;
	border-bottom: 1px solid #FFFFFF;
}
div.navbar-inner, .navbar .nav li ul {
	background: #DDDDDD;
	color: #222222;
}
a.btn-navbar {
	background: #DDDDDD;
	color: #222222;
}
.navbar .nav li a {
	color: #222222;
	text-shadow: 1px 1px 0px #EEEEEE;
}
.navbar .nav li a:hover, .navbar .nav li.active a {
	text-shadow: none;
}
div.navbar-inner ul {
}
.navbar .nav > li a {
	color: #444444;
}
.navbar .nav > li a:hover, a.btn-navbar:hover {
	background: #444444;
	color: #DDDDDD;
}
.navbar .nav .active > a, .navbar .nav .active > a:hover, a.btn-navbar:active {
	background-color: #444444;
	color: #DDDDDD;
}
.navbar .nav li ul li a:hover {
	background: #444444;
	color: #8ec93e;
}
.navbar .nav li ul li a:active {
	background: #444444;
	color: #DDDDDD;
}
.btn-primary {
	background-image: -moz-linear-gradient(top, #0088CC, #0044CC);
	background-image: -ms-linear-gradient(top, #0088CC, #0044CC);
 background-image: -webkit-gradient(linear, 0 0, 0 0088CC%, from(#DDDDDD), to(#0044CC));
	background-image: -webkit-linear-gradient(top, #0088CC, #0044CC);
	background-image: -o-linear-gradient(top, #0088CC, #0044CC);
	background-image: linear-gradient(top, #0088CC, #0044CC);
 filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#0088CC', endColorstr='#0044CC', GradientType=0);
	border-color: #0044CC #0044CC #bfbfbf;
	color: #FFFFFF;
}
.btn-primary:hover,  .btn-primary:active,  .btn-primary.active,  .btn-primary.disabled,  .btn-primary[disabled] {
	background-color: #0044CC;
}
#documenter_copyright {
	display: block !important;
	visibility: visible !important;
}
#theme_options2 img {
  border: 1px solid #eee;
  margin: 10px 0;
  max-width: 100%;
  padding: 10px;
}
#intro .highlight {
color: #333333;
}
#images a{
	font-size: 16px;
	color: #333333;
}
#images a:hover{
	color: #8ec93e;
}
#documenter_content {
	padding-top: 100px;
}
.section {
	margin-top: 30px!important;
}
..masthead h1 {
	line-height: 24px!important;
}
</style>
</head>
<body class="documenter-project-Reale Aste-" data-spy="scroll" id="top">

<!-- Documentation Navbar -->
<div class="navbar navbar-fixed-top">
  <div class="navbar-inner">
    <div class="container"> <a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"> <span class="icon-bar"></span> <span class="icon-bar"></span> <span class="icon-bar"></span> </a> <a class="brand" href="#">Edulearn Education - Education HTML5 Template</a>
      <div class="nav-collapse">
        <ul class="nav">
          <li><a href="#theme_installation" title="Theme Installation">Template Installation</a></li> 
		  <li><a href="#reading_setting" title="Page Included in Nimis">Pages</a></li>
		  <li><a href="#fonts" title="Fonts">Font</a></li>
		  <li><a href="#theme_options" title="Source File (CSS)">Source File (CSS)</a></li>
		  <li><a href="#theme_options1" title="Source File (JS)">Source File (JS)</a></li>   
		  <li><a href="#theme_options2" title="Customize">Customization</a></li> 		  
          <li><a href="#thanks" title="Thanks">Thanks</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>
<!-- End of Navbar --> 

<!-- Main Container Beginning -->
<div class="container" id="documenter_content">
 <div id="documenter-cover">
    <div class="masthead">
      <h1 style="text-align: center">Edulearn Education - Education HTML Template</h1>
      <p style="text-align: center">Created: 10/04/2018</p>
      <p class="download-info">  <a href="https://themeforest.net/user/rs-theme/" class="btn btn-large">24/7 Support: Send Us a message from our profile</a> </p>
    </div>
    <!-- masthead -->
    <div id="intro">
      <p class="highlight hero-unit">Thank you for purchasing our template. If you have any questions that are beyond the scope of this help file, please feel free to message us</p>
    </div>
    <!-- intro --> 
 </div>
 <section id="theme_installation">
    <div class="page-header">
      <h3>Template Installation</h3>
      <hr class="notop">
    </div>
		<ol>
			<li>
				<h5>FTP Upload:</h5>
				<ul>
					<li>Open up your FTP manager and connect to your hosting</li>
					<li>Browse to required directory (Normally public_html)</li>
					<li>Upload the files inside <strong>Education</strong> folder.</li>
				</ul>
			</li>
		</ol>
  </section>
 
  <section id="reading_setting">
    <div class="page-header">
      <h3>Pages</h3>
      <hr class="notop">
		<ol> 
			<ul>							
				<li><a href="#">Home</a>
					<ul>							
						<li><a href="#">Home One</a></li>
						<li><a href="#">Home Two</a></li>
						<li><a href="#">Home Three</a></li>
						<li><a href="#">Home Four</a></li>
					</ul>
				</li>	
				<li><a href="#">About us</a>
					<ul>
						<li><a href="#">About One</a></li>
						<li><a href="#">About Two</a></li>
						<li><a href="#">About Three</a></li>
					</ul>
				</li>		
				<li><a href="#">Pages</a>
					<ul>
					   <li>
						  <a href="#">Teachers</a>
						  <ul>
							  <li><a href="#">Teachers</a></li>
							  <li><a href="#">Teachers without filter</a></li>
							  <li><a href="#">Teachers Single</a></li>
						  </ul>
					   </li>
					   <li>
						  <a href="#">Gallery</a>
						  <ul>
							  <li><a href="#">Gallery</a></li>
							  <li><a href="#">Gallery Two</a></li>
							  <li><a href="#">Gallery Three</a></li>
						  </ul>
					   </li>
						<li>
						  <a href="#">Shop</a>
						  <ul>
							 <li><a href="#">Shop</a></li>
							 <li><a href="#">Shop Details</a></li>
						  </ul>
					   </li>
						<li><a href="#">Cart</a></li>
						<li><a href="#">Checkout</a></li>
						<li><a href="#">Error 404</a></li>
					</ul>
				</li>
				<li><a href="#">Courses</a>
					<ul>
						<li><a href="#">Courses One</a></li>
						<li><a href="#">Courses Two</a></li>
						<li><a href="#">Courses Details</a></li>
					</ul>
				</li>
				<li><a href="#">Events</a>
					<ul>
						<li><a href="#">Events</a></li>
						<li><a href="#">Events Details</a></li>
					</ul>
				</li>
				<li>
					<a href="#">Blog</a>
					<ul>
						<li><a href="#">Blog</a></li>
						<li><a href="#">Blog Details</a></li>
					</ul>
				</li>
				<li><a href="#">Contact</a></li>	
			</ul>	
		</ol>
    </div>
  </section>
  <section id="fonts">
    <div class="page-header">
      <h3>Font</h3>
      <hr class="notop">
    </div>
		<pre>
			@import url('https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700|Roboto+Condensed:400,500,600,700');
		</pre>
  </section>

   <section id="images">
    <div class="page-header">
      <h3>Images</h3>
      <hr class="notop">
    </div>
		<pre>
			<a href="https://photodune.net/">1. Photodune</a>
			<a href="http://allfreephotos.net">2. Allfreephotos</a>
			<a href="https://pixabay.com/">3. Pixabay</a>
			<a href="https://www.pexels.com/">4. Pexels</a>			
			<a href="http://unsplash.com/">5. Unsplash</a>	
			<a href="https://www.fotolia.com">6. Fotolia</a>
			<br />	
			Note: All images are just used for Preview Purpose Only. They are not part of the theme and NOT included in the final purchase files.
			
		</pre>
  </section>

  <section id="them
  e_options">
    <div class="page-header">
      <h3>Source File (CSS)</h3>
      <hr class="notop">
    </div>		
	<pre>
		&lt;!-- Bootstrap CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/bootstrap.min.css"&gt;

		&lt;!-- Font Awesome CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/font-awesome.min.css"&gt;

		&lt;!-- Flaticon CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="fonts/flaticon.css"&gt;

		&lt;!-- Main Menu CSS
		============================================ --&gt;    
		&lt;link href="css/rsmenu-main.css" rel="stylesheet"&gt;

		&lt;!-- Main Menu Effect CSS
		============================================ --&gt;    
		&lt;link href="css/rsmenu-transitions.css" rel="stylesheet"&gt;

		&lt;!-- Owl Carousel CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/owl.carousel.css"&gt;

		&lt;!-- Slick Slider CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/slick.css"&gt;		

		&lt;!-- Animate css
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/animate.css"&gt;
		
		&lt;!-- Magnific popup css
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/magnific-popup.css"&gt;

		&lt;!-- Main Style CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="style.css"&gt; 

		&lt;!-- responsive CSS
		============================================ --&gt;
		&lt;link rel="stylesheet" href="css/responsive.css"&gt;

	</pre>			
  </section>
  <section id="theme_options1">
    <div class="page-header">
      <h3>Source File (JS)</h3>
      <hr class="notop">
    </div>	
	<pre>
		
		&lt;!-- jquery js 
		============================================ --&gt;
		&lt;script src="js/jquery.min.js"&gt;&lt;/script&gt;  

		&lt;!-- menu js 
		============================================ --&gt;
		&lt;script src="js/rsmenu-main.js"&gt;&lt;/script&gt; 

		&lt;!-- bootstrap js 
		=========================================== --&gt;
		&lt;script src="js/bootstrap.min.js"&gt;&lt;/script&gt;  

		&lt;!-- owl carousel min js 
		=========================================== --&gt;
		&lt;script src="js/owl.carousel.js"&gt;&lt;/script&gt;		 		

		&lt;!-- slick js 
		=========================================== --&gt;   
		&lt;script src="js/slick.min.js"&gt;&lt;/script&gt;    

		&lt;!-- imagesloaded js 
		=========================================== --&gt;
		&lt;script src="js/imagesloaded.pkgd.min.js"&gt;&lt;/script&gt;  

		&lt;!-- wow min js 
		=========================================== --&gt;
		&lt;script src="js/wow.min.js"&gt;&lt;/script&gt;
		
		&lt;!-- counter top js 
		=========================================== --&gt;
		&lt;script src="js/waypoints.min.js"&gt;&lt;/script&gt;
		&lt;script src="js/jquery.counterup.min.js"&gt;&lt;/script&gt;

		&lt;!-- magnific popup js 
		=========================================== --&gt;    
		&lt;script src="js/jquery.magnific-popup.min.js" type="text/javascript"&gt;&lt;/script&gt;
		
		&lt;!-- isotope js 
		=========================================== --&gt;    
		&lt;script src="js/isotope.pkgd.min.js" type="text/javascript"&gt;&lt;/script&gt;

		&lt;!-- google map js
		=========================================== --&gt;    
		&lt;script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAgC6ZapXdUzFdeQOFhdm_wucwlDMMQ8CQ" type="text/javascript"&gt;&lt;/script&gt;
		
		&lt;!-- Main js 
		=========================================== --&gt;
		&lt;script src="js/main.js"&gt;&lt;/script&gt;
	</pre>	
  </section>
  
    <section id="theme_options2">
    <div class="page-header">
      <h3>Customization</h3>
      <hr class="notop">
    </div>
        <h4>How to Change Logo</h4>
        <img src="assets/img/logo.jpg" alt="">
		<h4>How to Change Menu Name</h4>
        <img src="assets/img/menu.jpg" alt="">
		<h4>How to Change Slider Images</h4>
        <img src="assets/img/slider.jpg" alt="">
		<h4>How Setup Youtube Video</h4>
        <img src="assets/img/youtube.jpg" alt="">
		<h4>How Setup Popular Courses </h4>
        <img src="assets/img/course.jpg" alt="">
		<h4>How Setup Events</h4>
        <img src="assets/img/event.jpg" alt="">
        <h4>How Setup Experienced Staffs</h4>
        <img src="assets/img/team.jpg" alt="">
		<h4>How Setup News </h4>
        <img src="assets/img/blog.jpg" alt="">
		
        <h4>How Setup MAP </h4>
		 You can select js/main.js form root folder. Then check below images for change latitude and longitude code
        <img src="assets/img/google.jpg" alt="">
        <h4>Where you put E-mail receiving address</h4>
        <p>You can select mailer.php form root folder. Then check below images for change recipient email address</p>
        <img src="assets/img/email.jpg" alt="">       
    </section>
  <section id="thanks">
    <div class="page-header">
      <h3>Thanks</h3>
      <hr class="notop">
    </div>
    <h4 id="thanks_span_style"margin_0px_padding_0px_border_0px_outline_0px_font_weight_700_font_style_inherit_font_family_inherit_vertical_align_baseline"once_again_thankyou_for_for_purchasing_one_of_our_theme_span"><span style="margin: 0px; padding: 0px; border: 0px; outline: 0px; font-weight: 700; font-style: inherit; font-family: inherit; vertical-align: baseline;">Once again thank you for  purchasing one of our Templates</span></h4>
    <h2 style="margin: 0px 0px 18px; padding: 0px; border: 0px; outline: 0px; font-weight: 100; font-size: 20px; font-family: Arial, verdana, arial, sans-serif; vertical-align: baseline; "> Best Regards</h2>
    <h3 style="margin: 18px 0px 0px; padding: 0px; border: 0px; outline: 0px; font-weight: 100; font-size: 26px; font-family: Arial, verdana, arial, sans-serif; vertical-align: baseline;"> RS Theme</h3>
  </section>
  <hr />
  <footer>
    <p>Copyright RS Theme 2018 made with the <a href="http://rstheme.com/">Documenter v1.0</a></p>
  </footer>
</div>
<!-- /container --> 

<!-- Le javascript
    ================================================== --> 
<!-- Placed at the end of the document so the pages load faster --> 
<script>document.createElement('section');var duration='500',easing='swing';</script> 
<script src="assets/js/jquery.js"></script> 
<script src="assets/js/jquery.scrollTo.js"></script> 
<script src="assets/js/jquery.easing.js"></script> 
<script src="assets/js/scripts.js"></script> 
<script src="assets/js/google-code-prettify/prettify.js"></script> 
<script src="assets/js/bootstrap-min.js"></script>
</body>
</html>
