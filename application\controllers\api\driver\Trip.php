<?php
if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}
require APPPATH . 'libraries/RestController.php';
use chris<PERSON><PERSON><PERSON><PERSON>\RestServer\RestController;

class Trip extends RestController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model(array("driver/address_model", "vehicle_model", "vehroute_model", "route_model", "trip_model"));
    }

    /**
     * Start a new bus trip
     * 
     * @param int $school_id School ID
     * @return json
     */
    public function start_trip_post($school_id)
    {
        $school_id = $this->session->userdata('school_id'); 
        $data = [
            'driver_id' => $this->input->post('driver_id'),
            'vechile_id' => $this->input->post('vechile_id'),
            'route_id' => $this->input->post('route_id'),
            'school_id' => $school_id,
            'trip_start_time' => date('Y-m-d H:i:s'),
            'status' => 'ongoing',
            'firebase_json_url' => null // Will be updated after insert with full URL
        ];
        
        // Validate required fields
        if (empty($data['driver_id']) || empty($data['vechile_id']) || empty($data['school_id']) || empty($data['route_id'])) {
            return $this->response([
                'status' => false,
                'message' => 'Missing required parameters. driver_id, vechile_id, and school_id are required.'
            ], 400);
        }
        
        $this->db->insert('bus_trips', $data);
        $trip_id = $this->db->insert_id();

        if ($trip_id) {
            // Get Firebase config and generate full URL
            $firebase_url = null;
            $firebase_path = 'trips/trip_' . $trip_id . '/' . $data['vechile_id'];

            // Try to get Firebase config for this school
            $this->db->where('school_id', $school_id);
            $firebase_config = $this->db->get('firebase_config')->row();

            if ($firebase_config) {
                $database_url = rtrim($firebase_config->database_url, '/');
                $firebase_url = $database_url . '/' . $firebase_path . '.json';
                $this->db->where('id', $trip_id);
                $this->db->update('bus_trips', ['firebase_json_url' => $firebase_url]);
            }

            return $this->response([
                'status' => 'success',
                'trip_id' => $trip_id,
                'firebase_path' => $firebase_path,
                'firebase_url' => $firebase_url
            ], 200);
        } else {
            return $this->response([
                'status' => 'error',
                'message' => 'Failed to start trip'
            ], 500);
        }
    }
    
    /**
     * End an ongoing bus trip
     * 
     * @return json
     */
    public function end_trip_post($school_id)
    { 
        $school_id = $this->session->userdata('school_id'); 
        $trip_id = $this->input->post('trip_id');
        
        if (empty($trip_id)) {
            return $this->response([
                'status' => false,
                'message' => 'Trip ID is required'
            ], 400);
        }
        
        $data = [
            'trip_end_time' => date('Y-m-d H:i:s'),
            'status' => 'completed'
        ];
        
        $this->db->where('id', $trip_id);
        $this->db->update('bus_trips', $data);
        
        if ($this->db->affected_rows() > 0) {
            return $this->response([
                'status' => 'success',
                'message' => 'Trip ended successfully'
            ], 200);
        } else {
            return $this->response([
                'status' => false,
                'message' => 'Failed to end trip or trip not found'
            ], 404);
        }
    }
    
    /**
     * Get trip details by ID
     * 
     * @return json
     */
    public function get_trip_details_post($school_id)
    {
        $school_id = $this->session->userdata('school_id');
        
        $trip_id = $this->input->post('trip_id');
        
        if (empty($trip_id)) {
            return $this->response([
                'status' => false,
                'message' => 'Trip ID is required'
            ], 400);
        }
        
        $this->db->select('*');
        $this->db->from('bus_trips');
        $this->db->where('id', $trip_id);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            return $this->response([
                'status' => 'success',
                'data' => $query->row_array()
            ], 200);
        } else {
            return $this->response([
                'status' => false,
                'message' => 'Trip not found'
            ], 404);
        }
    }
    
    /**
     * Get trips for a specific driver
     * 
     * @return json
     */
    public function get_driver_trips_post()
    {
        $driver_id = $this->input->post('driver_id');
        $school_id = $this->session->userdata('school_id');
        $status = $this->input->post('status'); // Optional: 'ongoing', 'completed', or null for all
        
        if (empty($driver_id) || empty($school_id)) {
            return $this->response([
                'status' => false,
                'message' => 'Driver ID and School ID are required'
            ], 400);
        }
        
        $this->db->select('bus_trips.*, v.vehicle_no, v.vehicle_model');
        $this->db->from('bus_trips');
        $this->db->where('bus_trips.driver_id', $driver_id);
        $this->db->where('bus_trips.school_id', $school_id);
        
        // Join with vehicle table to get vehicle details
        $this->db->join('vehicles v', 'v.id = bus_trips.vechile_id', 'left');
        
        // Filter by status if provided
        if (!empty($status)) {
            $this->db->where('bus_trips.status', $status);
        }
        
        // Order by most recent trips first
        $this->db->order_by('bus_trips.trip_start_time', 'DESC');
        
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            return $this->response([
                'status' => 'success',
                'data' => $query->result_array(),
                'total_trips' => $query->num_rows()
            ], 200);
        } else {
            return $this->response([
                'status' => 'success',
                'message' => 'No trips found for this driver',
                'data' => [],
                'total_trips' => 0
            ], 200);
        }
    }

    /**
     * Update vehicle location during trip (push to Firebase)
     *
     * @return json
     */
    public function update_location_post()
    {
        $school_id = $this->session->userdata('school_id');
        $trip_id = $this->input->post('trip_id');
        $vehicle_id = $this->input->post('vehicle_id');
        $latitude = $this->input->post('latitude');
        $longitude = $this->input->post('longitude');
        $speed = $this->input->post('speed');
        $timestamp = $this->input->post('timestamp') ?: time();

        // Validate required fields
        if (empty($trip_id) || empty($vehicle_id) || empty($latitude) || empty($longitude)) {
            return $this->response([
                'status' => false,
                'message' => 'Missing required parameters: trip_id, vehicle_id, latitude, longitude'
            ], 400);
        }

        // Get Firebase config
        $this->db->where('school_id', $school_id);
        $firebase_config = $this->db->get('firebase_config')->row();

        if (!$firebase_config) {
            return $this->response([
                'status' => false,
                'message' => 'Firebase not configured for this school'
            ], 400);
        }

        // Prepare location data
        $location_data = [
            'latitude' => floatval($latitude),
            'longitude' => floatval($longitude),
            'speed' => floatval($speed ?: 0),
            'timestamp' => intval($timestamp),
            'vehicle_id' => $vehicle_id,
            'trip_id' => $trip_id
        ];

        // Firebase path for this trip/vehicle
        $firebase_path = 'trips/trip_' . $trip_id . '/' . $vehicle_id;
        $database_url = rtrim($firebase_config->database_url, '/');

        // Use timestamp as key for push-like behavior
        $firebase_url = $database_url . '/' . $firebase_path . '/' . $timestamp . '.json';

        // Here you would normally push to Firebase using Firebase SDK
        // For now, we'll just return success with the data structure

        return $this->response([
            'status' => 'success',
            'message' => 'Location updated successfully',
            'firebase_path' => $firebase_path,
            'firebase_url' => $firebase_url,
            'location_data' => $location_data
        ], 200);
    }

    /**
     * Get route details with start/end points for a trip
     *
     * @return json
     */
    public function get_route_details_post()
    {
        $school_id = $this->session->userdata('school_id');
        $route_id = $this->input->post('route_id');

        if (empty($route_id)) {
            return $this->response([
                'status' => false,
                'message' => 'Route ID is required'
            ], 400);
        }

        $route_details = $this->trip_model->getRouteDetails($route_id);

        if (!$route_details) {
            return $this->response([
                'status' => false,
                'message' => 'Route not found'
            ], 404);
        }

        return $this->response([
            'status' => 'success',
            'route' => $route_details['route'],
            'pickup_points' => $route_details['pickup_points']
        ], 200);
    }
}
