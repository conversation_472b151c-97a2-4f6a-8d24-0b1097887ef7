<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Firebase_config_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function get($school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }
        
        $this->db->where('school_id', $school_id);
        $query = $this->db->get('firebase_config');
        return $query->row();
    }

    public function add($data)
    {
        $this->db->trans_start();
        
        // Check if config already exists for this school
        $existing = $this->get($data['school_id']);
        
        if ($existing) {
            // Update existing configuration
            $this->db->where('school_id', $data['school_id']);
            $this->db->update('firebase_config', $data);
            $message = "Firebase configuration updated for school ID " . $data['school_id'];
            $action = "Update";
            $record_id = $existing->id;
        } else {
            // Insert new configuration
            $this->db->insert('firebase_config', $data);
            $insert_id = $this->db->insert_id();
            $message = "Firebase configuration added for school ID " . $data['school_id'];
            $action = "Insert";
            $record_id = $insert_id;
        }
        
        $this->log($message, $record_id, $action);
        
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            return false;
        }
        
        return true;
    }

    public function remove($school_id)
    {
        $this->db->trans_start();
        
        $config = $this->get($school_id);
        if ($config) {
            $this->db->where('school_id', $school_id);
            $this->db->delete('firebase_config');
            
            $message = "Firebase configuration deleted for school ID " . $school_id;
            $action = "Delete";
            $record_id = $config->id;
            $this->log($message, $record_id, $action);
        }
        
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            return false;
        }
        
        return true;
    }

    public function test_connection($config_data)
    {
        // This method can be used to test Firebase connection
        // For now, we'll just validate that required fields are present
        $required_fields = ['project_name', 'api_key', 'database_url'];
        
        foreach ($required_fields as $field) {
            if (empty($config_data[$field])) {
                return [
                    'success' => false,
                    'message' => "Required field '{$field}' is missing"
                ];
            }
        }
        
        // Additional validation can be added here
        // For example, checking if the database URL is valid
        if (!filter_var($config_data['database_url'], FILTER_VALIDATE_URL)) {
            return [
                'success' => false,
                'message' => 'Invalid database URL format'
            ];
        }
        
        return [
            'success' => true,
            'message' => 'Configuration appears valid'
        ];
    }
}
