<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> <?php echo $this->lang->line('transport'); ?>
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-list"></i> <?php echo $this->lang->line('vehicle'); ?> <?php echo $this->lang->line('trips'); ?></h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo site_url('admin/vehicle'); ?>" class="btn btn-primary btn-sm"><i class="fa fa-arrow-left"></i> <?php echo $this->lang->line('back'); ?></a>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-striped table-bordered table-hover example">
                                        <thead>
                                            <tr>
                                                <th><?php echo $this->lang->line('trip_id'); ?></th>
                                                <th><?php echo $this->lang->line('driver'); ?></th>
                                                <th><?php echo $this->lang->line('start_time'); ?></th>
                                                <th><?php echo $this->lang->line('end_time'); ?></th>
                                                <th><?php echo $this->lang->line('status'); ?></th>
                                                <th class="text-right"><?php echo $this->lang->line('action'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            if (!empty($trips)) {
                                                foreach ($trips as $trip) {
                                                    ?>
                                                    <tr>
                                                        <td><?php echo $trip['id']; ?></td>
                                                        <td><?php echo $trip['driver_name'] . ' ' . $trip['driver_surname']; ?></td>
                                                        <td><?php echo date($this->customlib->getSchoolDateFormat(), strtotime($trip['trip_start_time'])); ?></td>
                                                        <td>
                                                            <?php 
                                                            if (!empty($trip['trip_end_time'])) {
                                                                echo date($this->customlib->getSchoolDateFormat(), strtotime($trip['trip_end_time']));
                                                            } else {
                                                                echo "-";
                                                            }
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <?php 
                                                            if ($trip['status'] == 'ongoing') {
                                                                echo '<span class="label label-success">Ongoing</span>';
                                                            } else {
                                                                echo '<span class="label label-default">Completed</span>';
                                                            }
                                                            ?>
                                                        </td>
                                                        <td class="text-right">
                                                            <a href="<?php echo site_url('admin/vehicle/tripdetails/' . $trip['id']); ?>" class="btn btn-default btn-xs" data-toggle="tooltip" title="<?php echo $this->lang->line('view'); ?>">
                                                                <i class="fa fa-eye"></i> <?php echo $this->lang->line('view'); ?>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                }
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>