<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Vehicle extends Admin_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->library('media_storage');
        $this->load->model('staff_model');
    }

    public function index()
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'vehicle/index');
        $data['title']       = 'Add Vehicle';
        $listVehicle         = $this->vehicle_model->get();
        $staff_roles         = $this->staff_model->getStaffRole();
        $driverroleId = null; 
        foreach ($staff_roles as $role) {
            if (trim($role['type']) === 'Driver') {
                $driverroleId = $role['id'];
                break;
            }
        }
        $driverlist          = $this->staff_model->getEmployeeByRoleID($driverroleId); 
        $data['driverlist']  = $driverlist;
        $data['listVehicle'] = $listVehicle;
        $this->load->view('layout/header');
        $this->load->view('admin/vehicle/index', $data);
        $this->load->view('layout/footer');
    }

    public function add()
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_add')) {
            access_denied();
        }
        $this->form_validation->set_rules('vehicle_no', $this->lang->line('vehicle_number'), 'trim|required|xss_clean');
        $this->form_validation->set_rules('vehicle_photo', $this->lang->line('vehicle_photo'), 'callback_handle_upload');
        
        if ($this->form_validation->run() == false) {
            $msg = array(
                'vehicle_no' => form_error('vehicle_no'),
                'vehicle_photo' => form_error('vehicle_photo'),
            );

            $array = array('status' => 'fail', 'error' => $msg, 'message' => '');
        } else {            
            
            $vehicle_photo = $this->media_storage->fileupload("vehicle_photo", "./uploads/vehicle_photo/");
            $staff_id  = $this->input->post('driver_id');
            $getemp = $this->staff_model->get($staff_id);  
            $data = array(
                'vehicle_no'           => $this->input->post('vehicle_no'),
                'vehicle_model'        => $this->input->post('vehicle_model'),
                'driver_id'            => $this->input->post('driver_id'),
                'driver_name'          => $getemp['name'].' '.$getemp['surname'],
                'driver_licence'       => $this->input->post('driver_licence'),
                'driver_contact'       => $getemp['contact_no'],
                'note'                 => $this->input->post('note'),
                'registration_number'  => $this->input->post('registration_number'),
                'chasis_number'        => $this->input->post('chasis_number'),
                'max_seating_capacity' => $this->input->post('max_seating_capacity'),
                'manufacture_year'      => $this->input->post('manufacture_year'),
                'vehicle_photo'        => $vehicle_photo,
                'school_id'     => $this->school_id,
            );
           
            $this->vehicle_model->add($data);

            $msg   = $this->lang->line('success_message');
            $array = array('status' => 'success', 'error' => '', 'message' => $msg);
        }
        echo json_encode($array);
    }

    public function getsinglevehicledata()
    {
        $vehicleid           = $this->input->post('vehicleid');
        $data['editvehicle'] = $this->vehicle_model->get($vehicleid);
        $staff_roles         = $this->staff_model->getStaffRole();
        $driverroleId = null; 
        foreach ($staff_roles as $role) {
            if (trim($role['type']) === 'Driver') {
                $driverroleId = $role['id'];
                break;
            }
        }
        $driverlist          = $this->staff_model->getEmployeeByRoleID($driverroleId); 
        $data['driverlist']  = $driverlist;
        $page                = $this->load->view('admin/vehicle/edit', $data, true);
        echo json_encode(array('page' => $page));
    }

    public function edit()
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_edit')) {
            access_denied();
        }
        
        $this->form_validation->set_rules('vehicle_no', $this->lang->line('vehicle_number'), 'trim|required|xss_clean');
        $this->form_validation->set_rules('vehicle_photo', $this->lang->line('vehicle_photo'), 'callback_handle_upload');
        $id =   $this->input->post('id');
        
        $vehicle              = $this->vehicle_model->get($id);       
        
        if ($this->form_validation->run() == false) {
            $msg = array(
                'vehicle_no' => form_error('vehicle_no'),
                'vehicle_photo' => form_error('vehicle_photo'),
            );

            $array = array('status' => 'fail', 'error' => $msg, 'message' => '');
        } else {           
            $staff_id   = $this->input->post('driver_name');
            $getemp     = $this->staff_model->get($staff_id); 
            $data = array(
                'id'                   => $this->input->post('id'),
                'vehicle_no'           => $this->input->post('vehicle_no'),
                'vehicle_model'        => $this->input->post('vehicle_model'),
                'driver_id'            => $this->input->post('driver_name'),
                'driver_name'          => $getemp['name'].' '.$getemp['surname'],
                'driver_licence'       => $this->input->post('driver_licence'),
                'driver_contact'       => $getemp['contact_no'],
                'driver_licence'       => $this->input->post('driver_licence'), 
                'note'                 => $this->input->post('note'),
                'registration_number'  => $this->input->post('registration_number'),
                'chasis_number'        => $this->input->post('chasis_number'),
                'max_seating_capacity' => $this->input->post('max_seating_capacity'),
                'manufacture_year'     => $this->input->post('manufacture_year'),        
                
            );            
            
            if (isset($_FILES["vehicle_photo"]) && $_FILES['vehicle_photo']['name'] != '' && (!empty($_FILES['vehicle_photo']['name']))) {

                $img_name = $this->media_storage->fileupload("vehicle_photo", "./uploads/vehicle_photo/");
            } else {
                $img_name = $vehicle->vehicle_photo;
            }

            $data['vehicle_photo'] = $img_name;

            if (isset($_FILES["vehicle_photo"]) && $_FILES['vehicle_photo']['name'] != '' && (!empty($_FILES['vehicle_photo']['name']))) {
                if ($vehicle->vehicle_photo != '') {
                    $this->media_storage->filedelete($vehicle->vehicle_photo, "uploads/school_income");
                }
            }
            
            $this->vehicle_model->add($data);

            $msg   = $this->lang->line('success_message');
            $array = array('status' => 'success', 'error' => '', 'message' => $msg);
        }
        echo json_encode($array);
    }

    public function delete($id)
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_delete')) {
            access_denied();
        }        
        $this->vehicle_model->remove($id);
        redirect('admin/vehicle/index');
    }
    
    public function vehicledetails()
    {
        $vehicleid           = $this->input->post('vehicleid');
        $data['editvehicle'] = $this->vehicle_model->get($vehicleid);
        $page                = $this->load->view('admin/vehicle/_vehicledetails', $data, true);
        echo json_encode(array('page' => $page));
    }
    
    public function handle_upload()
    {
        $image_validate = $this->config->item('file_validate');
        $result         = $this->filetype_model->get();
        if (isset($_FILES["vehicle_photo"]) && !empty($_FILES['vehicle_photo']['name'])) {

            $file_type = $_FILES["vehicle_photo"]['type'];
            $file_size = $_FILES["vehicle_photo"]["size"];
            $file_name = $_FILES["vehicle_photo"]["name"];

            $allowed_extension = array_map('trim', array_map('strtolower', explode(',', $result->image_extension)));
            $allowed_mime_type = array_map('trim', array_map('strtolower', explode(',', $result->image_mime)));
            $ext               = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

            if ($files = filesize($_FILES['vehicle_photo']['tmp_name'])) {

                if (!in_array($file_type, $allowed_mime_type)) {
                    $this->form_validation->set_message('handle_upload', $this->lang->line('file_type_not_allowed'));
                    return false;
                }

                if (!in_array($ext, $allowed_extension) || !in_array($file_type, $allowed_mime_type)) {
                    $this->form_validation->set_message('handle_upload', $this->lang->line('extension_not_allowed'));
                    return false;
                }
                
                if ($file_size > $result->image_size) {
                    $this->form_validation->set_message('handle_upload', $this->lang->line('file_size_shoud_be_less_than') . number_format($result->image_size / 1048576, 2) . " MB");
                    return false;
                }
            } else {
                $this->form_validation->set_message('handle_upload', $this->lang->line('file_type_extension_error_uploading_image'));
                return false;
            }

            return true;
        }
        return true;
    }


    /**
     * Live tracking for a specific vehicle
     */
    public function live_tracking($vehicle_id)
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'vehicle/index');

        $data['title'] = 'Live Vehicle Tracking';
        $data['vehicle_id'] = $vehicle_id;
        $data['vehicle_details'] = $this->vehicle_model->get($vehicle_id);
        $data['firebase_config'] = $this->get_firebase_config();

        $this->load->view('layout/header');
        $this->load->view('admin/vehicle/live_tracking', $data);
        $this->load->view('layout/footer');
    }

    /**
     * Trip list for a specific vehicle or all vehicles
     */
    public function trip_list($vehicle_id = null)
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'vehicle/index');

        $data['title'] = 'Vehicle Trip List';
        $data['vehicle_id'] = $vehicle_id;

        if ($vehicle_id) {
            // Get trips for specific vehicle
            $this->db->select('bus_trips.*, v.vehicle_no, v.vehicle_model, s.name as driver_name, s.surname as driver_surname');
            $this->db->from('bus_trips');
            $this->db->where('bus_trips.vechile_id', $vehicle_id);
            $this->db->where('bus_trips.school_id', $this->school_id);
            $this->db->join('vehicles v', 'v.id = bus_trips.vechile_id', 'left');
            $this->db->join('staff s', 's.id = bus_trips.driver_id', 'left');
            $this->db->order_by('bus_trips.trip_start_time', 'DESC');
        } else {
            // Get all trips
            $this->db->select('bus_trips.*, v.vehicle_no, v.vehicle_model, s.name as driver_name, s.surname as driver_surname');
            $this->db->from('bus_trips');
            $this->db->where('bus_trips.school_id', $this->school_id);
            $this->db->join('vehicles v', 'v.id = bus_trips.vechile_id', 'left');
            $this->db->join('staff s', 's.id = bus_trips.driver_id', 'left');
            $this->db->order_by('bus_trips.trip_start_time', 'DESC');
        }

        $query = $this->db->get();
        $data['trips'] = $query->result_array();

        // Get vehicle and vehicles data after completing the trips query
        if ($vehicle_id) {
            $data['vehicle'] = $this->vehicle_model->get($vehicle_id);
        }
        $data['vehicles'] = $this->vehicle_model->get();
        $data['firebase_config'] = $this->get_firebase_config();

        $this->load->view('layout/header');
        $this->load->view('admin/vehicle/trip_list', $data);
        $this->load->view('layout/footer');
    }

    /**
     * Trip details with Firebase integration
     */
    public function trip_details($trip_id)
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_view')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'vehicle/index');

        $data['title'] = 'Trip Details';

        $this->db->select('bus_trips.*, v.vehicle_no, v.vehicle_model, s.name as driver_name, s.surname as driver_surname, s.contact_no as driver_contact');
        $this->db->from('bus_trips');
        $this->db->where('bus_trips.id', $trip_id);
        $this->db->where('bus_trips.school_id', $this->school_id);
        $this->db->join('vehicles v', 'v.id = bus_trips.vechile_id', 'left');
        $this->db->join('staff s', 's.id = bus_trips.driver_id', 'left');
        $query = $this->db->get();

        $data['trip'] = $query->row_array();

        if (empty($data['trip'])) {
            $this->session->set_flashdata('error', 'Trip not found');
            redirect('admin/vehicle/index');
        }

        $data['firebase_config'] = $this->get_firebase_config();

        $this->load->view('layout/header');
        $this->load->view('admin/vehicle/trip_details', $data);
        $this->load->view('layout/footer');
    }

    /**
     * Get Firebase configuration for the current school
     */
    private function get_firebase_config()
    {
        $this->db->where('school_id', $this->school_id);
        $query = $this->db->get('firebase_config');
        $config = $query->row();

        if ($config) {
            return [
                'apiKey' => $config->api_key,
                'authDomain' => $config->project_name . '.firebaseapp.com',
                'databaseURL' => $config->database_url,
                'projectId' => $config->project_name,
                'storageBucket' => $config->project_name . '.appspot.com',
                'messagingSenderId' => $config->messaging_sender_id,
                'appId' => $config->app_id
            ];
        }

        return null;
    }

    /**
     * Firebase configuration management
     */
    public function firebase_config()
    {
        if (!$this->rbac->hasPrivilege('vehicle', 'can_edit')) {
            access_denied();
        }

        $this->session->set_userdata('top_menu', 'Transport');
        $this->session->set_userdata('sub_menu', 'vehicle/index');

        $this->load->model('Firebase_config_model');

        if ($this->input->post('submit')) {
            $data = [
                'school_id' => $this->school_id,
                'project_name' => $this->input->post('project_name'),
                'project_url' => $this->input->post('project_url'),
                'api_key' => $this->input->post('api_key'),
                'database_url' => $this->input->post('database_url'),
                'messaging_sender_id' => $this->input->post('messaging_sender_id'),
                'app_id' => $this->input->post('app_id'),
                'server_key' => $this->input->post('server_key')
            ];

            if ($this->Firebase_config_model->add($data)) {
                $this->session->set_flashdata('msg', 'Firebase configuration saved successfully');
            } else {
                $this->session->set_flashdata('error', 'Failed to save Firebase configuration');
            }
            redirect('admin/vehicle/firebase_config');
        }

        $data['title'] = 'Firebase Configuration';
        $data['firebase_config'] = $this->Firebase_config_model->get();

        $this->load->view('layout/header');
        $this->load->view('admin/vehicle/firebase_config', $data);
        $this->load->view('layout/footer');
    }
}
