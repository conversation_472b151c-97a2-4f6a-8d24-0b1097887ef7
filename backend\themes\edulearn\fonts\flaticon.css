	/*
  	Flaticon icon font: Flaticon
  	Creation date: 02/02/2018 08:55
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
  font-style: normal;
}

.flaticon-tool-2:before { content: "\f100"; }
.flaticon-ribbon:before { content: "\f101"; }
.flaticon-school-1:before { content: "\f102"; }
.flaticon-graduation:before { content: "\f103"; }
.flaticon-school:before { content: "\f104"; }
.flaticon-document:before { content: "\f105"; }
.flaticon-book-1:before { content: "\f106"; }
.flaticon-tool-1:before { content: "\f107"; }
.flaticon-tool:before { content: "\f108"; }
.flaticon-book:before { content: "\f109"; }
.flaticon-people:before { content: "\f10a"; }
.flaticon-diploma:before { content: "\f10b"; }