<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> <?php echo $this->lang->line('vehicle_trips'); ?>
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-list"></i> <?php echo $this->lang->line('trip_list'); ?></h3>
                        <div class="box-tools pull-right">
                            <?php if ($vehicle_id) { ?>
                                <a href="<?php echo site_url('admin/vehicle'); ?>" class="btn btn-primary btn-sm"><i class="fa fa-arrow-left"></i> <?php echo $this->lang->line('back'); ?></a>
                            <?php } ?>
                        </div>
                    </div>
                    <div class="box-body">
                        <?php if (!$vehicle_id) { ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('select_vehicle'); ?></label>
                                        <select class="form-control" id="vehicle_filter">
                                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                                            <?php foreach ($vehicles as $vehicle) { ?>
                                                <option value="<?php echo $vehicle['id']; ?>"><?php echo $vehicle['vehicle_no'] . ' - ' . $vehicle['vehicle_model']; ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><?php echo $this->lang->line('status'); ?></label>
                                        <select class="form-control" id="status_filter">
                                            <option value=""><?php echo $this->lang->line('all'); ?></option>
                                            <option value="ongoing"><?php echo $this->lang->line('ongoing'); ?></option>
                                            <option value="completed"><?php echo $this->lang->line('completed'); ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped" id="trip_list">
                                <thead>
                                    <tr>
                                        <th><?php echo $this->lang->line('vehicle'); ?></th>
                                        <th><?php echo $this->lang->line('driver'); ?></th>
                                        <th><?php echo $this->lang->line('start_time'); ?></th>
                                        <th><?php echo $this->lang->line('end_time'); ?></th>
                                        <th><?php echo $this->lang->line('status'); ?></th>
                                        <th class="text-right"><?php echo $this->lang->line('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($trips)) {
                                        foreach ($trips as $trip) { ?>
                                            <tr>
                                                <td><?php echo $trip['vehicle_no'] . ' - ' . $trip['vehicle_model']; ?></td>
                                                <td><?php echo $trip['driver_name']; ?></td>
                                                <td><?php echo date('d-m-Y h:i A', strtotime($trip['trip_start_time'])); ?></td>
                                                <td>
                                                    <?php 
                                                    if ($trip['trip_end_time']) {
                                                        echo date('d-m-Y h:i A', strtotime($trip['trip_end_time']));
                                                    } else {
                                                        echo '-';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($trip['status'] == 'ongoing') { ?>
                                                        <span class="label label-success"><?php echo $this->lang->line('ongoing'); ?></span>
                                                    <?php } else { ?>
                                                        <span class="label label-default"><?php echo $this->lang->line('completed'); ?></span>
                                                    <?php } ?>
                                                </td>
                                                <td class="text-right">
                                                    <a href="<?php echo site_url('admin/vehicle/trip_details/' . $trip['id']); ?>" class="btn btn-default btn-xs" data-toggle="tooltip" title="<?php echo $this->lang->line('view'); ?>">
                                                        <i class="fa fa-eye"></i>
                                                    </a>
                                                    <?php if ($trip['status'] == 'ongoing') { ?>
                                                        <a href="#" class="btn btn-success btn-xs" onclick="viewLiveTracking('<?php echo $trip['id']; ?>')" data-toggle="tooltip" title="<?php echo $this->lang->line('live_tracking'); ?>">
                                                            <i class="fa fa-map-marker"></i>
                                                        </a>
                                                    <?php } ?>
                                                </td>
                                            </tr>
                                        <?php }
                                    } else { ?>
                                        <tr>
                                            <td colspan="6" class="text-center"><?php echo $this->lang->line('no_record_found'); ?></td>
                                        </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
    $(document).ready(function() {
        $('#trip_list').DataTable({
            "aaSorting": [[2, "desc"]]
        });
        
        $('#vehicle_filter, #status_filter').change(function() {
            filterTrips();
        });
    });
    
    function filterTrips() {
        const vehicleId = $('#vehicle_filter').val();
        const status = $('#status_filter').val();
        
        window.location.href = '<?php echo site_url("admin/vehicle/trip_list"); ?>/' + 
            (vehicleId ? vehicleId : '') + 
            (status ? '?status=' + status : '');
    }
    
    function viewLiveTracking(tripId) {
        // Get trip details from Firebase
        const firebaseConfig = {
            apiKey: "YOUR_FIREBASE_API_KEY",
            authDomain: "YOUR_FIREBASE_AUTH_DOMAIN",
            databaseURL: "YOUR_FIREBASE_DATABASE_URL",
            projectId: "YOUR_FIREBASE_PROJECT_ID",
            storageBucket: "YOUR_FIREBASE_STORAGE_BUCKET",
            messagingSenderId: "YOUR_FIREBASE_MESSAGING_SENDER_ID",
            appId: "YOUR_FIREBASE_APP_ID"
        };
        
        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        
        const database = firebase.database();
        const tripRef = database.ref('trips/' + tripId);
        
        // Open modal with map
        $('#liveTrackingModal').modal('show');
        
        // Initialize map after modal is shown
        $('#liveTrackingModal').on('shown.bs.modal', function() {
            initTripMap(tripId);
        });
    }
    
    function initTripMap(tripId) {
        // Code to initialize map and show trip tracking
        // This would be similar to the live tracking code
    }
</script>

<!-- Live Tracking Modal -->
<div class="modal fade" id="liveTrackingModal" tabindex="-1" role="dialog" aria-labelledby="liveTrackingModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="liveTrackingModalLabel"><?php echo $this->lang->line('live_tracking'); ?></h4>
            </div>
            <div class="modal-body">
                <div id="trip_map" style="height: 500px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo $this->lang->line('close'); ?></button>
            </div>
        </div>
    </div>
</div>