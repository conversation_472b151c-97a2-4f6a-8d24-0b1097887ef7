-- Add firebase_json_url field to bus_trips table
-- This field will store the full Firebase URL for trip location data
-- Example: https://schoolxon-5bbb5-default-rtdb.firebaseio.com/trips/trip_123/3.json

ALTER TABLE `bus_trips`
ADD COLUMN `firebase_json_url` VARCHAR(500) NULL
AFTER `status`
COMMENT 'Full Firebase URL for trip location data';

-- Add index for better performance when searching by firebase_json_url
ALTER TABLE `bus_trips`
ADD INDEX `idx_firebase_json_url` (`firebase_json_url`);

-- Note: firebase_json_url will be populated when Firebase config is available
-- The URL will be generated using: firebase_config.database_url + '/trips/trip_' + trip_id + '/' + vehicle_id + '.json'
