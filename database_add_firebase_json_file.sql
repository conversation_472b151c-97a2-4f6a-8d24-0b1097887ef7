-- Add firebase_json_file field to bus_trips table
-- This field will store the Firebase path for trip location data
-- Example: trips/trip_123/3 (without .json extension)

ALTER TABLE `bus_trips` 
ADD COLUMN `firebase_json_file` VARCHAR(255) NULL 
AFTER `status` 
COMMENT 'Firebase path for trip location data (e.g., trips/trip_123/3)';

-- Add index for better performance when searching by firebase_json_file
ALTER TABLE `bus_trips` 
ADD INDEX `idx_firebase_json_file` (`firebase_json_file`);

-- Update existing trips with generated firebase_json_file paths
-- This will create paths like: trips/trip_{id}/{vehicle_id}
UPDATE `bus_trips` 
SET `firebase_json_file` = CONCAT('trips/trip_', id, '/', vechile_id) 
WHERE `firebase_json_file` IS NULL;
