<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Edulearn_model extends CI_Model
{

    public function __construct()
    {
        parent::__construct();
        $this->current_session = $this->setting_model->getCurrentSession();
        $this->school_id = $this->session->userdata('admin')['school_id'];
    }

    /**
     * Get all homepage content sections for edulearn theme
     * 
     * @param int $school_id School ID (0 for default content)
     * @return array Content sections grouped by section name
     */
    public function getHomepageContent($school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }
        
        $this->db->select('*');
        $this->db->from('page_contents');
        $this->db->where('page_slug', 'home');
        $this->db->where('school_id', $school_id);
        $this->db->where('status', 1);
        $query = $this->db->get();
        
        $content = array();
        if ($query->num_rows() > 0) {
            foreach ($query->result_array() as $row) {
                $content[$row['section_name']] = array(
                    'title' => $row['content_title'],
                    'content' => $row['content_text'],
                    'image' => $row['content_image'],
                    'status' => $row['status'],
                    'updated_at' => $row['updated_at']
                );
            }
        }
        
        return $content;
    }

    /**
     * Get content for a specific section with fallback to default
     * 
     * @param string $section_name Section name
     * @param int $school_id School ID
     * @return array|null Content data or null if not found
     */
    public function getSectionContent($section_name, $school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }
        
        // First try to get school-specific content
        $this->db->select('*');
        $this->db->from('page_contents');
        $this->db->where('page_slug', 'home');
        $this->db->where('section_name', $section_name);
        $this->db->where('school_id', $school_id);
        $this->db->where('status', 1);
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            return $query->row_array();
        }
        
        // Fallback to default content (school_id = 0)
        if ($school_id != 0) {
            $this->db->select('*');
            $this->db->from('page_contents');
            $this->db->where('page_slug', 'home');
            $this->db->where('section_name', $section_name);
            $this->db->where('school_id', 0);
            $this->db->where('status', 1);
            $query = $this->db->get();
            
            if ($query->num_rows() > 0) {
                return $query->row_array();
            }
        }
        
        return null;
    }

    /**
     * Save or update homepage content section
     * 
     * @param array $data Content data
     * @return bool Success status
     */
    public function saveContent($data)
    {
        // Ensure required fields
        if (!isset($data['school_id'])) {
            $data['school_id'] = $this->school_id;
        }
        if (!isset($data['page_slug'])) {
            $data['page_slug'] = 'home';
        }
        
        // Check if content already exists
        $this->db->where('school_id', $data['school_id']);
        $this->db->where('page_slug', $data['page_slug']);
        $this->db->where('section_name', $data['section_name']);
        $existing = $this->db->get('page_contents')->row();
        
        if ($existing) {
            // Update existing content
            $data['updated_at'] = date('Y-m-d H:i:s');
            $this->db->where('id', $existing->id);
            return $this->db->update('page_contents', $data);
        } else {
            // Insert new content
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            return $this->db->insert('page_contents', $data);
        }
    }

    /**
     * Delete content section for a school (reset to default)
     * 
     * @param string $section_name Section name
     * @param int $school_id School ID
     * @return bool Success status
     */
    public function deleteSchoolContent($section_name, $school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }
        
        $this->db->where('school_id', $school_id);
        $this->db->where('page_slug', 'home');
        $this->db->where('section_name', $section_name);
        return $this->db->delete('page_contents');
    }

    /**
     * Toggle section status
     * 
     * @param string $section_name Section name
     * @param int $status Status (0 or 1)
     * @param int $school_id School ID
     * @return bool Success status
     */
    public function toggleSectionStatus($section_name, $status, $school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }
        
        // Get current content
        $current = $this->getSectionContent($section_name, $school_id);
        
        if ($current && $current['school_id'] == $school_id) {
            // Update existing school-specific content
            $this->db->where('id', $current['id']);
            return $this->db->update('page_contents', array('status' => $status));
        } else {
            // Create new school-specific content with status
            $default_content = $this->getSectionContent($section_name, 0);
            if ($default_content) {
                $data = array(
                    'school_id' => $school_id,
                    'page_slug' => 'home',
                    'section_name' => $section_name,
                    'content_title' => $default_content['content_title'],
                    'content_text' => $default_content['content_text'],
                    'content_image' => $default_content['content_image'],
                    'status' => $status,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                );
                return $this->db->insert('page_contents', $data);
            }
        }
        
        return false;
    }

    /**
     * Initialize default content for edulearn theme
     * This method creates default content sections for the homepage
     * 
     * @return bool Success status
     */
    public function initializeDefaultContent()
    {
        $default_sections = array(
            // Slider sections
            array(
                'section_name' => 'slider_1_title',
                'content_title' => 'WELCOME TO EDULEARN',
                'content_text' => '',
            ),
            array(
                'section_name' => 'slider_1_content',
                'content_title' => '',
                'content_text' => 'Discover excellence in education with our comprehensive programs designed to nurture young minds and build bright futures.',
            ),
            array(
                'section_name' => 'slider_2_title',
                'content_title' => 'ARE YOU READY TO APPLY?',
                'content_text' => '',
            ),
            array(
                'section_name' => 'slider_2_content',
                'content_title' => '',
                'content_text' => 'Join our community of learners and discover your potential with our comprehensive educational programs.',
            ),
            array(
                'section_name' => 'slider_3_title',
                'content_title' => 'EXCELLENCE IN EDUCATION',
                'content_text' => '',
            ),
            array(
                'section_name' => 'slider_3_content',
                'content_title' => '',
                'content_text' => 'Experience quality education with modern facilities and dedicated teachers committed to your success.',
            ),
            
            // Services sections
            array(
                'section_name' => 'service_1_title',
                'content_title' => 'Quality Education',
                'content_text' => '',
            ),
            array(
                'section_name' => 'service_1_content',
                'content_title' => '',
                'content_text' => 'We provide comprehensive and quality education for all students',
            ),
            array(
                'section_name' => 'service_2_title',
                'content_title' => 'Books & Library',
                'content_text' => '',
            ),
            array(
                'section_name' => 'service_2_content',
                'content_title' => '',
                'content_text' => 'Access to extensive library resources and educational materials',
            ),
            array(
                'section_name' => 'service_3_title',
                'content_title' => 'Certified Teachers',
                'content_text' => '',
            ),
            array(
                'section_name' => 'service_3_content',
                'content_title' => '',
                'content_text' => 'Experienced and qualified teachers dedicated to student success',
            ),
            array(
                'section_name' => 'service_4_title',
                'content_title' => 'Certification',
                'content_text' => '',
            ),
            array(
                'section_name' => 'service_4_content',
                'content_title' => '',
                'content_text' => 'Recognized certifications and academic achievements',
            ),
            
            // About section
            array(
                'section_name' => 'about_section_title',
                'content_title' => 'ABOUT US',
                'content_text' => '',
            ),
            array(
                'section_name' => 'about_section_subtitle',
                'content_title' => '',
                'content_text' => 'Learn more about our institution and our commitment to excellence in education.',
            ),
            array(
                'section_name' => 'about_title',
                'content_title' => 'WELCOME TO OUR SCHOOL',
                'content_text' => '',
            ),
            array(
                'section_name' => 'about_content',
                'content_title' => '',
                'content_text' => 'Our institution is dedicated to providing quality education and fostering academic excellence. We believe in nurturing young minds and preparing students for a successful future.',
            ),
            array(
                'section_name' => 'history_title',
                'content_title' => 'Our History',
                'content_text' => '',
            ),
            array(
                'section_name' => 'history_content',
                'content_title' => '',
                'content_text' => 'Our institution has a rich history of providing quality education and fostering academic excellence for generations of students.',
            ),
            array(
                'section_name' => 'mission_title',
                'content_title' => 'Our Mission',
                'content_text' => '',
            ),
            array(
                'section_name' => 'mission_content',
                'content_title' => '',
                'content_text' => 'To provide comprehensive education that develops critical thinking, creativity, and character in our students, preparing them for success in their future endeavors.',
            ),
            array(
                'section_name' => 'vision_title',
                'content_title' => 'Our Vision',
                'content_text' => '',
            ),
            array(
                'section_name' => 'vision_content',
                'content_title' => '',
                'content_text' => 'To be a leading educational institution that inspires lifelong learning and empowers students to become responsible global citizens.',
            ),
        );
        
        $success = true;
        foreach ($default_sections as $section) {
            $section['school_id'] = 0; // Default content
            $section['page_slug'] = 'home';
            $section['status'] = 1;
            $section['created_at'] = date('Y-m-d H:i:s');
            $section['updated_at'] = date('Y-m-d H:i:s');
            
            // Check if section already exists
            $this->db->where('school_id', 0);
            $this->db->where('page_slug', 'home');
            $this->db->where('section_name', $section['section_name']);
            $existing = $this->db->get('page_contents')->row();
            
            if (!$existing) {
                if (!$this->db->insert('page_contents', $section)) {
                    $success = false;
                }
            }
        }
        
        return $success;
    }
}
