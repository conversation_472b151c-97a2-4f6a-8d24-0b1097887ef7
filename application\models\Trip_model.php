<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Trip_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
    }
    
    public function get($id = null)
    {
        $this->db->select('bus_trips.*, v.vehicle_no, v.vehicle_model, staff.name as driver_name, staff.surname as driver_surname');
        $this->db->from('bus_trips');
        $this->db->join('vehicles v', 'v.id = bus_trips.vechile_id', 'left');
        $this->db->join('staff', 'staff.id = bus_trips.driver_id', 'left');
        
        if ($id != null) {
            $this->db->where('bus_trips.id', $id);
            return $this->db->get()->row_array();
        } else {
            $this->db->where('bus_trips.school_id', $this->school_id);
            $this->db->order_by('bus_trips.trip_start_time', 'DESC');
            return $this->db->get()->result_array();
        }
    }
    
    public function getByVehicle($vehicle_id)
    {
        $this->db->select('bus_trips.*, staff.name as driver_name, staff.surname as driver_surname');
        $this->db->from('bus_trips');
        $this->db->where('bus_trips.vechile_id', $vehicle_id);
        $this->db->where('bus_trips.school_id', $this->school_id);
        $this->db->join('staff', 'staff.id = bus_trips.driver_id', 'left');
        $this->db->order_by('bus_trips.trip_start_time', 'DESC');
        return $this->db->get()->result_array();
    }
}