<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Trip_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
    }
    
    public function get($id = null)
    {
        $this->db->select('bus_trips.*, v.vehicle_no, v.vehicle_model, staff.name as driver_name, staff.surname as driver_surname');
        $this->db->from('bus_trips');
        $this->db->join('vehicles v', 'v.id = bus_trips.vechile_id', 'left');
        $this->db->join('staff', 'staff.id = bus_trips.driver_id', 'left');

        if ($id != null) {
            $this->db->where('bus_trips.id', $id);
            return $this->db->get()->row_array();
        } else {
            $this->db->where('bus_trips.school_id', $this->school_id);
            $this->db->order_by('bus_trips.trip_start_time', 'DESC');
            return $this->db->get()->result_array();
        }
    }
    
    public function getByVehicle($vehicle_id)
    {
        $this->db->select('bus_trips.*, staff.name as driver_name, staff.surname as driver_surname');
        $this->db->from('bus_trips');
        $this->db->where('bus_trips.vechile_id', $vehicle_id);
        $this->db->where('bus_trips.school_id', $this->school_id);
        $this->db->join('staff', 'staff.id = bus_trips.driver_id', 'left');
        $this->db->order_by('bus_trips.trip_start_time', 'DESC');
        return $this->db->get()->result_array();
    }

    /**
     * Add a new trip with firebase_json_url
     */
    public function add($data)
    {
        $this->db->trans_start();

        // firebase_json_url will be set later when Firebase config is available
        if (empty($data['firebase_json_url'])) {
            $data['firebase_json_url'] = null;
        }

        $this->db->insert('bus_trips', $data);
        $insert_id = $this->db->insert_id();

        $this->db->trans_complete();

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            return false;
        }

        return $insert_id;
    }

    /**
     * Update trip with firebase_json_file path
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        $this->db->where('school_id', $this->school_id);
        $this->db->update('bus_trips', $data);
        return $this->db->affected_rows() > 0;
    }

    /**
     * Get Firebase URL for trip location data
     */
    public function getFirebaseUrl($trip_id, $firebase_config = null)
    {
        $trip = $this->get($trip_id);

        if (!$trip || !$firebase_config) {
            return null;
        }

        // Return stored URL if available
        if (!empty($trip['firebase_json_url'])) {
            return $trip['firebase_json_url'];
        }

        // Generate URL if not stored
        $firebase_path = 'trips/trip_' . $trip_id . '/' . $trip['vechile_id'];
        $database_url = rtrim($firebase_config['databaseURL'], '/');
        return $database_url . '/' . $firebase_path . '.json';
    }

    /**
     * Update firebase_json_url for existing trip
     */
    public function updateFirebaseUrl($trip_id, $firebase_url)
    {
        $this->db->where('id', $trip_id);
        $this->db->where('school_id', $this->school_id);
        $this->db->update('bus_trips', ['firebase_json_url' => $firebase_url]);
        return $this->db->affected_rows() > 0;
    }

    /**
     * Generate Firebase URL for a trip
     */
    public function generateFirebaseUrl($trip_id, $vehicle_id, $firebase_config)
    {
        if (!$firebase_config || empty($firebase_config['databaseURL'])) {
            return null;
        }

        $firebase_path = 'trips/trip_' . $trip_id . '/' . $vehicle_id;
        $database_url = rtrim($firebase_config['databaseURL'], '/');
        return $database_url . '/' . $firebase_path . '.json';
    }
}