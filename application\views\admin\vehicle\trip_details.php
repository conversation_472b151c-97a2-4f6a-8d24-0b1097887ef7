<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> Trip Details
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-info-circle"></i> Trip Information</h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo site_url('admin/vehicle/trip_list/' . $trip['vechile_id']); ?>" class="btn btn-primary btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to Trip List
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <?php if (!empty($trip)) { ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Vehicle Number:</strong></label>
                                        <p><?php echo $trip['vehicle_no']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Vehicle Model:</strong></label>
                                        <p><?php echo $trip['vehicle_model']; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Driver Name:</strong></label>
                                        <p><?php echo $trip['driver_name'] . ' ' . $trip['driver_surname']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Driver Contact:</strong></label>
                                        <p><?php echo $trip['driver_contact']; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip Start Time:</strong></label>
                                        <p><?php echo date('d-m-Y h:i A', strtotime($trip['trip_start_time'])); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip End Time:</strong></label>
                                        <p>
                                            <?php 
                                            if ($trip['trip_end_time']) {
                                                echo date('d-m-Y h:i A', strtotime($trip['trip_end_time']));
                                            } else {
                                                echo 'Ongoing';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Status:</strong></label>
                                        <p>
                                            <?php if ($trip['status'] == 'ongoing') { ?>
                                                <span class="label label-success">Ongoing</span>
                                            <?php } else { ?>
                                                <span class="label label-default">Completed</span>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip Duration:</strong></label>
                                        <p>
                                            <?php 
                                            if ($trip['trip_end_time']) {
                                                $start = new DateTime($trip['trip_start_time']);
                                                $end = new DateTime($trip['trip_end_time']);
                                                $duration = $start->diff($end);
                                                echo $duration->format('%h hours %i minutes');
                                            } else {
                                                $start = new DateTime($trip['trip_start_time']);
                                                $now = new DateTime();
                                                $duration = $start->diff($now);
                                                echo $duration->format('%h hours %i minutes') . ' (ongoing)';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Firebase Configuration Info -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h4><i class="fa fa-info-circle"></i> Firebase Location Data</h4>
                                        <p><strong>Firebase URL:</strong>
                                            <?php if (!empty($trip['firebase_json_url'])) { ?>
                                                <code><?php echo $trip['firebase_json_url']; ?></code>
                                                <br>
                                                <a href="<?php echo $trip['firebase_json_url']; ?>" target="_blank" class="btn btn-xs btn-primary">
                                                    <i class="fa fa-external-link"></i> View in Firebase
                                                </a>
                                            <?php } else { ?>
                                                <code class="text-muted">Firebase URL will be generated when Firebase is configured</code>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Firebase Trip Data Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="box box-info">
                                        <div class="box-header with-border">
                                            <h3 class="box-title"><i class="fa fa-map"></i> Trip Route & Firebase Data</h3>
                                            <div class="box-tools pull-right">
                                                <button type="button" class="btn btn-success btn-sm" onclick="loadFirebaseData()">
                                                    <i class="fa fa-refresh"></i> Load Firebase Data
                                                </button>
                                                <button type="button" class="btn btn-info btn-sm" onclick="loadRouteDetails()">
                                                    <i class="fa fa-map"></i> Show Route
                                                </button>
                                                <button type="button" class="btn btn-primary btn-sm" onclick="pushTestLocation()">
                                                    <i class="fa fa-plus"></i> Push Test Data
                                                </button>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="stopRealTimeUpdates()" style="display: none;" id="stop-updates-btn">
                                                    <i class="fa fa-stop"></i> Stop Updates
                                                </button>
                                            </div>
                                        </div>
                                        <div class="box-body">
                                            <div id="firebase-loading" style="display: none;">
                                                <p><i class="fa fa-spinner fa-spin"></i> Loading Firebase data...</p>
                                            </div>
                                            <div id="firebase-data">
                                                <p class="text-muted">Click "Load Firebase Data" to fetch real-time trip information.</p>
                                            </div>
                                            <div id="trip-map" style="height: 400px; margin-top: 20px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        <?php } else { ?>
                            <div class="alert alert-warning">
                                <i class="fa fa-warning"></i> Trip details not found.
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Include Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo $google_maps_api_key; ?>&callback=initMap" async defer></script>

<script>
    // Firebase configuration from database
    <?php if (!empty($firebase_config)) { ?>
    const firebaseConfig = {
        apiKey: "<?php echo $firebase_config['apiKey']; ?>",
        authDomain: "<?php echo $firebase_config['authDomain']; ?>",
        databaseURL: "<?php echo $firebase_config['databaseURL']; ?>",
        projectId: "<?php echo $firebase_config['projectId']; ?>",
        storageBucket: "<?php echo $firebase_config['storageBucket']; ?>",
        messagingSenderId: "<?php echo $firebase_config['messagingSenderId']; ?>",
        appId: "<?php echo $firebase_config['appId']; ?>"
    };
    <?php } else { ?>
    const firebaseConfig = null;
    <?php } ?>

    // Initialize Firebase
    let database = null;
    if (firebaseConfig && !firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        database = firebase.database();
    } else if (firebaseConfig) {
        database = firebase.database();
    }
    
    let map;
    let tripPath;
    let vehicleMarker = null;
    let bounds;
    let firebaseListener = null;
    let locationsArray = [];
    let markers = []; // Array to store all markers

    // Initialize Google Map
    function initMap() {
        const defaultLocation = { lat: 25.6175504, lng: 85.14511019999999 };  // Gandhi Maidan
        map = new google.maps.Map(document.getElementById("trip-map"), {
            zoom: 15,
            center: defaultLocation,
        });

        tripPath = new google.maps.Polyline({
            path: [],
            geodesic: true,
            strokeColor: "#2196F3", // blue
            strokeOpacity: 1.0,
            strokeWeight: 4,
        });

        tripPath.setMap(map);

        bounds = new google.maps.LatLngBounds();
    }

    // Load data from Firebase and listen for changes
    function loadFirebaseData() {
        if (!database) {
            console.log("Firebase config missing.");
            $('#firebase-data').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Firebase not configured. Please check Firebase settings.</div>');
            return;
        }

        // Show loading indicator
        $('#firebase-loading').show();
        $('#firebase-data').html('<p class="text-muted">Loading Firebase data...</p>');

        // Use dynamic trip ID and vehicle ID from PHP
        const tripId = "<?php echo $trip['id']; ?>";
        const vehicleId = "<?php echo $trip['vechile_id']; ?>";
        const firebasePath = "trips/trip_" + tripId + "/" + vehicleId;

        console.log("Loading Firebase data from path:", firebasePath);
        console.log("Trip ID:", tripId, "Vehicle ID:", vehicleId);

        // Show the path being used for debugging
        $('#firebase-data').html('<div class="alert alert-info"><i class="fa fa-info-circle"></i> Loading data from Firebase path: <code>' + firebasePath + '</code></div>');

        const tripRef = database.ref(firebasePath);

        if (firebaseListener) {
            firebaseListener.off();
        }

        firebaseListener = tripRef;

        tripRef.on("value", (snapshot) => {
            const data = snapshot.val();
            console.log("Firebase data received:", data);

            // Hide loading indicator
            $('#firebase-loading').hide();

            if (!data) {
                console.log("No Firebase data found");
                $('#firebase-data').html('<div class="alert alert-info"><i class="fa fa-info-circle"></i> No Firebase data found for this trip. Path: <code>' + firebasePath + '</code></div>');
                return;
            }

            // Display the Firebase data structure for debugging
            displayFirebaseData(data);

            // Check if data is a single location object
            if (data.latitude && data.longitude) {
                console.log("Single location data found");
                displaySingleLocation(data);
                return;
            }

            // Handle multiple location entries (timestamped data)
            const locationEntries = Object.entries(data)
                .filter(([key, value]) => value && typeof value === 'object' && value.latitude && value.longitude)
                .sort((a, b) => (a[1].timestamp || parseInt(a[0])) - (b[1].timestamp || parseInt(b[0])));

            console.log("Location entries found:", locationEntries.length);

            if (locationEntries.length === 0) {
                console.log("No valid location entries found");
                return;
            }

            locationsArray = locationEntries.map(([_, loc]) => ({
                lat: parseFloat(loc.latitude),
                lng: parseFloat(loc.longitude),
            }));

            drawPathAndMoveMarker();
        }, (error) => {
            console.error("Firebase error:", error);
            $('#firebase-loading').hide();
            $('#firebase-data').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Error loading Firebase data: ' + error.message + '<br>Path: <code>' + firebasePath + '</code></div>');
        });
    }

    // Draw polyline and move vehicle marker
    function drawPathAndMoveMarker() {
        if (!locationsArray.length) return;

        const lastPoint = locationsArray[locationsArray.length - 1];

        // Update polyline
        tripPath.setPath(locationsArray);

        // Move marker
        if (!vehicleMarker) {
            vehicleMarker = new google.maps.Marker({
                position: lastPoint,
                map: map,
                title: "Live Bus",
                icon: {
                    url: "<?php echo base_url(); ?>backend/icons/bus_tracking/bus_32.png",
                    scaledSize: new google.maps.Size(40, 40),
                },
            });
        } else {
            vehicleMarker.setPosition(lastPoint);
        }

        // Extend bounds and auto-fit map
        bounds.extend(lastPoint);
        map.panTo(lastPoint);
        map.setZoom(17);
    }

    // Stop real-time updates
    function stopRealTimeUpdates() {
        if (firebaseListener) {
            firebaseListener.off();
            firebaseListener = null;
            console.log("Real-time updates stopped.");
        }
    }

    $(document).ready(function () {
        initMap();
        setTimeout(loadFirebaseData, 1000);
    });
 
    
    function displayFirebaseData(data) {
        let html = '<div class="row">';

        // Display raw data structure for debugging
        html += '<div class="col-md-12">';
        html += '<h4><i class="fa fa-database"></i> Firebase Data Structure</h4>';
        html += '<pre style="max-height: 200px; overflow-y: auto; background: #f4f4f4; padding: 10px; border-radius: 4px;">';
        html += JSON.stringify(data, null, 2);
        html += '</pre>';
        html += '</div>';

        // Check for location data
        if (data.latitude && data.longitude) {
            html += '<div class="col-md-6">';
            html += '<h4><i class="fa fa-map-marker"></i> Current Location</h4>';
            html += '<p><strong>Latitude:</strong> ' + data.latitude + '</p>';
            html += '<p><strong>Longitude:</strong> ' + data.longitude + '</p>';
            if (data.speed) html += '<p><strong>Speed:</strong> ' + data.speed + ' km/h</p>';
            if (data.timestamp) html += '<p><strong>Timestamp:</strong> ' + new Date(data.timestamp).toLocaleString() + '</p>';
            html += '</div>';
        }

        // Count location entries
        const locationEntries = Object.entries(data).filter(([key, value]) =>
            value && typeof value === 'object' && value.latitude && value.longitude
        );

        if (locationEntries.length > 0) {
            html += '<div class="col-md-6">';
            html += '<h4><i class="fa fa-list"></i> Location Points</h4>';
            html += '<p><strong>Total Points:</strong> ' + locationEntries.length + '</p>';
            html += '<p><strong>Latest Update:</strong> ' + new Date(Math.max(...locationEntries.map(([key]) => parseInt(key) || 0))).toLocaleString() + '</p>';
            html += '</div>';
        }

        html += '</div>';

        $('#firebase-data').html(html);
    }

    function displaySingleLocation(data) {
        if (!data.latitude || !data.longitude) return;

        const position = {
            lat: parseFloat(data.latitude),
            lng: parseFloat(data.longitude)
        };

        // Clear existing markers
        markers.forEach(marker => marker.setMap(null));
        markers = [];

        // Add marker for current location
        const marker = new google.maps.Marker({
            position: position,
            map: map,
            title: 'Vehicle Location',
            icon: {
                url: '<?php echo base_url(); ?>backend/icons/bus_tracking/bus_32.png',
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        markers.push(marker);

        // Center map on location
        map.setCenter(position);
        map.setZoom(17);

        // Add info window
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>Vehicle Location</h5>
                    <p><strong>Coordinates:</strong> ${data.latitude}, ${data.longitude}</p>
                    ${data.speed ? '<p><strong>Speed:</strong> ' + data.speed + ' km/h</p>' : ''}
                    ${data.timestamp ? '<p><strong>Time:</strong> ' + new Date(data.timestamp).toLocaleString() + '</p>' : ''}
                </div>
            `
        });

        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    }
    
    function displayTripRoute(locations) {
        // Clear existing markers
        markers.forEach(marker => marker.setMap(null));
        markers = [];

        const path = [];
        const bounds = new google.maps.LatLngBounds();

        // Handle both array and object formats
        let locationArray = [];
        if (Array.isArray(locations)) {
            locationArray = locations;
        } else {
            // Convert object to array and sort by timestamp/key
            locationArray = Object.entries(locations)
                .map(([key, value]) => ({...value, timestamp: key}))
                .sort((a, b) => parseInt(a.timestamp) - parseInt(b.timestamp));
        }

        locationArray.forEach((location, index) => {
            if (!location.latitude || !location.longitude) return;

            const position = {
                lat: parseFloat(location.latitude),
                lng: parseFloat(location.longitude)
            };

            path.push(position);
            bounds.extend(position);

            // Add marker for start, end, and current points
            let markerIcon, markerTitle;
            if (index === 0) {
                markerIcon = 'https://maps.google.com/mapfiles/ms/icons/green-dot.png';
                markerTitle = 'Start Point';
            } else if (index === locationArray.length - 1) {
                markerIcon = '<?php echo base_url(); ?>backend/icons/bus_tracking/bus_32.png';
                markerTitle = 'Current Location';
            } else {
                // Skip intermediate points for cleaner display
                return;
            }

            const marker = new google.maps.Marker({
                position: position,
                map: map,
                title: markerTitle,
                icon: {
                    url: markerIcon,
                    scaledSize: new google.maps.Size(32, 32)
                }
            });

            // Add info window
            const infoContent = `
                <div>
                    <h5>${markerTitle}</h5>
                    <p><strong>Coordinates:</strong> ${location.latitude}, ${location.longitude}</p>
                    ${location.speed ? '<p><strong>Speed:</strong> ' + location.speed + ' km/h</p>' : ''}
                    ${location.timestamp ? '<p><strong>Time:</strong> ' + new Date(parseInt(location.timestamp)).toLocaleString() + '</p>' : ''}
                </div>
            `;

            const infoWindow = new google.maps.InfoWindow({
                content: infoContent
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            markers.push(marker);
        });

        // Draw path if we have multiple points
        if (path.length > 1) {
            tripPath.setPath(path);
            map.fitBounds(bounds);
        } else if (path.length === 1) {
            // Single point - center and zoom
            map.setCenter(path[0]);
            map.setZoom(17);
        }
    }

    function stopRealTimeUpdates() {
        if (firebaseListener) {
            firebaseListener.off();
            firebaseListener = null;
            $('#stop-updates-btn').hide();
            $('#firebase-data').append('<div class="alert alert-info" style="margin-top: 10px;"><i class="fa fa-info-circle"></i> Real-time updates stopped. Click "Load Firebase Data" to resume.</div>');
        }
    }

    // Example function to push location data to Firebase (instead of put)
    function pushLocationToFirebase(latitude, longitude, speed) {
        const tripId = "<?php echo $trip['id']; ?>";
        const vehicleId = "<?php echo $trip['vechile_id']; ?>";

        if (!database) {
            console.error('Firebase not initialized');
            return;
        }

        // Use push() to add data with auto-generated timestamp key
        const tripRef = database.ref('trips/trip_' + tripId + '/' + vehicleId);

        const locationData = {
            latitude: parseFloat(latitude),
            longitude: parseFloat(longitude),
            speed: parseFloat(speed || 0),
            timestamp: Date.now(),
            vehicle_id: vehicleId,
            trip_id: tripId
        };

        // Push creates a new child with auto-generated key
        tripRef.push(locationData)
            .then(() => {
                console.log('Location pushed to Firebase successfully');
            })
            .catch((error) => {
                console.error('Error pushing to Firebase:', error);
            });
    }

    // Test function to push sample location data
    function pushTestLocation() {
        if (!database) {
            alert('Firebase not configured');
            return;
        }

        // Use sample coordinates (Gandhi Maidan, Patna)
        const testLat = 25.6175504;
        const testLng = 85.14511019999999;
        const testSpeed = Math.floor(Math.random() * 50) + 10; // Random speed 10-60

        pushLocationToFirebase(testLat, testLng, testSpeed);

        // Show success message
        $('#firebase-data').prepend('<div class="alert alert-success alert-dismissible" style="margin-bottom: 10px;"><button type="button" class="close" data-dismiss="alert">&times;</button><i class="fa fa-check"></i> Test location data pushed to Firebase!</div>');

        // Auto-reload data after 2 seconds
        setTimeout(() => {
            loadFirebaseData();
        }, 2000);
    }

    // Example: Simulate pushing location data every 30 seconds
    function startLocationPushing() {
        // This is just an example - in real app, you'd get GPS coordinates
        const exampleLocations = [
            {lat: 28.6139, lng: 77.2090, speed: 25}, // Delhi
            {lat: 28.6129, lng: 77.2295, speed: 30},
            {lat: 28.6169, lng: 77.2395, speed: 20}
        ];

        let locationIndex = 0;

        setInterval(() => {
            if (locationIndex < exampleLocations.length) {
                const loc = exampleLocations[locationIndex];
                pushLocationToFirebase(loc.lat, loc.lng, loc.speed);
                locationIndex++;
            }
        }, 30000); // Push every 30 seconds
    }

    // Load route details and show route on map
    function loadRouteDetails() {
        const routeId = "<?php echo $trip['route_id']; ?>";

        if (!routeId) {
            console.log('No route ID found for this trip');
            return;
        }

        $.ajax({
            url: '<?php echo base_url(); ?>api/driver/trip/get_route_details',
            type: 'POST',
            data: {
                route_id: routeId
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    displayRouteOnMap(response.route, response.pickup_points);
                } else {
                    console.error('Failed to load route details:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading route details:', error);
            }
        });
    }

    // Display route with start/end points and pickup points
    function displayRouteOnMap(route, pickupPoints) {
        if (!map) return;

        const bounds = new google.maps.LatLngBounds();

        // Add start point marker
        if (route.start_latitude && route.start_longitude) {
            const startPosition = {
                lat: parseFloat(route.start_latitude),
                lng: parseFloat(route.start_longitude)
            };

            const startMarker = new google.maps.Marker({
                position: startPosition,
                map: map,
                title: route.start_route_name || 'Start Point',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
                    scaledSize: new google.maps.Size(32, 32)
                }
            });

            const startInfoWindow = new google.maps.InfoWindow({
                content: '<div><strong>Start Point</strong><br>' + (route.start_route_name || 'Route Start') + '</div>'
            });

            startMarker.addListener('click', function() {
                startInfoWindow.open(map, startMarker);
            });

            bounds.extend(startPosition);
        }

        // Add end point marker
        if (route.end_latitude && route.end_longitude) {
            const endPosition = {
                lat: parseFloat(route.end_latitude),
                lng: parseFloat(route.end_longitude)
            };

            const endMarker = new google.maps.Marker({
                position: endPosition,
                map: map,
                title: route.end_route_name || 'End Point',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
                    scaledSize: new google.maps.Size(32, 32)
                }
            });

            const endInfoWindow = new google.maps.InfoWindow({
                content: '<div><strong>End Point</strong><br>' + (route.end_route_name || 'Route End') + '</div>'
            });

            endMarker.addListener('click', function() {
                endInfoWindow.open(map, endMarker);
            });

            bounds.extend(endPosition);
        }

        // Add pickup points
        if (pickupPoints && pickupPoints.length > 0) {
            pickupPoints.forEach(function(point, index) {
                if (point.latitude && point.longitude) {
                    const position = {
                        lat: parseFloat(point.latitude),
                        lng: parseFloat(point.longitude)
                    };

                    const marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: point.pickup_point_name,
                        icon: {
                            url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png',
                            scaledSize: new google.maps.Size(24, 24)
                        }
                    });

                    const infoWindow = new google.maps.InfoWindow({
                        content: '<div><strong>Pickup Point</strong><br>' + point.pickup_point_name + '<br>Order: ' + (point.order_number || (index + 1)) + '</div>'
                    });

                    marker.addListener('click', function() {
                        infoWindow.open(map, marker);
                    });

                    bounds.extend(position);
                }
            });
        }

        // Fit map to show all markers with proper zoom
        if (!bounds.isEmpty()) {
            map.fitBounds(bounds);

            // Set minimum zoom level to avoid too much zoom
            google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                if (map.getZoom() > 17) {
                    map.setZoom(17);
                }
            });
        }
    }

    $(document).ready(function() {
        // Initialize map first
        initMap();

        // Load route details after map is initialized
        setTimeout(loadRouteDetails, 1000);

        // Auto-load Firebase data if trip is ongoing
        <?php if ($trip['status'] == 'ongoing') { ?>
            setTimeout(loadFirebaseData, 2000);
        <?php } ?>
    });
</script>
