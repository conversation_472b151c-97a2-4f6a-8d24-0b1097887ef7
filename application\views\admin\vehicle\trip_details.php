<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> Trip Details
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-info-circle"></i> Trip Information</h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo site_url('admin/vehicle/trip_list/' . $trip['vechile_id']); ?>" class="btn btn-primary btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to Trip List
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <?php if (!empty($trip)) { ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Vehicle Number:</strong></label>
                                        <p><?php echo $trip['vehicle_no']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Vehicle Model:</strong></label>
                                        <p><?php echo $trip['vehicle_model']; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Driver Name:</strong></label>
                                        <p><?php echo $trip['driver_name'] . ' ' . $trip['driver_surname']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Driver Contact:</strong></label>
                                        <p><?php echo $trip['driver_contact']; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip Start Time:</strong></label>
                                        <p><?php echo date('d-m-Y h:i A', strtotime($trip['trip_start_time'])); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip End Time:</strong></label>
                                        <p>
                                            <?php 
                                            if ($trip['trip_end_time']) {
                                                echo date('d-m-Y h:i A', strtotime($trip['trip_end_time']));
                                            } else {
                                                echo 'Ongoing';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Status:</strong></label>
                                        <p>
                                            <?php if ($trip['status'] == 'ongoing') { ?>
                                                <span class="label label-success">Ongoing</span>
                                            <?php } else { ?>
                                                <span class="label label-default">Completed</span>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip Duration:</strong></label>
                                        <p>
                                            <?php 
                                            if ($trip['trip_end_time']) {
                                                $start = new DateTime($trip['trip_start_time']);
                                                $end = new DateTime($trip['trip_end_time']);
                                                $duration = $start->diff($end);
                                                echo $duration->format('%h hours %i minutes');
                                            } else {
                                                $start = new DateTime($trip['trip_start_time']);
                                                $now = new DateTime();
                                                $duration = $start->diff($now);
                                                echo $duration->format('%h hours %i minutes') . ' (ongoing)';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Firebase Configuration Info -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h4><i class="fa fa-info-circle"></i> Firebase Location Data</h4>
                                        <p><strong>Firebase URL:</strong>
                                            <?php if (!empty($trip['firebase_json_url'])) { ?>
                                                <code><?php echo $trip['firebase_json_url']; ?></code>
                                                <br>
                                                <a href="<?php echo $trip['firebase_json_url']; ?>" target="_blank" class="btn btn-xs btn-primary">
                                                    <i class="fa fa-external-link"></i> View in Firebase
                                                </a>
                                            <?php } else { ?>
                                                <code class="text-muted">Firebase URL will be generated when Firebase is configured</code>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Firebase Trip Data Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="box box-info">
                                        <div class="box-header with-border">
                                            <h3 class="box-title"><i class="fa fa-map"></i> Trip Route & Firebase Data</h3>
                                            <div class="box-tools pull-right">
                                                <button type="button" class="btn btn-success btn-sm" onclick="loadFirebaseData()">
                                                    <i class="fa fa-refresh"></i> Load Firebase Data
                                                </button>
                                                <button type="button" class="btn btn-warning btn-sm" onclick="stopRealTimeUpdates()" style="display: none;" id="stop-updates-btn">
                                                    <i class="fa fa-stop"></i> Stop Updates
                                                </button>
                                            </div>
                                        </div>
                                        <div class="box-body">
                                            <div id="firebase-loading" style="display: none;">
                                                <p><i class="fa fa-spinner fa-spin"></i> Loading Firebase data...</p>
                                            </div>
                                            <div id="firebase-data">
                                                <p class="text-muted">Click "Load Firebase Data" to fetch real-time trip information.</p>
                                            </div>
                                            <div id="trip-map" style="height: 400px; margin-top: 20px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        <?php } else { ?>
                            <div class="alert alert-warning">
                                <i class="fa fa-warning"></i> Trip details not found.
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Include Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo $google_maps_api_key; ?>&callback=initMap" async defer></script>

<script>
    // Firebase configuration from database
    <?php if (!empty($firebase_config)) { ?>
    const firebaseConfig = {
        apiKey: "<?php echo $firebase_config['apiKey']; ?>",
        authDomain: "<?php echo $firebase_config['authDomain']; ?>",
        databaseURL: "<?php echo $firebase_config['databaseURL']; ?>",
        projectId: "<?php echo $firebase_config['projectId']; ?>",
        storageBucket: "<?php echo $firebase_config['storageBucket']; ?>",
        messagingSenderId: "<?php echo $firebase_config['messagingSenderId']; ?>",
        appId: "<?php echo $firebase_config['appId']; ?>"
    };
    <?php } else { ?>
    const firebaseConfig = null;
    <?php } ?>

    // Initialize Firebase
    let database = null;
    if (firebaseConfig && !firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        database = firebase.database();
    } else if (firebaseConfig) {
        database = firebase.database();
    }
    
    let map;
    let tripPath;
    let markers = [];
    let firebaseListener = null;
    const tripId = "<?php echo $trip['id']; ?>";
    
    function initMap() {
        // Default location
        const defaultLocation = { lat: 0, lng: 0 };
        
        map = new google.maps.Map(document.getElementById("trip-map"), {
            zoom: 13,
            center: defaultLocation,
        });
        
        tripPath = new google.maps.Polyline({
            path: [],
            geodesic: true,
            strokeColor: "#FF0000",
            strokeOpacity: 1.0,
            strokeWeight: 2,
        });
        
        tripPath.setMap(map);
    }
    
    function loadFirebaseData() {
        if (!database) {
            $('#firebase-data').html('<div class="alert alert-warning"><i class="fa fa-warning"></i> Firebase not configured. Real-time data is not available.</div>');
            return;
        }

        $('#firebase-loading').show();
        $('#firebase-data').html('');

        // Reference to the trip data in Firebase
        const tripId = <?php echo $trip['id']; ?>;
        const vehicleId = <?php echo $trip['vechile_id']; ?>;

        <?php if (!empty($trip['firebase_json_url'])) { ?>
        // Use stored Firebase URL to extract path
        const firebaseUrl = "<?php echo $trip['firebase_json_url']; ?>";
        const databaseUrl = database.app.options.databaseURL;
        const firebasePath = firebaseUrl.replace(databaseUrl + '/', '').replace('.json', '');
        console.log('Using stored Firebase path:', firebasePath);
        <?php } else { ?>
        // Generate default path
        const firebasePath = 'trips/trip_' + tripId + '/' + vehicleId;
        console.log('Using generated Firebase path:', firebasePath);
        <?php } ?>

        const tripRef = database.ref(firebasePath);
        
        // Stop any existing listener
        if (firebaseListener) {
            firebaseListener.off();
        }

        // Set up real-time listener
        firebaseListener = tripRef;
        tripRef.on('value', (snapshot) => {
                const data = snapshot.val();
                $('#firebase-loading').hide();

                console.log('Firebase data received:', data);

                if (data) {
                    displayFirebaseData(data);

                    // Check for different possible location data structures
                    if (data.locations) {
                        displayTripRoute(data.locations);
                    } else if (data.latitude && data.longitude) {
                        // Single location point
                        displaySingleLocation(data);
                    } else {
                        // Check if data is an object with timestamp keys
                        const locationEntries = Object.entries(data).filter(([key, value]) =>
                            value && typeof value === 'object' && value.latitude && value.longitude
                        );
                        if (locationEntries.length > 0) {
                            const locations = locationEntries.map(([key, value]) => ({
                                ...value,
                                timestamp: key
                            }));
                            displayTripRoute(locations);
                        }
                    }
                } else {
                    $('#firebase-data').html('<div class="alert alert-info"><i class="fa fa-info-circle"></i> No Firebase data found for this trip. Path: <code>' + firebasePath + '</code></div>');
                }

                // Show stop button for real-time updates
                $('#stop-updates-btn').show();
            }, (error) => {
                $('#firebase-loading').hide();
                console.error('Firebase error:', error);
                $('#firebase-data').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Error loading Firebase data: ' + error.message + '<br>Path: <code>' + firebasePath + '</code></div>');
            });
    }
    
    function displayFirebaseData(data) {
        let html = '<div class="row">';

        // Display raw data structure for debugging
        html += '<div class="col-md-12">';
        html += '<h4><i class="fa fa-database"></i> Firebase Data Structure</h4>';
        html += '<pre style="max-height: 200px; overflow-y: auto; background: #f4f4f4; padding: 10px; border-radius: 4px;">';
        html += JSON.stringify(data, null, 2);
        html += '</pre>';
        html += '</div>';

        // Check for location data
        if (data.latitude && data.longitude) {
            html += '<div class="col-md-6">';
            html += '<h4><i class="fa fa-map-marker"></i> Current Location</h4>';
            html += '<p><strong>Latitude:</strong> ' + data.latitude + '</p>';
            html += '<p><strong>Longitude:</strong> ' + data.longitude + '</p>';
            if (data.speed) html += '<p><strong>Speed:</strong> ' + data.speed + ' km/h</p>';
            if (data.timestamp) html += '<p><strong>Timestamp:</strong> ' + new Date(data.timestamp).toLocaleString() + '</p>';
            html += '</div>';
        }

        // Count location entries
        const locationEntries = Object.entries(data).filter(([key, value]) =>
            value && typeof value === 'object' && value.latitude && value.longitude
        );

        if (locationEntries.length > 0) {
            html += '<div class="col-md-6">';
            html += '<h4><i class="fa fa-list"></i> Location Points</h4>';
            html += '<p><strong>Total Points:</strong> ' + locationEntries.length + '</p>';
            html += '<p><strong>Latest Update:</strong> ' + new Date(Math.max(...locationEntries.map(([key]) => parseInt(key) || 0))).toLocaleString() + '</p>';
            html += '</div>';
        }

        html += '</div>';

        $('#firebase-data').html(html);
    }

    function displaySingleLocation(data) {
        if (!data.latitude || !data.longitude) return;

        const position = {
            lat: parseFloat(data.latitude),
            lng: parseFloat(data.longitude)
        };

        // Clear existing markers
        markers.forEach(marker => marker.setMap(null));
        markers = [];

        // Add marker for current location
        const marker = new google.maps.Marker({
            position: position,
            map: map,
            title: 'Vehicle Location',
            icon: {
                url: 'https://maps.google.com/mapfiles/ms/icons/bus.png',
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        markers.push(marker);

        // Center map on location
        map.setCenter(position);
        map.setZoom(15);

        // Add info window
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>Vehicle Location</h5>
                    <p><strong>Coordinates:</strong> ${data.latitude}, ${data.longitude}</p>
                    ${data.speed ? '<p><strong>Speed:</strong> ' + data.speed + ' km/h</p>' : ''}
                    ${data.timestamp ? '<p><strong>Time:</strong> ' + new Date(data.timestamp).toLocaleString() + '</p>' : ''}
                </div>
            `
        });

        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    }
    
    function displayTripRoute(locations) {
        // Clear existing markers
        markers.forEach(marker => marker.setMap(null));
        markers = [];

        const path = [];
        const bounds = new google.maps.LatLngBounds();

        // Handle both array and object formats
        let locationArray = [];
        if (Array.isArray(locations)) {
            locationArray = locations;
        } else {
            // Convert object to array and sort by timestamp/key
            locationArray = Object.entries(locations)
                .map(([key, value]) => ({...value, timestamp: key}))
                .sort((a, b) => parseInt(a.timestamp) - parseInt(b.timestamp));
        }

        locationArray.forEach((location, index) => {
            if (!location.latitude || !location.longitude) return;

            const position = {
                lat: parseFloat(location.latitude),
                lng: parseFloat(location.longitude)
            };

            path.push(position);
            bounds.extend(position);

            // Add marker for start, end, and current points
            let markerIcon, markerTitle;
            if (index === 0) {
                markerIcon = 'https://maps.google.com/mapfiles/ms/icons/green-dot.png';
                markerTitle = 'Start Point';
            } else if (index === locationArray.length - 1) {
                markerIcon = 'https://maps.google.com/mapfiles/ms/icons/bus.png';
                markerTitle = 'Current Location';
            } else {
                // Skip intermediate points for cleaner display
                return;
            }

            const marker = new google.maps.Marker({
                position: position,
                map: map,
                title: markerTitle,
                icon: {
                    url: markerIcon,
                    scaledSize: new google.maps.Size(32, 32)
                }
            });

            // Add info window
            const infoContent = `
                <div>
                    <h5>${markerTitle}</h5>
                    <p><strong>Coordinates:</strong> ${location.latitude}, ${location.longitude}</p>
                    ${location.speed ? '<p><strong>Speed:</strong> ' + location.speed + ' km/h</p>' : ''}
                    ${location.timestamp ? '<p><strong>Time:</strong> ' + new Date(parseInt(location.timestamp)).toLocaleString() + '</p>' : ''}
                </div>
            `;

            const infoWindow = new google.maps.InfoWindow({
                content: infoContent
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            markers.push(marker);
        });

        // Draw path if we have multiple points
        if (path.length > 1) {
            tripPath.setPath(path);
            map.fitBounds(bounds);
        } else if (path.length === 1) {
            // Single point - center and zoom
            map.setCenter(path[0]);
            map.setZoom(15);
        }
    }

    function stopRealTimeUpdates() {
        if (firebaseListener) {
            firebaseListener.off();
            firebaseListener = null;
            $('#stop-updates-btn').hide();
            $('#firebase-data').append('<div class="alert alert-info" style="margin-top: 10px;"><i class="fa fa-info-circle"></i> Real-time updates stopped. Click "Load Firebase Data" to resume.</div>');
        }
    }

    $(document).ready(function() {
        // Auto-load Firebase data if trip is ongoing
        <?php if ($trip['status'] == 'ongoing') { ?>
            setTimeout(loadFirebaseData, 1000);
        <?php } ?>
    });
</script>
