<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> Trip Details
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-info-circle"></i> Trip Information</h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo site_url('admin/vehicle/trip_list/' . $trip['vechile_id']); ?>" class="btn btn-primary btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to Trip List
                            </a>
                        </div>
                    </div>
                    <div class="box-body">
                        <?php if (!empty($trip)) { ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Vehicle Number:</strong></label>
                                        <p><?php echo $trip['vehicle_no']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Vehicle Model:</strong></label>
                                        <p><?php echo $trip['vehicle_model']; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Driver Name:</strong></label>
                                        <p><?php echo $trip['driver_name'] . ' ' . $trip['driver_surname']; ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Driver Contact:</strong></label>
                                        <p><?php echo $trip['driver_contact']; ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip Start Time:</strong></label>
                                        <p><?php echo date('d-m-Y h:i A', strtotime($trip['trip_start_time'])); ?></p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip End Time:</strong></label>
                                        <p>
                                            <?php 
                                            if ($trip['trip_end_time']) {
                                                echo date('d-m-Y h:i A', strtotime($trip['trip_end_time']));
                                            } else {
                                                echo 'Ongoing';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Status:</strong></label>
                                        <p>
                                            <?php if ($trip['status'] == 'ongoing') { ?>
                                                <span class="label label-success">Ongoing</span>
                                            <?php } else { ?>
                                                <span class="label label-default">Completed</span>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label><strong>Trip Duration:</strong></label>
                                        <p>
                                            <?php 
                                            if ($trip['trip_end_time']) {
                                                $start = new DateTime($trip['trip_start_time']);
                                                $end = new DateTime($trip['trip_end_time']);
                                                $duration = $start->diff($end);
                                                echo $duration->format('%h hours %i minutes');
                                            } else {
                                                $start = new DateTime($trip['trip_start_time']);
                                                $now = new DateTime();
                                                $duration = $start->diff($now);
                                                echo $duration->format('%h hours %i minutes') . ' (ongoing)';
                                            }
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Firebase Configuration Info -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h4><i class="fa fa-info-circle"></i> Firebase Location Data</h4>
                                        <p><strong>Firebase URL:</strong>
                                            <?php if (!empty($trip['firebase_json_url'])) { ?>
                                                <code><?php echo $trip['firebase_json_url']; ?></code>
                                                <br>
                                                <a href="<?php echo $trip['firebase_json_url']; ?>" target="_blank" class="btn btn-xs btn-primary">
                                                    <i class="fa fa-external-link"></i> View in Firebase
                                                </a>
                                            <?php } else { ?>
                                                <code class="text-muted">Firebase URL will be generated when Firebase is configured</code>
                                            <?php } ?>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Firebase Trip Data Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="box box-info">
                                        <div class="box-header with-border">
                                            <h3 class="box-title"><i class="fa fa-map"></i> Trip Route & Firebase Data</h3>
                                            <div class="box-tools pull-right">
                                                <button type="button" class="btn btn-success btn-sm" onclick="loadFirebaseData()">
                                                    <i class="fa fa-refresh"></i> Load Firebase Data
                                                </button>
                                            </div>
                                        </div>
                                        <div class="box-body">
                                            <div id="firebase-loading" style="display: none;">
                                                <p><i class="fa fa-spinner fa-spin"></i> Loading Firebase data...</p>
                                            </div>
                                            <div id="firebase-data">
                                                <p class="text-muted">Click "Load Firebase Data" to fetch real-time trip information.</p>
                                            </div>
                                            <div id="trip-map" style="height: 400px; margin-top: 20px;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        <?php } else { ?>
                            <div class="alert alert-warning">
                                <i class="fa fa-warning"></i> Trip details not found.
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Include Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo $google_maps_api_key; ?>&callback=initMap" async defer></script>

<script>
    // Firebase configuration from database
    <?php if (!empty($firebase_config)) { ?>
    const firebaseConfig = {
        apiKey: "<?php echo $firebase_config['apiKey']; ?>",
        authDomain: "<?php echo $firebase_config['authDomain']; ?>",
        databaseURL: "<?php echo $firebase_config['databaseURL']; ?>",
        projectId: "<?php echo $firebase_config['projectId']; ?>",
        storageBucket: "<?php echo $firebase_config['storageBucket']; ?>",
        messagingSenderId: "<?php echo $firebase_config['messagingSenderId']; ?>",
        appId: "<?php echo $firebase_config['appId']; ?>"
    };
    <?php } else { ?>
    const firebaseConfig = null;
    <?php } ?>

    // Initialize Firebase
    let database = null;
    if (firebaseConfig && !firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        database = firebase.database();
    } else if (firebaseConfig) {
        database = firebase.database();
    }
    
    let map;
    let tripPath;
    let markers = [];
    const tripId = "<?php echo $trip['id']; ?>";
    
    function initMap() {
        // Default location
        const defaultLocation = { lat: 0, lng: 0 };
        
        map = new google.maps.Map(document.getElementById("trip-map"), {
            zoom: 13,
            center: defaultLocation,
        });
        
        tripPath = new google.maps.Polyline({
            path: [],
            geodesic: true,
            strokeColor: "#FF0000",
            strokeOpacity: 1.0,
            strokeWeight: 2,
        });
        
        tripPath.setMap(map);
    }
    
    function loadFirebaseData() {
        if (!database) {
            $('#firebase-data').html('<div class="alert alert-warning"><i class="fa fa-warning"></i> Firebase not configured. Real-time data is not available.</div>');
            return;
        }

        $('#firebase-loading').show();
        $('#firebase-data').html('');

        // Reference to the trip data in Firebase using firebase_json_url
        <?php if (!empty($trip['firebase_json_url'])) { ?>
        // Use direct Firebase URL (remove .json extension for Firebase ref)
        const firebaseUrl = "<?php echo $trip['firebase_json_url']; ?>";
        const firebasePath = firebaseUrl.replace(database.app.options.databaseURL + '/', '').replace('.json', '');
        <?php } else { ?>
        const firebasePath = 'trips/trip_' + tripId + '/<?php echo $trip['vechile_id']; ?>';
        <?php } ?>
        const tripRef = database.ref(firebasePath);
        
        tripRef.once('value')
            .then((snapshot) => {
                const data = snapshot.val();
                $('#firebase-loading').hide();
                
                if (data) {
                    displayFirebaseData(data);
                    if (data.locations) {
                        displayTripRoute(data.locations);
                    }
                } else {
                    $('#firebase-data').html('<div class="alert alert-info"><i class="fa fa-info-circle"></i> No Firebase data found for this trip.</div>');
                }
            })
            .catch((error) => {
                $('#firebase-loading').hide();
                $('#firebase-data').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Error loading Firebase data: ' + error.message + '</div>');
            });
    }
    
    function displayFirebaseData(data) {
        let html = '<div class="row">';
        
        if (data.driver_info) {
            html += '<div class="col-md-6">';
            html += '<h4><i class="fa fa-user"></i> Driver Information</h4>';
            html += '<p><strong>Name:</strong> ' + (data.driver_info.name || 'N/A') + '</p>';
            html += '<p><strong>Phone:</strong> ' + (data.driver_info.phone || 'N/A') + '</p>';
            html += '</div>';
        }
        
        if (data.vehicle_info) {
            html += '<div class="col-md-6">';
            html += '<h4><i class="fa fa-bus"></i> Vehicle Information</h4>';
            html += '<p><strong>Number:</strong> ' + (data.vehicle_info.number || 'N/A') + '</p>';
            html += '<p><strong>Model:</strong> ' + (data.vehicle_info.model || 'N/A') + '</p>';
            html += '</div>';
        }
        
        html += '</div>';
        
        if (data.trip_stats) {
            html += '<div class="row"><div class="col-md-12">';
            html += '<h4><i class="fa fa-bar-chart"></i> Trip Statistics</h4>';
            html += '<div class="row">';
            html += '<div class="col-md-3"><div class="info-box"><span class="info-box-icon bg-blue"><i class="fa fa-road"></i></span><div class="info-box-content"><span class="info-box-text">Distance</span><span class="info-box-number">' + (data.trip_stats.total_distance || '0') + ' km</span></div></div></div>';
            html += '<div class="col-md-3"><div class="info-box"><span class="info-box-icon bg-green"><i class="fa fa-clock-o"></i></span><div class="info-box-content"><span class="info-box-text">Duration</span><span class="info-box-number">' + (data.trip_stats.duration || '0') + ' min</span></div></div></div>';
            html += '<div class="col-md-3"><div class="info-box"><span class="info-box-icon bg-yellow"><i class="fa fa-tachometer"></i></span><div class="info-box-content"><span class="info-box-text">Avg Speed</span><span class="info-box-number">' + (data.trip_stats.avg_speed || '0') + ' km/h</span></div></div></div>';
            html += '<div class="col-md-3"><div class="info-box"><span class="info-box-icon bg-red"><i class="fa fa-tachometer"></i></span><div class="info-box-content"><span class="info-box-text">Max Speed</span><span class="info-box-number">' + (data.trip_stats.max_speed || '0') + ' km/h</span></div></div></div>';
            html += '</div></div></div>';
        }
        
        $('#firebase-data').html(html);
    }
    
    function displayTripRoute(locations) {
        // Clear existing markers
        markers.forEach(marker => marker.setMap(null));
        markers = [];
        
        const path = [];
        const bounds = new google.maps.LatLngBounds();
        
        Object.keys(locations).forEach((key, index) => {
            const location = locations[key];
            const position = {
                lat: parseFloat(location.latitude),
                lng: parseFloat(location.longitude)
            };
            
            path.push(position);
            bounds.extend(position);
            
            // Add marker for start and end points
            if (index === 0 || index === Object.keys(locations).length - 1) {
                const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: index === 0 ? 'Start' : 'End',
                    icon: {
                        url: index === 0 ? 'https://maps.google.com/mapfiles/ms/icons/green-dot.png' : 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
                        scaledSize: new google.maps.Size(32, 32)
                    }
                });
                markers.push(marker);
            }
        });
        
        tripPath.setPath(path);
        map.fitBounds(bounds);
    }
    
    $(document).ready(function() {
        // Auto-load Firebase data if trip is ongoing
        <?php if ($trip['status'] == 'ongoing') { ?>
            setTimeout(loadFirebaseData, 1000);
        <?php } ?>
    });
</script>
