-- Birthday Notification System Database Migration
-- This script creates the necessary tables for automated birthday notifications

-- Create birthday notification settings table
CREATE TABLE IF NOT EXISTS `birthday_notification_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `school_id` int(11) NOT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=enabled, 0=disabled',
  `send_whatsapp` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Send WhatsApp messages',
  `send_push` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Send push notifications',
  `send_notice` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Send user notices',
  `notification_time` time NOT NULL DEFAULT '06:00:00' COMMENT 'Time to send notifications',
  `student_template_whatsapp` text DEFAULT NULL COMMENT 'WhatsApp template for students',
  `student_template_push` text DEFAULT NULL COMMENT 'Push notification template for students',
  `student_template_notice` text DEFAULT NULL COMMENT 'User notice template for students',
  `staff_template_whatsapp` text DEFAULT NULL COMMENT 'WhatsApp template for staff',
  `staff_template_push` text DEFAULT NULL COMMENT 'Push notification template for staff',
  `staff_template_notice` text DEFAULT NULL COMMENT 'User notice template for staff',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_school_settings` (`school_id`),
  KEY `idx_school_enabled` (`school_id`, `is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create birthday notification logs table
CREATE TABLE IF NOT EXISTS `birthday_notification_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `school_id` int(11) NOT NULL,
  `user_type` enum('student','staff') NOT NULL COMMENT 'Type of user',
  `user_id` int(11) NOT NULL COMMENT 'Student or Staff ID',
  `notification_date` date NOT NULL COMMENT 'Date notification was sent',
  `whatsapp_status` enum('pending','sent','failed','disabled') NOT NULL DEFAULT 'pending',
  `push_status` enum('pending','sent','failed','disabled') NOT NULL DEFAULT 'pending',
  `notice_status` enum('pending','sent','failed','disabled') NOT NULL DEFAULT 'pending',
  `whatsapp_response` text DEFAULT NULL COMMENT 'WhatsApp API response',
  `push_response` text DEFAULT NULL COMMENT 'Push notification response',
  `notice_id` int(11) DEFAULT NULL COMMENT 'Created notice ID',
  `phone_number` varchar(20) DEFAULT NULL COMMENT 'Phone number used',
  `app_key` text DEFAULT NULL COMMENT 'App key for push notification',
  `whatsapp_appkey` varchar(255) DEFAULT NULL COMMENT 'WhatsApp API key from whatsapp_config',
  `whatsapp_auth_key` varchar(255) DEFAULT NULL COMMENT 'WhatsApp auth key from whatsapp_config',
  `error_message` text DEFAULT NULL COMMENT 'Error details if any',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_date` (`school_id`, `user_type`, `user_id`, `notification_date`),
  KEY `idx_school_date` (`school_id`, `notification_date`),
  KEY `idx_user_type_id` (`user_type`, `user_id`),
  KEY `idx_status_check` (`whatsapp_status`, `push_status`, `notice_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create birthday notification queue table for processing
CREATE TABLE IF NOT EXISTS `birthday_notification_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `school_id` int(11) NOT NULL,
  `user_type` enum('student','staff') NOT NULL,
  `user_id` int(11) NOT NULL,
  `notification_type` enum('whatsapp','push','notice') NOT NULL,
  `scheduled_date` date NOT NULL,
  `scheduled_time` time NOT NULL,
  `status` enum('pending','processing','completed','failed') NOT NULL DEFAULT 'pending',
  `priority` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=normal, 2=high, 3=urgent',
  `retry_count` int(11) NOT NULL DEFAULT 0,
  `max_retries` int(11) NOT NULL DEFAULT 3,
  `message_content` text DEFAULT NULL,
  `recipient_phone` varchar(20) DEFAULT NULL,
  `recipient_app_key` text DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_school_status` (`school_id`, `status`),
  KEY `idx_scheduled_datetime` (`scheduled_date`, `scheduled_time`),
  KEY `idx_user_type_id` (`user_type`, `user_id`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_retry_processing` (`status`, `retry_count`, `max_retries`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default birthday notification settings for existing schools
INSERT INTO `birthday_notification_settings` 
(`school_id`, `is_enabled`, `send_whatsapp`, `send_push`, `send_notice`, `notification_time`, 
 `student_template_whatsapp`, `student_template_push`, `student_template_notice`,
 `staff_template_whatsapp`, `staff_template_push`, `staff_template_notice`)
SELECT 
    id as school_id,
    1 as is_enabled,
    1 as send_whatsapp,
    1 as send_push,
    1 as send_notice,
    '06:00:00' as notification_time,
    ' Happy Birthday {name}! \n\nWishing you a wonderful day filled with happiness and joy. May this new year of your life bring you success, good health, and lots of memorable moments.\n\nBest wishes from {school_name} family! ' as student_template_whatsapp,
    'Happy Birthday {name}!  Wishing you a fantastic day ahead!' as student_template_push,
    'Dear {name},\n\nOn behalf of {school_name}, we wish you a very Happy Birthday! \n\nMay this special day bring you joy, happiness, and wonderful memories. We hope the year ahead is filled with success, good health, and amazing achievements.\n\nEnjoy your special day!\n\nWarm regards,\n{school_name}' as student_template_notice,
    ' Happy Birthday {name}! \n\nWishing you a wonderful day filled with happiness and joy. Thank you for your dedication and hard work at {school_name}. May this new year bring you success, good health, and prosperity.\n\nBest wishes from the {school_name} team! ' as staff_template_whatsapp,
    'Happy Birthday {name}!  Thank you for your dedication to {school_name}!' as staff_template_push,
    'Dear {name},\n\nOn behalf of {school_name}, we wish you a very Happy Birthday! \n\nThank you for your continued dedication and valuable contributions to our school community. Your hard work and commitment make a real difference in the lives of our students.\n\nMay this special day bring you joy, happiness, and wonderful memories. We hope the year ahead is filled with success, good health, and personal fulfillment.\n\nEnjoy your special day!\n\nWarm regards,\n{school_name} Management' as staff_template_notice
FROM `sch_settings`
WHERE NOT EXISTS (
    SELECT 1 FROM `birthday_notification_settings` 
    WHERE `birthday_notification_settings`.`school_id` = `sch_settings`.`id`
);

-- Create indexes for better performance
CREATE INDEX `idx_students_dob_active` ON `students` (`dob`, `is_active`, `school_id`);
CREATE INDEX `idx_staff_dob_active` ON `staff` (`dob`, `is_active`, `school_id`);

-- Add birthday notification permission to existing roles if not exists
INSERT IGNORE INTO `permission_category` (`id`, `perm_group_id`, `name`, `short_code`, `enable_view`, `enable_add`, `enable_edit`, `enable_delete`, `created_at`) 
VALUES (NULL, 1, 'Birthday Notifications', 'birthday_notifications', 1, 1, 1, 1, NOW());

-- Get the permission category ID for birthday notifications
SET @perm_cat_id = (SELECT id FROM `permission_category` WHERE `short_code` = 'birthday_notifications' LIMIT 1);

-- Add specific permissions for birthday notifications
INSERT IGNORE INTO `permissions` (`id`, `permission_category_id`, `name`, `short_code`, `enable_view`, `enable_add`, `enable_edit`, `enable_delete`, `created_at`) 
VALUES 
(NULL, @perm_cat_id, 'Birthday Notification Settings', 'birthday_notification_settings', 1, 0, 1, 0, NOW()),
(NULL, @perm_cat_id, 'Birthday Notification Logs', 'birthday_notification_logs', 1, 0, 0, 1, NOW()),
(NULL, @perm_cat_id, 'Send Birthday Notifications', 'send_birthday_notifications', 1, 1, 0, 0, NOW());

-- Create view for easy birthday queries
CREATE OR REPLACE VIEW `birthday_users_today` AS
SELECT 
    'student' as user_type,
    s.id as user_id,
    s.school_id,
    CONCAT(s.firstname, ' ', IFNULL(s.middlename, ''), ' ', IFNULL(s.lastname, '')) as full_name,
    s.firstname,
    s.middlename,
    s.lastname,
    s.dob,
    s.mobileno as phone,
    s.email,
    s.app_key,
    s.is_active,
    ss.session_id,
    c.class,
    sec.section,
    YEAR(CURDATE()) - YEAR(s.dob) as age
FROM students s
LEFT JOIN student_session ss ON s.id = ss.student_id
LEFT JOIN classes c ON ss.class_id = c.id
LEFT JOIN sections sec ON ss.section_id = sec.id
WHERE s.is_active = 'yes' 
    AND MONTH(s.dob) = MONTH(CURDATE()) 
    AND DAY(s.dob) = DAY(CURDATE())
    AND s.dob IS NOT NULL

UNION ALL

SELECT 
    'staff' as user_type,
    st.id as user_id,
    st.school_id,
    CONCAT(st.name, ' ', IFNULL(st.surname, '')) as full_name,
    st.name as firstname,
    '' as middlename,
    st.surname as lastname,
    st.dob,
    st.contact_no as phone,
    st.email,
    st.app_key,
    st.is_active,
    NULL as session_id,
    NULL as class,
    NULL as section,
    YEAR(CURDATE()) - YEAR(st.dob) as age
FROM staff st
WHERE st.is_active = 1 
    AND MONTH(st.dob) = MONTH(CURDATE()) 
    AND DAY(st.dob) = DAY(CURDATE())
    AND st.dob IS NOT NULL;

-- Create stored procedure for birthday notification processing
DELIMITER //

CREATE PROCEDURE `ProcessBirthdayNotifications`(IN target_school_id INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_user_type VARCHAR(10);
    DECLARE v_user_id INT;
    DECLARE v_school_id INT;
    DECLARE v_full_name VARCHAR(255);
    DECLARE v_phone VARCHAR(20);
    DECLARE v_app_key TEXT;
    DECLARE v_email VARCHAR(255);
    
    -- Cursor for today's birthdays
    DECLARE birthday_cursor CURSOR FOR 
        SELECT user_type, user_id, school_id, full_name, phone, app_key, email
        FROM birthday_users_today 
        WHERE (target_school_id = 0 OR school_id = target_school_id)
        AND is_active IN ('yes', 1);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- Open cursor and process each birthday user
    OPEN birthday_cursor;
    
    birthday_loop: LOOP
        FETCH birthday_cursor INTO v_user_type, v_user_id, v_school_id, v_full_name, v_phone, v_app_key, v_email;
        
        IF done THEN
            LEAVE birthday_loop;
        END IF;
        
        -- Check if notification already sent today
        IF NOT EXISTS (
            SELECT 1 FROM birthday_notification_logs 
            WHERE school_id = v_school_id 
                AND user_type = v_user_type 
                AND user_id = v_user_id 
                AND notification_date = CURDATE()
        ) THEN
            -- Insert log entry
            INSERT INTO birthday_notification_logs 
            (school_id, user_type, user_id, notification_date, phone_number, app_key)
            VALUES (v_school_id, v_user_type, v_user_id, CURDATE(), v_phone, v_app_key);
            
            -- Add to notification queue
            INSERT INTO birthday_notification_queue 
            (school_id, user_type, user_id, notification_type, scheduled_date, scheduled_time, recipient_phone, recipient_app_key)
            SELECT 
                v_school_id, v_user_type, v_user_id, 'whatsapp', CURDATE(), 
                IFNULL(bns.notification_time, '06:00:00'), v_phone, v_app_key
            FROM birthday_notification_settings bns 
            WHERE bns.school_id = v_school_id AND bns.is_enabled = 1 AND bns.send_whatsapp = 1;
            
            INSERT INTO birthday_notification_queue 
            (school_id, user_type, user_id, notification_type, scheduled_date, scheduled_time, recipient_phone, recipient_app_key)
            SELECT 
                v_school_id, v_user_type, v_user_id, 'push', CURDATE(), 
                IFNULL(bns.notification_time, '06:00:00'), v_phone, v_app_key
            FROM birthday_notification_settings bns 
            WHERE bns.school_id = v_school_id AND bns.is_enabled = 1 AND bns.send_push = 1;
            
            INSERT INTO birthday_notification_queue 
            (school_id, user_type, user_id, notification_type, scheduled_date, scheduled_time, recipient_phone, recipient_app_key)
            SELECT 
                v_school_id, v_user_type, v_user_id, 'notice', CURDATE(), 
                IFNULL(bns.notification_time, '06:00:00'), v_phone, v_app_key
            FROM birthday_notification_settings bns 
            WHERE bns.school_id = v_school_id AND bns.is_enabled = 1 AND bns.send_notice = 1;
        END IF;
        
    END LOOP;
    
    CLOSE birthday_cursor;
    
END //

DELIMITER ;

-- Grant necessary permissions
-- Note: Adjust these based on your database user setup
-- GRANT SELECT, INSERT, UPDATE ON birthday_notification_settings TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE ON birthday_notification_logs TO 'your_app_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON birthday_notification_queue TO 'your_app_user'@'localhost';
-- GRANT EXECUTE ON PROCEDURE ProcessBirthdayNotifications TO 'your_app_user'@'localhost';

-- Create trigger to automatically clean old logs (optional - keeps last 90 days)
DELIMITER //

CREATE EVENT IF NOT EXISTS `CleanOldBirthdayLogs`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    DELETE FROM birthday_notification_logs 
    WHERE notification_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
    
    DELETE FROM birthday_notification_queue 
    WHERE scheduled_date < DATE_SUB(CURDATE(), INTERVAL 7 DAY) 
    AND status IN ('completed', 'failed');
END //

DELIMITER ;

-- Enable event scheduler if not already enabled
-- SET GLOBAL event_scheduler = ON;

-- ALTER TABLE statements for existing installations
-- Add WhatsApp config fields to birthday_notification_logs table
ALTER TABLE `birthday_notification_logs`
ADD COLUMN IF NOT EXISTS `whatsapp_appkey` varchar(255) DEFAULT NULL COMMENT 'WhatsApp API key from whatsapp_config' AFTER `app_key`,
ADD COLUMN IF NOT EXISTS `whatsapp_auth_key` varchar(255) DEFAULT NULL COMMENT 'WhatsApp auth key from whatsapp_config' AFTER `whatsapp_appkey`;

-- Insert sample test data (uncomment for testing)
/*
-- Test student with today's birthday
INSERT INTO students (school_id, firstname, lastname, dob, mobileno, email, is_active, admission_no)
VALUES (1, 'Test', 'Student', CURDATE(), '1234567890', '<EMAIL>', 'yes', 'TEST001');

-- Test staff with today's birthday
INSERT INTO staff (school_id, name, surname, dob, contact_no, email, is_active, employee_id)
VALUES (1, 'Test', 'Staff', CURDATE(), '0987654321', '<EMAIL>', 1, 'STAFF001');
*/
