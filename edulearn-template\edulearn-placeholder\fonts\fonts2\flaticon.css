	/*
  	Flaticon icon font: Flaticon
  	Creation date: 23/04/2018 13:00
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
font-style: normal;
}

.flaticon-placeholder:before { content: "\f100"; }
.flaticon-email-1:before { content: "\f101"; }
.flaticon-email:before { content: "\f102"; }
.flaticon-message:before { content: "\f103"; }
.flaticon-opened-email-envelope:before { content: "\f104"; }
.flaticon-phone-call:before { content: "\f105"; }