<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Birthday_notification_service
{
    private $CI;
    private $school_id;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model(['birthday_notification_model', 'setting_model', 'notification_model']);
        $this->CI->load->library(['whatsappgateway', 'pushnotification', 'mailsmsconf']);
        $this->school_id = $this->CI->session->userdata('school_id');
    }

    /**
     * Process all pending birthday notifications
     */
    public function processPendingNotifications($school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }

        $pending_notifications = $this->CI->birthday_notification_model->getPendingNotifications();
        $processed_count = 0;
        $failed_count = 0;

        foreach ($pending_notifications as $notification) {
            try {
                // Check if user already received this type of notification today
                if ($this->hasReceivedNotificationToday($notification->school_id, $notification->user_type, $notification->user_id, $notification->notification_type)) {
                    $this->CI->birthday_notification_model->updateQueueStatus($notification->id, 'skipped', 'Notification already sent today');
                    continue;
                }

                // Update status to processing
                $this->CI->birthday_notification_model->updateQueueStatus($notification->id, 'processing');

                $success = false; 
                switch ($notification->notification_type) {
                    case 'whatsapp':
                        $success = $this->sendWhatsAppNotification($notification);
                        break;
                    case 'push':
                        $success = $this->sendPushNotification($notification);
                        break;
                    case 'notice':
                        $success = $this->sendUserNotice($notification);
                        break;
                }

                if ($success) {
                    $this->CI->birthday_notification_model->updateQueueStatus($notification->id, 'completed');
                    $processed_count++;
                } else {
                    $this->CI->birthday_notification_model->updateQueueStatus($notification->id, 'failed', 'Notification delivery failed');
                    $failed_count++;
                }

            } catch (Exception $e) {
                $this->CI->birthday_notification_model->updateQueueStatus($notification->id, 'failed', $e->getMessage());
                $failed_count++;
                log_message('error', 'Birthday notification failed: ' . $e->getMessage());
            }
        }

        return [
            'processed' => $processed_count,
            'failed' => $failed_count,
            'total' => count($pending_notifications)
        ];
    }

    /**
     * Get WhatsApp configuration for school
     */
    private function getWhatsAppConfig($school_id)
    {
        $this->CI->db->select('app_key, auth_key');
        $this->CI->db->from('whatsapp_config');
        $this->CI->db->where('school_id', $school_id);
        $this->CI->db->where('is_active', 'enabled');
        $query = $this->CI->db->get();   
        if ($query->num_rows() > 0) {
            return $query->row();
        } 
        return null;
    }

    /**
     * Send WhatsApp birthday notification
     */
    private function sendWhatsAppNotification($notification)
    {
        
        try {
            // Get user data
            $user_data = $this->getUserData($notification->user_type, $notification->user_id);
            if (!$user_data || empty($user_data->phone)) {
                $this->logNotificationResult($notification, 'whatsapp', 'failed', null, 'No phone number available', null, null);
                return false;
            }

            // Get WhatsApp configuration for this school
            $whatsapp_config = $this->getWhatsAppConfig($notification->school_id);
            if (!$whatsapp_config) {
                $this->logNotificationResult($notification, 'whatsapp', 'failed', null, 'WhatsApp configuration not found for school', null, null);
                return false;
            }

            // Get settings and template
            $settings = $this->CI->birthday_notification_model->getSettings($notification->school_id);
            $template_field = $notification->user_type . '_template_whatsapp';
            $template = $settings->$template_field ?? $this->getDefaultTemplate('whatsapp', $notification->user_type);

            // Process template
            $school_data = $this->CI->setting_model->getSetting($notification->school_id);
            $message = $this->CI->birthday_notification_model->processTemplate($template, $user_data, $school_data);  
            // Send WhatsApp message 
            
            try {
                if ($whatsapp_config) {
                    $result = $this->sendBirthdayWhatsAppSMS($user_data->phone, $message, $whatsapp_config->auth_key, $whatsapp_config->app_key);
                    if ($result) {
                        $this->logNotificationResult($notification, 'whatsapp', 'sent', json_encode($result), null, null, $whatsapp_config);
                        return true;
                    } else {
                        $this->logNotificationResult($notification, 'whatsapp', 'failed', null, 'WhatsApp API failed', null, $whatsapp_config);
                        return false;
                    }
                }
            } catch (Exception $e) {
                $whatsapp_config = $this->getWhatsAppConfig($notification->school_id);
                $this->logNotificationResult($notification, 'whatsapp', 'failed', null, $e->getMessage(), null, $whatsapp_config);
                return false;
            }

        } catch (Exception $e) {
            log_message('error', 'sendWhatsAppNotification Exception: ' . $e->getMessage());
            return false;
        }
    }

    
    /**
     * Send birthday WhatsApp message using the API
     * 
     * @param string $phone_number Recipient's phone number
     * @param string $message Message content
     * @param string $auth_key Authentication key for the WhatsApp API
     * @param string $app_key Application key for the WhatsApp API
     * @return mixed API response or false on failure
     */
    private function sendBirthdayWhatsAppSMS($phone_number, $message, $auth_key, $app_key)
    {  
        try {
            // Initialize cURL session
            $curl = curl_init();
            
            // Set cURL options
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://xmsgpro.icu/api/create-message',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array(
                    'appkey' => $app_key,
                    'authkey' => $auth_key,
                    'to' => '91'.$phone_number,
                    'message' => $message,
                    'sandbox' => 'false'
                ),
            ));
            
            // Execute cURL request
            $response = curl_exec($curl);
            $err = curl_error($curl);
            
            // Close cURL session
            curl_close($curl);
            
            if ($err) {
                log_message('error', 'Birthday WhatsApp API cURL Error: ' . $err);
                return false;
            }
            
            // Decode and return the response
            $result = json_decode($response); 
            return $result;
            
        } catch (Exception $e) {
            log_message('error', 'Birthday WhatsApp API Exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send push notification
     */
    private function sendPushNotification($notification)
    {
        try {
            // Get user data
            $user_data = $this->getUserData($notification->user_type, $notification->user_id);
            if (!$user_data || empty($user_data->app_key)) {
                $this->logNotificationResult($notification, 'push', 'failed', null, 'No app key available');
                return false;
            }

            // Get settings and template
            $settings = $this->CI->birthday_notification_model->getSettings($notification->school_id);
            $template_field = $notification->user_type . '_template_push';
            $template = $settings->$template_field ?? $this->getDefaultTemplate('push', $notification->user_type);

            // Process template
            $school_data = $this->CI->setting_model->getSetting($notification->school_id);
            $message = $this->CI->birthday_notification_model->processTemplate($template, $user_data, $school_data);

            // Prepare push notification data
            $push_data = [
                'title' => 'Happy Birthday! ',
                'body' => $message
            ];

            // Send push notification
            $result = $this->CI->pushnotification->send($user_data->app_key, $push_data);
            
            if ($result) {
                $this->logNotificationResult($notification, 'push', 'sent', json_encode($result));
                return true;
            } else {
                $this->logNotificationResult($notification, 'push', 'failed', null, 'Push notification failed');
                return false;
            }

        } catch (Exception $e) {
            $this->logNotificationResult($notification, 'push', 'failed', null, $e->getMessage());
            return false;
        }
    }

    /**
     * Send user notice
     */
    private function sendUserNotice($notification)
    {
        try {
            // Get user data
            $user_data = $this->getUserData($notification->user_type, $notification->user_id);
            if (!$user_data) {
                $this->logNotificationResult($notification, 'notice', 'failed', null, 'User data not found');
                return false;
            }

            // Get settings and template
            $settings = $this->CI->birthday_notification_model->getSettings($notification->school_id);
            $template_field = $notification->user_type . '_template_notice';
            $template = $settings->$template_field ?? $this->getDefaultTemplate('notice', $notification->user_type);

            // Process template
            $school_data = $this->CI->setting_model->getSetting($notification->school_id);
            $message = $this->CI->birthday_notification_model->processTemplate($template, $user_data, $school_data);

            // Create user notice
            $notice_data = [
                'title' => 'Happy Birthday! ',
                'message' => $message,
                'publish_date' => date('Y-m-d'),
                'date' => date('Y-m-d H:i:s'),
                'visible_student' => $notification->user_type === 'student' ? 'Yes' : 'No',
                'visible_staff' => $notification->user_type === 'staff' ? 'Yes' : 'No',
                'visible_parent' => 'No',
                'created_by' => 'Birthday Notification System',
                'is_active' => 'yes',
                'created_id' => 1, // System generated
                'school_id' => $notification->school_id
            ];

            // Insert notification
            $this->CI->db->insert('send_notification', $notice_data);
            $notice_id = $this->CI->db->insert_id();

            if ($notice_id) {
                // For staff notifications, we need to add role-based targeting
                if ($notification->user_type === 'staff') {
                    // Get staff role_id from staff_roles table
                    $this->CI->db->select('role_id');
                    $this->CI->db->from('staff_roles');
                    $this->CI->db->where('staff_id', $notification->user_id);
                    $this->CI->db->where('is_active', 1);
                    $staff_roles_query = $this->CI->db->get();

                    if ($staff_roles_query->num_rows() > 0) {
                        // Staff can have multiple roles, so insert all of them
                        $staff_roles = $staff_roles_query->result();
                        foreach ($staff_roles as $staff_role) {
                            $role_data = [
                                'send_notification_id' => $notice_id,
                                'role_id' => $staff_role->role_id
                            ];
                            $this->CI->db->insert('notification_roles', $role_data);
                        }
                    }
                }
                // For students, the visible_student = 'Yes' is sufficient

                $this->logNotificationResult($notification, 'notice', 'sent', null, null, $notice_id);
                return true;
            } else {
                $this->logNotificationResult($notification, 'notice', 'failed', null, 'Failed to create notice');
                return false;
            }

        } catch (Exception $e) {
            $this->logNotificationResult($notification, 'notice', 'failed', null, $e->getMessage());
            return false;
        }
    }

    /**
     * Get user data based on type and ID
     */
    private function getUserData($user_type, $user_id)
    {
        if ($user_type === 'student') {
            $this->CI->db->select('s.*, 
                                  CONCAT(s.firstname, " ", IFNULL(s.middlename, ""), " ", IFNULL(s.lastname, "")) as full_name,
                                  s.mobileno as phone, s.admission_no as user_identifier,
                                  c.class, sec.section, YEAR(CURDATE()) - YEAR(s.dob) as age');
            $this->CI->db->from('students s');
            $this->CI->db->join('student_session ss', 's.id = ss.student_id', 'left');
            $this->CI->db->join('classes c', 'ss.class_id = c.id', 'left');
            $this->CI->db->join('sections sec', 'ss.section_id = sec.id', 'left');
            $this->CI->db->where('s.id', $user_id);
            $this->CI->db->where('s.is_active', 'yes');
        } else {
            $this->CI->db->select('st.*, 
                                  CONCAT(st.name, " ", IFNULL(st.surname, "")) as full_name,
                                  st.contact_no as phone, st.employee_id as user_identifier,
                                  YEAR(CURDATE()) - YEAR(st.dob) as age');
            $this->CI->db->from('staff st');
            $this->CI->db->where('st.id', $user_id);
            $this->CI->db->where('st.is_active', 1);
        }

        $query = $this->CI->db->get();
        return $query->row();
    }

    /**
     * Log notification result
     */
    private function logNotificationResult($notification, $type, $status, $response = null, $error = null, $notice_id = null, $whatsapp_config = null)
    {
        $log_data = [
            'school_id' => $notification->school_id,
            'user_type' => $notification->user_type,
            'user_id' => $notification->user_id,
            'notification_date' => $notification->scheduled_date,
            'phone_number' => $notification->recipient_phone,
            'app_key' => $notification->recipient_app_key
        ];

        if ($type === 'whatsapp') {
            $log_data['whatsapp_status'] = $status;
            $log_data['whatsapp_response'] = $response;

            // Add WhatsApp config if provided
            if ($whatsapp_config) {
                $log_data['whatsapp_appkey'] = $whatsapp_config->app_key;
                $log_data['whatsapp_auth_key'] = $whatsapp_config->auth_key;
            }
        } elseif ($type === 'push') {
            $log_data['push_status'] = $status;
            $log_data['push_response'] = $response;
        } elseif ($type === 'notice') {
            $log_data['notice_status'] = $status;
            $log_data['notice_id'] = $notice_id;
        }

        if ($error) {
            $log_data['error_message'] = $error;
        }

        $this->CI->birthday_notification_model->logNotification($log_data);
    }

    /**
     * Get default template
     */
    private function getDefaultTemplate($type, $user_type)
    {
        $templates = [
            'whatsapp' => [
                'student' => ' Happy Birthday {name}! \n\nWishing you a wonderful day filled with happiness and joy. May this new year of your life bring you success, good health, and lots of memorable moments.\n\nBest wishes from {school_name} family! ',
                'staff' => ' Happy Birthday {name}! \n\nWishing you a wonderful day filled with happiness and joy. Thank you for your dedication and hard work at {school_name}. May this new year bring you success, good health, and prosperity.\n\nBest wishes from the {school_name} team! '
            ],
            'push' => [
                'student' => 'Happy Birthday {name}!  Wishing you a fantastic day ahead!',
                'staff' => 'Happy Birthday {name}!  Thank you for your dedication to {school_name}!'
            ],
            'notice' => [
                'student' => 'Dear {name},\n\nOn behalf of {school_name}, we wish you a very Happy Birthday! \n\nMay this special day bring you joy, happiness, and wonderful memories. We hope the year ahead is filled with success, good health, and amazing achievements.\n\nEnjoy your special day!\n\nWarm regards,\n{school_name}',
                'staff' => 'Dear {name},\n\nOn behalf of {school_name}, we wish you a very Happy Birthday! \n\nThank you for your continued dedication and valuable contributions to our school community. Your hard work and commitment make a real difference in the lives of our students.\n\nMay this special day bring you joy, happiness, and wonderful memories. We hope the year ahead is filled with success, good health, and personal fulfillment.\n\nEnjoy your special day!\n\nWarm regards,\n{school_name} Management'
            ]
        ];

        return $templates[$type][$user_type] ?? 'Happy Birthday {name}!';
    }

    /**
     * Schedule birthday notifications for today
     */
    public function scheduleTodaysBirthdays($school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }

        $settings = $this->CI->birthday_notification_model->getSettings($school_id);
        if (!$settings->is_enabled) {
            return ['message' => 'Birthday notifications are disabled for this school'];
        }

        $today_birthdays = $this->CI->birthday_notification_model->getTodaysBirthdays($school_id); 
        $scheduled_count = 0;

        foreach ($today_birthdays as $birthday_user) {
            // Check if already scheduled/sent today
            if ($this->CI->birthday_notification_model->isNotificationSentToday($school_id, $birthday_user->user_type, $birthday_user->user_id)) {
                continue;
            }

            // Schedule WhatsApp notification
            if ($settings->send_whatsapp && !empty($birthday_user->phone)) {
                $this->CI->birthday_notification_model->addToQueue([
                    'school_id' => $school_id,
                    'user_type' => $birthday_user->user_type,
                    'user_id' => $birthday_user->user_id,
                    'notification_type' => 'whatsapp',
                    'scheduled_date' => date('Y-m-d'),
                    'scheduled_time' => $settings->notification_time,
                    'recipient_phone' => $birthday_user->phone,
                    'recipient_app_key' => $birthday_user->app_key
                ]);
                $scheduled_count++;
            }

            // Schedule push notification
            if ($settings->send_push && !empty($birthday_user->app_key)) {
                $this->CI->birthday_notification_model->addToQueue([
                    'school_id' => $school_id,
                    'user_type' => $birthday_user->user_type,
                    'user_id' => $birthday_user->user_id,
                    'notification_type' => 'push',
                    'scheduled_date' => date('Y-m-d'),
                    'scheduled_time' => $settings->notification_time,
                    'recipient_phone' => $birthday_user->phone,
                    'recipient_app_key' => $birthday_user->app_key
                ]);
                $scheduled_count++;
            }

            // Schedule user notice
            if ($settings->send_notice) {
                $this->CI->birthday_notification_model->addToQueue([
                    'school_id' => $school_id,
                    'user_type' => $birthday_user->user_type,
                    'user_id' => $birthday_user->user_id,
                    'notification_type' => 'notice',
                    'scheduled_date' => date('Y-m-d'),
                    'scheduled_time' => $settings->notification_time,
                    'recipient_phone' => $birthday_user->phone,
                    'recipient_app_key' => $birthday_user->app_key
                ]);
                $scheduled_count++;
            }
        }

        return [
            'birthday_users' => count($today_birthdays),
            'scheduled_notifications' => $scheduled_count,
            'message' => "Scheduled {$scheduled_count} birthday notifications for " . count($today_birthdays) . " users"
        ];
    }

    /**
     * Check if user already received birthday notification today
     */
    private function hasReceivedNotificationToday($school_id, $user_type, $user_id, $notification_type)
    {
        $today = date('Y-m-d');

        $this->CI->db->select('id');
        $this->CI->db->from('birthday_notification_logs');
        $this->CI->db->where('school_id', $school_id);
        $this->CI->db->where('user_type', $user_type);
        $this->CI->db->where('user_id', $user_id);
        $this->CI->db->where('notification_type', $notification_type);
        $this->CI->db->where('DATE(created_at)', $today);
        $this->CI->db->where('notice_status', 'sent');

        $query = $this->CI->db->get();
        return $query->num_rows() > 0;
    }

    /**
     * Send immediate birthday notification (for testing or manual sending)
     */
    public function sendImmediateBirthdayNotification($user_type, $user_id, $notification_types = ['whatsapp', 'push', 'notice'], $school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }

        $results = [];
        $user_data = $this->getUserData($user_type, $user_id);

        if (!$user_data) {
            return ['error' => 'User not found'];
        }

        foreach ($notification_types as $type) {
            // Check if user already received this type of notification today
            if ($this->hasReceivedNotificationToday($school_id, $user_type, $user_id, $type)) {
                $results[$type] = [
                    'success' => false,
                    'message' => 'Birthday notification already sent today',
                    'duplicate' => true
                ];
                continue;
            }

            $notification = (object) [
                'id' => 0,
                'school_id' => $school_id,
                'user_type' => $user_type,
                'user_id' => $user_id,
                'notification_type' => $type,
                'scheduled_date' => date('Y-m-d'),
                'scheduled_time' => date('H:i:s'),
                'recipient_phone' => $user_data->phone,
                'recipient_app_key' => $user_data->app_key
            ];

            switch ($type) {
                case 'whatsapp':
                    $results[$type] = $this->sendWhatsAppNotification($notification);
                    break;
                case 'push':
                    $results[$type] = $this->sendPushNotification($notification);
                    break;
                case 'notice':
                    $results[$type] = $this->sendUserNotice($notification);
                    break;
            }
        }

        return $results;
    }
}
