<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-bus"></i> <?php echo $this->lang->line('transport'); ?>
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-map-marker"></i> <?php echo $this->lang->line('live_tracking'); ?> - <?php echo $vehicle['vehicle_no']; ?></h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo site_url('admin/vehicle'); ?>" class="btn btn-primary btn-sm"><i class="fa fa-arrow-left"></i> <?php echo $this->lang->line('back'); ?></a>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="box box-widget widget-user-2">
                                    <div class="widget-user-header bg-blue">
                                        <h4><?php echo $vehicle['vehicle_no']; ?> - <?php echo $vehicle['vehicle_model']; ?></h4>
                                    </div>
                                    <div class="box-footer no-padding">
                                        <ul class="nav nav-stacked">
                                            <li><a><strong><?php echo $this->lang->line('driver'); ?>:</strong> <?php echo $vehicle['driver_name']; ?></a></li>
                                            <li><a><strong><?php echo $this->lang->line('contact'); ?>:</strong> <?php echo $vehicle['driver_contact']; ?></a></li>
                                            <li><a><strong><?php echo $this->lang->line('license'); ?>:</strong> <?php echo $vehicle['driver_licence']; ?></a></li>
                                            <?php if (!empty($active_trip)) { ?>
                                                <li><a><strong><?php echo $this->lang->line('trip_started'); ?>:</strong> <?php echo date($this->customlib->getSchoolDateFormat(), strtotime($active_trip['trip_start_time'])); ?></a></li>
                                                <li><a><strong><?php echo $this->lang->line('status'); ?>:</strong> <span class="label label-success">Active</span></a></li>
                                            <?php } else { ?>
                                                <li><a><strong><?php echo $this->lang->line('status'); ?>:</strong> <span class="label label-default">No Active Trip</span></a></li>
                                            <?php } ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <?php if (!empty($active_trip)) { ?>
                                    <div id="map" style="height: 400px; width: 100%;"></div>
                                    <input type="hidden" id="trip_id" value="<?php echo $active_trip['id']; ?>">
                                <?php } else { ?>
                                    <div class="alert alert-info">
                                        <h4><i class="fa fa-info-circle"></i> <?php echo $this->lang->line('info'); ?></h4>
                                        <?php echo $this->lang->line('no_active_trip_message'); ?>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php if (!empty($active_trip)) { ?>
<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>

<script type="text/javascript">
    // Initialize Firebase
    const firebaseConfig = {
        apiKey: "YOUR_FIREBASE_API_KEY",
        authDomain: "YOUR_FIREBASE_AUTH_DOMAIN",
        databaseURL: "YOUR_FIREBASE_DATABASE_URL",
        projectId: "YOUR_FIREBASE_PROJECT_ID",
        storageBucket: "YOUR_FIREBASE_STORAGE_BUCKET",
        messagingSenderId: "YOUR_FIREBASE_MESSAGING_SENDER_ID",
        appId: "YOUR_FIREBASE_APP_ID"
    };
    
    firebase.initializeApp(firebaseConfig);
    
    // Get trip ID
    const tripId = document.getElementById('trip_id').value;
    
    // Initialize map
    let map;
    let marker;
    
    function initMap() {
        map = new google.maps.Map(document.getElementById("map"), {
            center: { lat: 20.5937, lng: 78.9629 }, // Default to center of India
            zoom: 15,
        });
        
        marker = new google.maps.Marker({
            map: map,
            icon: {
                url: '<?php echo base_url("uploads/bus_icon.png"); ?>',
                scaledSize: new google.maps.Size(50, 50)
            },
            title: '<?php echo $vehicle["vehicle_no"]; ?>'
        });
        
        // Listen for location updates from Firebase
        const tripRef = firebase.database().ref('trips/' + tripId + '/location');
        tripRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                const position = new google.maps.LatLng(data.latitude, data.longitude);
                marker.setPosition(position);
                map.setCenter(position);
                
                // Update additional info if needed
                if (data.speed) {
                    document.getElementById('vehicle_speed').textContent = data.speed + ' km/h';
                }
            }
        });
    }
</script>
<?php } ?>