<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

$config['ci_balance_group']         = 'Balance Master';
$config['ci_balance_type']          = 'Previous Session Balance';
$config['ci_blog_theme']            = 'default';
$config['ci_front_page_url']        = 'page/';
$config['ci_front_page_read_url']   = 'read/';
$config['ci_front_event_content']   = 'events';
$config['ci_front_notice_content']  = 'notice';
$config['ci_front_gallery_content'] = 'gallery';
$config['ci_front_banner_content']  = 'banner';
$config['ci_front_home_page_slug']  = 'home';
$config['ci_front_themes']          = array(
    'kidsa'          => 'theme_kidsa.jpg',
    'edulearn'       => 'theme_edulearn.jpg',
);
// 'shadow_white'  => 'theme_shadow_white.jpg',
$config['ci_course_detail_url'] = 'course/coursedetail/';

