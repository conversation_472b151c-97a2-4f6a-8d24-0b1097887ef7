/*!
 * Documenter 2.0
 * http://rxa.li/documenter
 *
 * Copyright 2013, Xaver Birsak
 * http://revaxarts.com
 *
 */
html, body{max-width:100%;}
body{ padding-bottom: 40px;}

#documenter_content{
	padding-top: 200px;
}
.masthead h1{
	margin-top: 48px;
	line-height: 50px;
}

section h3,
section h4,
section h5,
section h6 {
	padding: 0;
	clear: both;
	line-height: 1.2em;
	margin-top: 2em;
}
section h3{ font-size:2em;}
section h4{ font-size:1.5em;}
section h5{ font-size:1.3em;}
section h6{ font-size:1.1em;}

section table{
	border-top:1px solid;
	margin: 18px 0 18px;
}
section table td, section table th{
	border-bottom:1px solid;
	text-align:left;
	padding: 10px 10px 10px 3px;
}
section table th{
	font-weight: 900;
}

hr.notop{
	margin-top:3px;
}

.img-jhilik {max-width: 100%; border: 1px solid #ddd; padding: 10px; margin: 30px 0;}
	
footer{
	padding-bottom:500px;
}
.container{
	padding-bottom:3px;
	position: relative;
}
.navbar a.brand{
	display:inline-block;
	width:200px;
	background-position:center left;
	background-repeat:no-repeat;
	background-size: contain;
	text-indent:-9999px;
	margin:0;
	padding:0;
	top: 0;
	bottom: 0;
	position: absolute;
}

.page-header{
	border:0;
}
.navbar .nav {
	margin: 10px 10px 0 200px;
	float: none;
}
.navbar .nav li a{
	border-radius:3px;
	display:block;
	white-space:nowrap;
	padding:6px 11px 7px;		
	text-overflow:ellipsis;
	text-decoration:none;
	margin-left: 3px;
	margin-bottom: 3px;
}
.navbar .nav li ul{
	border-radius:5px;
	display:none;
	position:absolute;
	list-style-type:none;
	min-width:100px;
	padding:3px;
	box-shadow:0 0 3px rgba(0,0,0,0.3);

}
.navbar .nav > li:hover ul{
	display:block;
}

.marketing-byline {
	list-style:none;
	margin-left: 0;
	margin-bottom: 24px;
}
.marketing-byline li{
	display:inline;
	padding:0 2px;
}
.download-info{
	clear:both;
	text-align: center;
}
#intro{margin-top: 40px;
text-align: center;}

section{
	margin-top:100px;
}
img{
	height:auto !important;
}
iframe{
	max-width:100% !important;
}

@media (max-width: 979px) {
	#documenter_content{
		padding-top: 0px;
	}
	.navbar .nav {
		margin: 10px 10px 0 0;
	}
	.navbar .nav > li > a{
		padding:6px 11px 7px;
		max-width:100%;
		overflow:hidden;
		text-overflow:ellipsis;
	}
	.navbar .nav li ul{
		display:none;
		box-shadow:none;
		position:static;
	}
	.navbar .nav li:hover ul{
		display:block;
	}
	.navbar .nav li ul li{
		display:block;
	}
	.navbar .nav li a{
		max-width:100%;
	}

	section{
		margin-top:80px;
	}
}
@media (max-width: 480px) {
	section{
		margin-top:30px;
	}
}