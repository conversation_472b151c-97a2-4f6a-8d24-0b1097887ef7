<?php
// Use page content passed from controller
// If not available, initialize as empty array
if (!isset($page_content) || !is_array($page_content)) {
    $page_content = array();
}

// Helper function to get content by section
function getContent($page_content, $section, $default = '') {
    if (isset($page_content[$section]) && !empty($page_content[$section]['content'])) {
        return $page_content[$section]['content'];
    }
    return $default;
}

// Helper function to get content title by section
function getContentTitle($page_content, $section, $default = '') {
    if (isset($page_content[$section]) && !empty($page_content[$section]['title'])) {
        return $page_content[$section]['title'];
    }
    return $default;
}
?>

<!-- Slider Area Start -->
<div id="rs-slider" class="slider-overlay-2">     
    <div id="home-slider" class="rs-carousel owl-carousel" data-loop="true" data-items="1" data-margin="0" data-autoplay="true" data-autoplay-timeout="5000" data-smart-speed="1200" data-dots="false" data-nav="true" data-nav-speed="false" data-mobile-device="1" data-mobile-device-nav="true" data-mobile-device-dots="true" data-ipad-device="1" data-ipad-device-nav="true" data-ipad-device-dots="true" data-md-device="1" data-md-device-nav="true" data-md-device-dots="false">
        
        <!-- Slide 1 -->
        <div class="item active">
            <img src="<?php echo base_url('backend/themes/edulearn/images/slider/home1/slide1.jpg'); ?>" alt="Slide1" />
            <div class="slide-content">
                <div class="display-table">
                    <div class="display-table-cell">
                        <div class="container text-center">
                            <h1 class="slider-title" data-animation-in="fadeInLeft" data-animation-out="animate-out">
                                <?php echo getContentTitle($page_content, 'slider_1_title', 'WELCOME TO ' . strtoupper($school_setting->name)); ?>
                            </h1>
                            <p data-animation-in="fadeInUp" data-animation-out="animate-out" class="slider-desc">
                                <?php echo getContent($page_content, 'slider_1_content', isset($school_setting->description) ? $school_setting->description : ''); ?>
                            </p>  
                            <a href="<?php echo base_url('page/about-us'); ?>" class="sl-readmore-btn mr-30" data-animation-in="lightSpeedIn" data-animation-out="animate-out">READ MORE</a>
                            <a href="<?php echo base_url('online_admission'); ?>" class="sl-get-started-btn" data-animation-in="lightSpeedIn" data-animation-out="animate-out">GET STARTED NOW</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2 -->
        <div class="item">
            <img src="<?php echo base_url('backend/themes/edulearn/images/slider/home1/slide2.jpg'); ?>" alt="Slide2" />
            <div class="slide-content">
                <div class="display-table">
                    <div class="display-table-cell">
                        <div class="container text-center">
                            <h1 class="slider-title" data-animation-in="fadeInUp" data-animation-out="animate-out">
                                <?php echo getContentTitle($page_content, 'slider_2_title', 'ARE YOU READY TO APPLY?'); ?>
                            </h1>
                            <p data-animation-in="fadeInUp" data-animation-out="animate-out" class="slider-desc">
                                <?php echo getContent($page_content, 'slider_2_content', 'Join our community of learners and discover your potential with our comprehensive educational programs.'); ?>
                            </p>  
                            <a href="<?php echo base_url('page/about-us'); ?>" class="sl-readmore-btn mr-30" data-animation-in="fadeInUp" data-animation-out="animate-out">READ MORE</a>
                            <a href="<?php echo base_url('online_admission'); ?>" class="sl-get-started-btn" data-animation-in="fadeInUp" data-animation-out="animate-out">GET STARTED NOW</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3 -->
        <div class="item">
            <img src="<?php echo base_url('backend/themes/edulearn/images/slider/home1/slide3.jpg'); ?>" alt="Slide3" />
            <div class="slide-content">
                <div class="display-table">
                    <div class="display-table-cell">
                        <div class="container text-center">
                            <h1 class="slider-title" data-animation-in="fadeInUp" data-animation-out="animate-out">
                                <?php echo getContentTitle($page_content, 'slider_3_title', 'EXCELLENCE IN EDUCATION'); ?>
                            </h1>
                            <p data-animation-in="fadeInUp" data-animation-out="animate-out" class="slider-desc">
                                <?php echo getContent($page_content, 'slider_3_content', 'Experience quality education with modern facilities and dedicated teachers committed to your success.'); ?>
                            </p>  
                            <a href="<?php echo base_url('page/about-us'); ?>" class="sl-readmore-btn mr-30" data-animation-in="fadeInUp" data-animation-out="animate-out">READ MORE</a>
                            <a href="<?php echo base_url('online_admission'); ?>" class="sl-get-started-btn" data-animation-in="fadeInUp" data-animation-out="animate-out">GET STARTED NOW</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>         
</div>
<!-- Slider Area End -->

<!-- Services Start -->
<div class="rs-services rs-services-style1">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="services-item rs-animation-hover">
                    <div class="services-icon">
                        <i class="fa fa-american-sign-language-interpreting rs-animation-scale-up"></i>                    	        
                    </div>
                    <div class="services-desc">
                        <h4 class="services-title"><?php echo getContentTitle($page_content, 'service_1_title', 'Quality Education'); ?></h4>
                        <p><?php echo getContent($page_content, 'service_1_content', 'We provide comprehensive and quality education for all students'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="services-item rs-animation-hover">
                    <div class="services-icon">                    	        
                        <i class="fa fa-book rs-animation-scale-up"></i>
                    </div>
                    <div class="services-desc">
                        <h4 class="services-title"><?php echo getContentTitle($page_content, 'service_2_title', 'Books & Library'); ?></h4>
                        <p><?php echo getContent($page_content, 'service_2_content', 'Access to extensive library resources and educational materials'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="services-item rs-animation-hover">
                    <div class="services-icon">
                        <i class="fa fa-user rs-animation-scale-up"></i>
                    </div>
                    <div class="services-desc">
                        <h4 class="services-title"><?php echo getContentTitle($page_content, 'service_3_title', 'Certified Teachers'); ?></h4>
                        <p><?php echo getContent($page_content, 'service_3_content', 'Experienced and qualified teachers dedicated to student success'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="services-item rs-animation-hover">
                    <div class="services-icon">
                        <i class="fa fa-graduation-cap rs-animation-scale-up"></i>
                    </div>
                    <div class="services-desc">
                        <h4 class="services-title"><?php echo getContentTitle($page_content, 'service_4_title', 'Certification'); ?></h4>
                        <p><?php echo getContent($page_content, 'service_4_content', 'Recognized certifications and academic achievements'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Services End -->

<!-- About Us Start -->
<div id="rs-about" class="rs-about sec-spacer">
    <div class="container">
        <div class="sec-title mb-50 text-center">
            <h2><?php echo getContentTitle($page_content, 'about_section_title', 'ABOUT US'); ?></h2>      
            <p><?php echo getContent($page_content, 'about_section_subtitle', 'Learn more about our institution and our commitment to excellence in education.'); ?></p>
        </div>
        <div class="row">
            <div class="col-lg-6 col-md-12">
                <div class="about-img rs-animation-hover">
                    <img src="<?php echo base_url('backend/themes/edulearn/images/about/about.jpg'); ?>" alt="About Us"/>
                    <?php if (!empty(getContent($page_content, 'about_video_url'))) { ?>
                        <a class="popup-youtube rs-animation-fade" href="<?php echo getContent($page_content, 'about_video_url', '#'); ?>" title="Video Icon">
                        </a>
                    <?php } ?>
                    <div class="overly-border"></div>
                </div>
            </div>
            <div class="col-lg-6 col-md-12">
                <div class="about-desc">
                    <h3><?php echo getContentTitle($page_content, 'about_title', 'WELCOME TO ' . strtoupper($school_setting->name)); ?></h3>      
                    <p><?php echo getContent($page_content, 'about_content', isset($school_setting->description) ? $school_setting->description : ''); ?></p>
                </div>
                <div id="accordion" class="rs-accordion-style1">
                    <div class="card">
                        <div class="card-header" id="headingOne">
                            <h3 class="acdn-title" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                <?php echo getContentTitle($page_content, 'history_title', 'Our History'); ?>
                            </h3>
                        </div>
                        <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-bs-parent="#accordion">
                            <div class="card-body">
                                <?php echo getContent($page_content, 'history_content', 'Our institution has a rich history of providing quality education and fostering academic excellence for generations of students.'); ?>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header" id="headingTwo">
                            <h3 class="acdn-title collapsed" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                <?php echo getContentTitle($page_content, 'mission_title', 'Our Mission'); ?>
                            </h3>
                        </div>
                        <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-bs-parent="#accordion">
                            <div class="card-body">
                                <?php echo getContent($page_content, 'mission_content', 'To provide comprehensive education that develops critical thinking, creativity, and character in our students, preparing them for success in their future endeavors.'); ?>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header mb-0" id="headingThree">
                            <h3 class="acdn-title collapsed" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                <?php echo getContentTitle($page_content, 'vision_title', 'Our Vision'); ?>
                            </h3>
                        </div>
                        <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-bs-parent="#accordion">
                            <div class="card-body">
                                <?php echo getContent($page_content, 'vision_content', 'To be a leading educational institution that inspires lifelong learning and empowers students to become responsible global citizens.'); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- About Us End -->

<!-- Counter Section Start -->
<div class="rs-counter bg7 pt-100 pb-80">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="rs-counter-list">
                    <div class="counter-text">
                        <div class="count-number">
                            <span class="rs-count"><?php echo getContent($page_content, 'counter_1_number', '2500'); ?></span>
                            <span class="prefix">+</span>
                        </div>
                        <h4 class="title"><?php echo getContentTitle($page_content, 'counter_1_title', 'Students'); ?></h4>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="rs-counter-list">
                    <div class="counter-text">
                        <div class="count-number">
                            <span class="rs-count"><?php echo getContent($page_content, 'counter_2_number', '120'); ?></span>
                            <span class="prefix">+</span>
                        </div>
                        <h4 class="title"><?php echo getContentTitle($page_content, 'counter_2_title', 'Teachers'); ?></h4>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="rs-counter-list">
                    <div class="counter-text">
                        <div class="count-number">
                            <span class="rs-count"><?php echo getContent($page_content, 'counter_3_number', '50'); ?></span>
                            <span class="prefix">+</span>
                        </div>
                        <h4 class="title"><?php echo getContentTitle($page_content, 'counter_3_title', 'Courses'); ?></h4>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="rs-counter-list">
                    <div class="counter-text">
                        <div class="count-number">
                            <span class="rs-count"><?php echo getContent($page_content, 'counter_4_number', '25'); ?></span>
                            <span class="prefix">+</span>
                        </div>
                        <h4 class="title"><?php echo getContentTitle($page_content, 'counter_4_title', 'Years Experience'); ?></h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Counter Section End -->

<!-- Call to Action Start -->
<div class="rs-cta">
    <div class="container">
        <div class="cta-wrap">
            <div class="row">
                <div class="col-lg-9 col-md-12">
                    <div class="cta-content">
                        <h2><?php echo getContentTitle($page_content, 'cta_title', 'Ready to Get Started?'); ?></h2>
                        <p><?php echo getContent($page_content, 'cta_content', 'Join our community and start your educational journey with us today.'); ?></p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-12">
                    <div class="cta-btn">
                        <a class="readon" href="<?php echo base_url('online_admission'); ?>">Apply Now</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Call to Action End -->

<!-- Latest News Start -->
<div id="rs-blog" class="rs-blog sec-spacer">
    <div class="container">
        <div class="sec-title mb-50 text-center">
            <h2><?php echo getContentTitle($page_content, 'news_section_title', 'LATEST NEWS'); ?></h2>
            <p><?php echo getContent($page_content, 'news_section_subtitle', 'Stay updated with our latest news and announcements.'); ?></p>
        </div>
        <div class="row">
            <?php
            // Get latest news/events from database
            if (isset($this->db)) {
                $this->db->select('*');
                $this->db->from('events');
                $this->db->where('is_active', 'yes');
                $this->db->where('school_id', isset($school_id) ? $school_id : (isset($school_setting->id) ? $school_setting->id : 0));
                $this->db->order_by('start_date', 'DESC');
                $this->db->limit(3);
                $latest_events = $this->db->get()->result_array();

                if (!empty($latest_events)) {
                    foreach ($latest_events as $event) {
                        ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="blog-item">
                                <div class="blog-img">
                                    <?php if (!empty($event['event_image'])) { ?>
                                        <img src="<?php echo base_url('uploads/school_content/events/' . (isset($event['event_image']) ? $event['event_image'] : '')); ?>" alt="<?php echo isset($event['event_title']) ? $event['event_title'] : ''; ?>" />
                                    <?php } else { ?>
                                        <img src="<?php echo base_url('backend/themes/edulearn/images/blog/1.jpg'); ?>" alt="<?php echo isset($event['event_title']) ? $event['event_title'] : ''; ?>" />
                                    <?php } ?>
                                    <div class="blog-date">
                                        <span class="date"><?php echo date('d', strtotime($event['event_start'])); ?></span>
                                        <span class="month"><?php echo date('M', strtotime($event['event_start'])); ?></span>
                                    </div>
                                </div>
                                <div class="blog-content">
                                    <h3 class="blog-title"><a href="<?php echo base_url('welcome/event/' . (isset($event['id']) ? $event['id'] : '')); ?>"><?php echo isset($event['event_title']) ? $event['event_title'] : ''; ?></a></h3>
                                    <div class="blog-meta">
                                        <span><i class="fa fa-calendar"></i> <?php echo isset($event['event_start']) ? date('M d, Y', strtotime($event['event_start'])) : ''; ?></span>
                                    </div>
                                    <div class="blog-desc">
                                        <?php echo isset($event['event_description']) ? character_limiter(strip_tags($event['event_description']), 100) : ''; ?>
                                    </div>
                                    <div class="blog-btn">
                                        <a href="<?php echo base_url('welcome/event/' . (isset($event['id']) ? $event['id'] : '')); ?>">Read More</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    // Default news items if no events found
                    for ($i = 1; $i <= 3; $i++) {
                        ?>
                        <div class="col-lg-4 col-md-6">
                            <div class="blog-item">
                                <div class="blog-img">
                                    <img src="<?php echo base_url('backend/themes/edulearn/images/blog/' . $i . '.jpg'); ?>" alt="News <?php echo $i; ?>" />
                                    <div class="blog-date">
                                        <span class="date"><?php echo date('d'); ?></span>
                                        <span class="month"><?php echo date('M'); ?></span>
                                    </div>
                                </div>
                                <div class="blog-content">
                                    <h3 class="blog-title"><a href="#"><?php echo getContentTitle($page_content, 'news_' . $i . '_title', 'School News ' . $i); ?></a></h3>
                                    <div class="blog-meta">
                                        <span><i class="fa fa-calendar"></i> <?php echo date('M d, Y'); ?></span>
                                    </div>
                                    <div class="blog-desc">
                                        <?php echo getContent($page_content, 'news_' . $i . '_content', 'Stay updated with the latest news and announcements from our school community.'); ?>
                                    </div>
                                    <div class="blog-btn">
                                        <a href="#">Read More</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                }
            }
            ?>
        </div>
    </div>
</div>
<!-- Latest News End -->
