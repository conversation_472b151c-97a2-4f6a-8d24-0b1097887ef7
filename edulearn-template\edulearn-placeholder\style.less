@charset "utf-8";

/**
*
* -----------------------------------------------------------------------------
*
* Template : <PERSON><PERSON><PERSON>n | Responsive Education HTML5 Template 
* Author : rs-theme
* Author URI : http://www.rstheme.com/
*
* -----------------------------------------------------------------------------
*
**/

/* Table Of Content
---------------------------------------------------------
01. General CSS
02. Global Class
03. Header Section 
04. Sticky Menu
05. Slider Section
06. Breadcrumbs
07. Services
08. About Us
09. <PERSON> Cources
10. Courses Details
11. Rs-counter
12. Upcoming Events
13. Experienced Staffs
14. Staffs Single
15. Calltoaction
16. Latest News
17. Our Publications
18. Testimonial
19. Newsletter
20. Rs-video
21. Why Choose Us
22. Pricing Table
23. Instagram
24. About Us Page
25. Rs Timeline
26. Elements
27. Blog Pages
28. Blog Details
29. Shop Page Start Here
30. Shop Single Page Start Here 
31. Check Out css Start Here
32. Shipping Area Start Here
32. Contact Page Section Start Here
33. Rs Gallery
34. ScrollUp
35. 404 Page Area Start Here 
36. Preloader css
37. Rs Footer

--------------------------------------------------------*/
@import url('https://fonts.googleapis.com/css?family=Montserrat:400,500,600,700|Roboto+Condensed:400,500,600,700');

@import "rs-animation.less";
@body-font: 'Roboto Condensed', sans-serif;
@title-font: 'Roboto Condensed', sans-serif;
@transition: all 0.3s ease 0s;
@primary-color: #ff3115;
@toolbar-background: #111111;
@toolbar-color: #ffffff;
@title-color: #212121;
@hover-color: #e41f05;
@white-color: #ffffff;
@global-color: #444;
@body-color: #505050;
@accordian-color: #252525;

/* -----------------------------------
    01. General CSS
-------------------------------------*/
html,
body {
    font-size: 16px;
    color: @body-color;
    font-family: @body-font;
    vertical-align: baseline;
    line-height: 26px;
    font-weight: 400;
    overflow-x:hidden;
}
img {
    max-width: 100%;
    height: auto;
}
p {
    margin: 0 0 26px;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: @title-font;
    color: @title-color;
    margin: 0 0 26px;
	font-weight: 700;
}
h1{
    font-size: 36px;
}
h2{
    font-size: 30px;
}
h3{
    font-size: 24px;
}
h4 {
    font-size: 20px;
}
h5 {
    font-size: 16px;
}

h6 {
    font-size: 14px;
}
a {
    color: @primary-color;
    transition: @transition;
    text-decoration: none !important;
    outline: none !important;
}
a:active,
a:hover {
    text-decoration: none;
    outline: 0 none;
    color: @hover-color;
}
ul {
    list-style: outside none none;
    margin: 0;
    padding: 0;
}
.clear {
    clear: both;
}
::-moz-selection {
    background: @primary-color;
    text-shadow: none;
    color: #ffffff;
}
::selection {
    background: @primary-color;
    text-shadow: none;
    color: #ffffff;
} 

.sec-spacer {
    padding: 100px 0;
}
.sec-color {
    background-color: #f9f9f9;
}
.drak-color {
    background-color: @accordian-color;
}
.gray-color{
     background-color: #f0f0f0;
}
.primary-color {
    color: @primary-color;
}
.primary-bg {
    background: @primary-color;
}
.sec-black {
    background: #212121;
}
.bg-fixed {
    background-attachment: fixed;
    background-repeat: no-repeat;
}

.text-right {
    text-align: right;
}
.text-left {
    text-align: left;
}
.text-center {
    text-align: center;
}
.relative {
    position: relative;
}
/*-- Readon Button Css --*/
.readon2{
    font-size: 14px;
    border: 1px solid #ff3115;
    background: #ff3115;
    display: inline-block;
    padding: 0 35px;
    height: 45px;
    line-height: 45px;
    position: relative;
    color: #fff;
    -webkit-transition: .4s;
    transition: .4s;
    text-transform: uppercase;
    font-weight: 400;
    text-align: center;
    border-radius: 35px;
    &.transparent {
        border: 1px solid #ff3115;
        color: @primary-color;
        background: transparent;
        &:hover{
            background: @primary-color;
            color: @white-color;
        }
    }
    &:hover{
        background: @hover-color;
        color: @white-color;
    }
}


/********************************/
/*       Slides backgrounds     */
/********************************/
.bg1 {
    background-image: url(images/bg/bg1.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg2 {
    background-image: url(images/bg/bg2.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg3 {
    background-image: url(images/bg/counter-bg.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg4 {
    background-image: url(images/bg/cta-bg.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg5 {
    background-image: url(images/bg/testimonial-bg.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg6 {
    background-image: url(images/slider/home1/slide2.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg7 {
    background-image: url(images/bg/bg3.jpg);
    background-size: cover;
    background-position: center;
    background-position: center top;
}
.bg8 {
    background-image: url(images/bg/bg8.jpg);
    background-attachment: fixed;
    background-position: center top;
}
.bg9{
    background-image: url(images/bg/video-bg2.jpg);
    background-attachment: fixed;
    background-position: center top;
}
.bg10{
    background-image: url(images/bg/counter-bg-style2.jpg);
    background-size: cover;
    background-attachment: fixed;
    background-position: center top;
}
.bg11{
    background-image: url(images/bg/home7-style.jpg);
    background-position: center top;
    background-size: cover;
    padding-top: 200px;
    padding-bottom: 130px;
}
.bg12{
    background-image: url(images/bg/countdown.jpg);
    background-position: center top;
    background-size: cover;
    padding-top: 100px;
    padding-bottom: 100px;
}
.bg13{
    background-image: url(images/bg/home8-style.jpg);
    background-position: center;
    background-size: cover;
    padding-top: 200px;
    padding-bottom: 250px;
}
.bg14{
    background-image: url(images/counter/2.jpg);
    background-size: cover;
    background-position: center top;
    padding-bottom: 500px !important
}
.bg15{
    background-image: url(images/bg/about-home8-bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    padding-top: 100px;
    padding-bottom: 100px;
}
.uppercase{
    text-transform: uppercase;
}

/* ------------------------------------
    02. Global Class
---------------------------------------*/
.drak-color{
    .owl-nav .owl-prev,
    .owl-nav .owl-next,
    .sec-title h3{
        color: #ffffff;
    } 
}
.sec-title2{
    h2{
        font-size: 30px;
        text-transform: uppercase;
        color: @title-color;
        font-weight: 700;
        line-height: 40px;
        margin: 0;
    }
    span{
        display: block;
        font-size: 18px;
        line-height: 34px;
        color: @primary-color;
        font-weight: 600;
    }
}
.sec-title {
    position: relative;
    margin-bottom: 50px;
    padding-bottom: 20px;
	h2{
        font-size: 30px;
        line-height: 24px;
        margin-bottom: 15px;
        text-transform: uppercase;
    }
    &:after{
        content:"";
        position: absolute;
        left: 0;
        bottom: 0;
        height: 3px;
        width: 100px;
        background: @primary-color;
    }
    &.text-center {
        &:after {
            left: 50%;
            -webkit-transform: translateX(-50%);      
            transform: translateX(-50%);            
        }
    }
    &.text-right {
        &:after {
            left: auto;
            right: 0;
        }
        .view-more {
            right: auto;
            left: 0;
        }
    }
    p {
        margin-bottom: 0;
        font-size: 16px;
    }
    .view-more {
        position: absolute;
        right: 0;
        a {
            font-weight: 600;
            font-size: 15px;
        }
    }
    &.white-text{
        h2{
            color: @white-color;
        }
        p{
            color: rgba(255, 255, 255, 0.6);
        }
    }
}
.home5{
    .sec-title {
		h2{
            color: #92278f;
        }
        &:after{
            background: #92278f;
        }
        &.white-text{
            p{
                color: rgba(255, 255, 255, 1);
            }
        }
    }
}

.sec-title-2 {
    position: relative;
	h2{
        position: relative;
        font-size: 30px;
        line-height: 24px;
        margin-bottom: 25px;
        padding-bottom: 26px;
        text-transform: uppercase;
        &:after{
            content:"";
            position: absolute;
            left: 0;
            bottom: 0;
            height: 3px;
            width: 100px;
            background: @primary-color;
        }
    }
    .view-more {
        position: absolute;
        right: 0;
        bottom: 0;
        a {
            font-weight: 700;
            font-size: 14px;
        }
    }
    &.text-center {
        h2{
            &:after {
                left: 50%;
                -webkit-transform: translateX(-50%);      
                transform: translateX(-50%);            
            }            
        }
    }
    &.text-right {
		h2{
            &:after {
                left: auto;
                right: 0;
            }            
        }
        .view-more {
            right: auto;
            left: 0;
        }
    }
    p {
        margin-bottom: 0;
        font-size: 16px;
    }
}
.primary-btn{
    display: inline-block;
    height: 40px;
    line-height: 35px;
    text-align: center;
    min-width: 136px;
    padding: 0 20px;
    border: 2px solid @primary-color;
    color: @title-color;
    transition: all 0.3s ease 0s;
    font-weight: 600;
    &:hover{
        background: @primary-color;
        color: @white-color;
    }
}
.readon {
    position: relative;
    display: inline-block;
    padding: 12px 20px;
    line-height: normal;
    background: @primary-color;
    color: #fff;
    transition: @transition;
    border-radius: 2px;
    &:hover, &:focus {
        background: @hover-color;
        color: rgba(255, 255, 255, 0.8)      
    }
    &.border {
        background: transparent;
        border: 1px solid @primary-color;
        color: @primary-color;
        &:hover {
            color: #fff;
            background: @primary-color;
        }
        &.white {
            border-color: #fff;
            color: #fff;
            &:hover {
                color: @primary-color;
                background: #fff;       
            }
        }
    }
}
/*overly border*/
.overly-border::before,
.overly-border::after {
    position: absolute;
    top: 20px;
    right: 20px;
    bottom: 20px;
    left: 20px;
    content: '';
    opacity: 0;
    -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
    transition: opacity 0.35s, transform 0.35s;
    z-index: 1;
}
.blue-bg{
    position: relative;
    .blue-overlay{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,188,212,0.9);
    }
}

.about-img .overly-border::before,
.about-img .overly-border::after{
    top: 50px;
    right: 50px;
    bottom: 50px;
    left: 50px;
}
.overly-border::before {
    border-top: 5px solid @primary-color;
    border-bottom: 5px solid @primary-color;
    -webkit-transform: scale(0,1);
    transform: scale(0,1);
}

.rs-blog .blog-item.slick-current .team-content {
    opacity: 1;
    top: 0;
}

.overly-border::after {
    border-right: 5px solid @primary-color;
    border-left: 5px solid @primary-color;
    -webkit-transform: scale(1,0);
    transform: scale(1,0);
}
.single-member-area figure .overly-border:before,
.single-member-area figure .overly-border:after,
.rs-blog .blog-item.slick-current .overly-border:before,
.rs-blog .blog-item.slick-current .overly-border:after,
.single-member-area:hover .overly-border:before,
.single-member-area:hover .overly-border:after,
.team-content:hover .overly-border:before,
.team-content:hover .overly-border:after,
.blog-content:hover .overly-border:before,
.blog-content:hover .overly-border:after,
.project-content:hover .overly-border:before,
.project-content:hover .overly-border:after,
.about-img:hover .overly-border:before,
.about-img:hover .overly-border:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}


.right_menu_togle .search-wrap button{
    color: @primary-color;
}
.padding-0{
    padding: 0 !important;
}
.pt-45 {
    padding-top: 45px !important;
}
.pt-70 {
    padding-top: 70px !important;
}
.pt-50 {
    padding-top: 50px !important;
}
.pt-80 {
    padding-top: 80px !important;
}
.pt-100 {
    padding-top: 100px !important;
}
.pb-40 {
    padding-bottom: 40px !important;
}
.pb-45 {
    padding-bottom: 45px !important;
}
.pb-70 {
    padding-bottom: 70px !important;
}
.pb-80 {
    padding-bottom: 80px !important;
}
.pb-170 {
    padding-bottom: 170px !important;
}
.mt-5 {
    margin-top: 5px !important;
}
.mt-15 {
    margin-top: 15px !important;
}
.sparator-15 {
    height: 15px;
    clear: both;
}
.ml-15 {
    margin-left: 15px !important;
}
.mt-30 {
    margin-top: 30px !important;
}
.mt-45 {
    margin-top: 45px !important;
}
.mt-50 {
    margin-top: 50px !important;
}
.mt-70 {
    margin-top: 70px !important;
}
.mt-80 {
    margin-top: 80px !important;
}
.mt-100 {
    margin-top: 100px !important;
}
.mb-0 {
    margin-bottom: 0px !important;
}
.mb-30 {
    margin-bottom: 30px !important;
}
.mb-45 {
    margin-bottom: 45px !important;
}
.mb-50 {
    margin-bottom: 50px !important;
}
.mb-70 {
    margin-bottom: 70px !important;
}
.mb-100{
    margin-bottom: 100px !important;
}
.mr-25 {
    margin-right: 25px !important;
}
.mr-30 {
    margin-right: 30px !important;
}

.margin-remove {
    margin: 0 !important;
}
.display-table {
    display: table;
    height: 100%;
    width: 100%;
}
.display-table-cell {
    display: table-cell;
    vertical-align: middle;
}
.white-color {
    color: #fff !important;
}
.rs-vertical-middle {
    .logo-area{
    	line-height: 95px;
    }
}
.rs-vertical-middle {
   display: -ms-flexbox;
   display: -webkit-flex;
   display: flex;
   -ms-flex-wrap: wrap;
   -webkit-flex-wrap: wrap;
   flex-wrap: wrap;
   -ms-flex-align: center;
   -webkit-align-items: center;
   align-items: center;
}
.rs-vertical-bottom {
   display: -ms-flexbox;
   display: -webkit-flex;
   display: flex;
   -ms-flex-wrap: wrap;
   -webkit-flex-wrap: wrap;
   flex-wrap: wrap;
   -ms-flex-align: flex-end;
   -webkit-align-items: flex-end;
   align-items: flex-end;
}

/* -. Owl Carousel -*/
.owl-nav {
    > div {
        position: absolute;
        top: 50%;
        width: 42px;
        height: 42px;
        line-height: 42px;
        text-align: center;
        background-color: #444;
        color: #fff;
        transform: translateY(-50%);
        font-size: 22px;
        border-radius: 50%;
        opacity: 0;
        -webkit-transition: @transition;
        transition: @transition;
        &:hover {
            background-color: @primary-color;
        }
    }
    .owl-prev {
        left: -60px;
    }
    .owl-next {
        right: -60px;
    }
}

.rs-carousel {
    &:hover {
        .owl-nav {
            > div {
                opacity: 1;
            }
        }
    }        
}
.rs-navigation-2 {
    padding-bottom: 80px;
    .owl-nav {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        > div {
            position: initial;
            display: inline-block;
            transform: none;
            opacity: 1 !important;
        }
        .owl-next {
            margin-left: 12px;
        }
    }
    .owl-stage-outer {
        padding-bottom: 0 !important;
    }
}

/* ------------------------------------
    03. Header Section  
---------------------------------------*/
.rs-toolbar {
    padding: 4px 0 5px;
    background-color: @toolbar-background;
    .rs-toolbar-left {
        .welcome-message {
            font-size: 14px;
            i {
                color: @primary-color;
                margin-right: 8px;
            }
            span {
                color: @toolbar-color;
            }
        }
    }
    .rs-toolbar-right {
        text-align: right;
        .toolbar-share-icon {
            display: inline-block;
            margin-right: 30px;
            ul {
                li {
                    display: inline-block;
                    + li {
                        margin-left: 8px;
                    }
                    a {
                        font-size: 14px;
                        color: #888888;
                        &:hover, &:active, &:focus {
                            color: #ffffff;
                        }
                    }
                }
            }            
        }
        .apply-btn {
            font-weight: 500;
            font-size: 14px;
        }
    }
}

/* ------------------------------------
    02. Header Start
---------------------------------------*/
.rs-header {
    .rs-header-top {
        padding: 25px 0 20px;
        .header-contact {
            float: right;
            .widget-text {
                position: relative;
                padding-left: 55px;
                i {
                    position: absolute;
                    border-radius: 2px;
                    text-align: center;
                    left: 0;
                    line-height: 40px;
                    color: @primary-color;
                    font-size: 35px;
                    -webkit-transition: all .3s ease;
                    transition: all .3s ease;
                    font-weight: 700;
                }
                
                .info-text {
                    a {
                        color: #505050;
                        font-weight: 400;
                        font-size: 16px;
                        &:hover {
                            color: @primary-color;
                        }
                    }
                    span {
                        display: block;
                        font-weight: 700;
                        color: #101010;
                        line-height: 18px;
                    }
                }
            }            
        }
    }
    .rs-menu-toggle{
        color: #fff !important;
        &:hover{
            color: @primary-color !important;
        }
    }
}
.rs-header,
.rs-header-2,
.rs-header-3,
.rs-header-4{
    .menu-area{
        .rs-menu li.current_page_item > a,
        .rs-menu li.current-menu-item > a,
        .rs-menu li.active > a,
        .rs-menu li a:hover{
            color: @primary-color !important;
        }
    }
}
.rs-header-2 {
    position: absolute;
    width: 100%;
    z-index: 999;
    .menu-area {
        padding: 30px 0;
        .rs-menu {
            .nav-menu {
                text-align: right;
                > li {
                    > a {
                        color: #ffffff;
                        &:hover {
                            color: @primary-color;
                        }
                    }
                }    
            }
        }
    }
}
@-moz-document url-prefix() {
    .home2 .right-bar-icon .nav-expander{
        position: relative;
        top: -1px;
    }
}
/* ----- Menu -------*/
.nav-menu > {
    > li {
        > a {
            font-weight: 500;
            font-size: 16px;
        }
    } 
}
.home1{
    .logo-area{
        padding-top: 3px;
    }
    .rs-menu {
        li {
            i{
                margin-right: 0;
                color: @primary-color;
                transition: 0.3s;
                -webkit-transition: 0.3s;
                -ms-transition: 0.3s;
                &:hover{
                    color: @hover-color;
                }
            }
        }
    }
    .menu-area .rs-menu > ul > li > a{
        color: #bbbbbb;
    }
    .menu-area .rs-menu > ul > li > a:hover,
    .menu-area .rs-menu li.current_page_item > a{
        color: @primary-color !important;
    }
    
    .menu-area{
        background: @title-color;
    }
    .searce-box{
        position: absolute;
        top: 0;
        right: 23%;
        top: 16px;
        a.rs-search{
            i{
                color: @primary-color;
                &:hover{
                    color: @white-color;
                }
            }
            
        }
    }
    .rs-header .rs-header-top .col-md-4:first-child .header-contact {
        float: left;
    }
}
.inner-page{
    .searce-box{
        position: absolute;
        top: 0;
        right: 16px;
        top: 15px;
        a.rs-search{
            i{
                color: @primary-color;
            }
        }
    }
}

.home2 {
    .rs-header-top {
        padding: 20px 0 50px;
    }
    .menu-area {
        .container {
            position: relative;
        }
       .main-menu{
            background: #111111;
            position: absolute;
            top: -30px;
            border-radius: 0;
            width: 100%;
            z-index: 999;
            left: 0;
            max-width: 1170px;
            margin: 0 auto;
        }
        .rs-menu {
            ul > li {
               > a {
                    color: #bbbbbb;
                    &:hover {
                        color: @primary-color;
                    }
                }
                ul.sub-menu{
                    a{
                         color: #bbbbbb;
                    }
                }
            }
        }
        &.sticky{
            .main-menu{
                position: static;
            }
        }
    }
    
    #rs-slider {
        .container {
            margin-bottom: 100px;
        }
    }
    .rs-search {
        position: absolute;
        right: 0;
        z-index: 99;
        top: 0;
        color: #fff;
        font-size: 18px;
        display: inline-block;
        height: 55px;
        width: 60px;
        line-height: 55px;
        padding: 0;
        text-align: center;
        background: @primary-color;
        &:hover {
            background-color: @hover-color;
            color: rgba(255,255,255,0.7);
        }
    }
    .sticky {
        .rs-search {
            right: 12px;
        }
    }
}
.home1,
.home2{
    .right-bar-icon{
        position: absolute;
        right: 0;
        a{
            display: inline-block;
            height: 55px;
            width: 45px;
            line-height: 55px;
            text-align: center;
            color: #fff;
            &:hover{
                color: #fff;
            }
        }
        .rs-search{
            position: static;
            color: #fff;
			background-color: @primary-color;
            margin-right: -4px;
            &:hover{
                background: @hover-color;
            }
        }
        .nav-expander{
            background: @hover-color;
            &:hover{
                background: @primary-color;
            }
        }
    }
    .sticky {
        .right-bar-icon {
            right: 12px;
        }
    }
}
.home1 .rs-courses .cource-item {
	border: none;
	background: #fff;
}
.university-home,
.inner-page,
.instructor-home,
.home1,
.home2,
.home5,
.home3{
    .search-modal {
        .modal-content {
            background: transparent;
            position: initial;
            border: 0;
        }
        .search-block {
            input {
                height: 60px;
                line-height: 60px;
                padding: 0 15px;
                background: transparent;
                border-width: 0 0 1px 0;
                border-radius: 0;
                border-color: rgba(255,255,255,0.4);
                box-shadow: none;
                color: #ffffff;
                font-weight: 600;
                font-size: 18px;
            }
        }
        .close {
            color: #ffffff;
            margin-top: 20px;
            font-size: 14px;
            background-color: rgba(255,255,255,0.4);
            height: 40px;
            width: 40px;
            text-align: center;
            line-height: 40px;
            border-radius: 50%;
            opacity: 1;
            outline: none;
            transition: @transition;
            &:hover {
                background-color: @primary-color;
            }
        }
    }
    .modal-backdrop {
        opacity: 0.95;
    }
}

.home3{
    .rs-header {
        position: absolute;
        width: 100%;
        z-index: 999;
    }
}
#rs-header{
    &.rs-transfarent-header-style{
        position: absolute;
        width: 100%;
        z-index: 999;
        .logo-area{
            padding-top: 0;
        }
        .menu-sticky.sticky{
            background: @title-color;
            padding-top: 10px;
            padding-bottom: 10px;
        }
        .menu-area{
            background: transparent;
            .rs-menu .nav-menu > li > a{
                color: #ffffff;
            }
        }
        .rs-right-btn-inner{
            position: relative;
            .searce-box{
                position: absolute;
                top: 0;
                right: 90%;
                top: 7px;
                .rs-search{
                    i{
                        color: @primary-color;
                    }
                }
            }
            .apply-box{
                margin-left: 35px;
                a{
                    padding: 0 28px;
                    border-radius: 35px;
                    line-height: 40px;
                    height: auto;
                    color: @white-color;
                    background: @primary-color;
                    display: inline-block;
                    text-align: center;
                    font-size: 14px;
                    text-transform: uppercase;
                    font-weight: 500;
                    &:hover{
                        color: @white-color;
                        background: @hover-color;
                    }
                   
                }
            }
        }
    }
}

.home3 {
    .rs-header {
        .menu-area {
            background-color: rgba(37, 37, 37, 0.8);
            .rs-menu .nav-menu > li > a{
                color: @white-color;
                line-height: 105px;
                height: 105px;
                &:hover{
                    color: @primary-color;
                }
            }
            .toggle-btn{
                position: absolute;
                right: 16px;
                top: 44px;
                background-color: transparent;
                cursor: pointer;
                .border-icon{
                    display: block;
                    height: 2px;
                    margin-bottom: 5px;
                    width: 25px;
                    position: relative;
                    -webkit-transition: all 0.4s ease-in-out 0s;
                    transition: all 0.4s ease-in-out 0s;
                    background: @white-color;
                    z-index: 11;
                    &:last-child{
                        margin-bottom: 0;
                    }
                }
                &.active{
                    top: 40px;
                    .border-icon{
                        background: @primary-color;
                        &:nth-child(1){
                            top: 9px;
                            -webkit-transform: rotate(45deg);
                            transform: rotate(45deg);
                        } 
                        &:nth-child(2){
                            opacity: 0;
                            visibility: hidden;
                        }
                        &:nth-child(3){
                            top: -5px;
                            -webkit-transform: rotate(-45deg);
                            transform: rotate(-45deg);
                        }
                    }
                }
            }
            
        }
        .menu-sticky.sticky{
            background-color: #212121;
            .rs-menu {
                .nav-menu > li > a{
                    line-height: 80px;
                    height: 80px;
                    &:hover{
                        color: @primary-color;
                    }
                }
            }
            .searce-box{
                top: 26px;
            }
            .toggle-btn{
                top: 32px;
            }
        }
        .searce-box{
            position: absolute;
            top: 0;
            right: 8%;
            top: 38px;
            a.rs-search{
                i{
                    color: @primary-color;
                }
            }
        }
        .rs-menu ul{
            text-align: right;
            margin-right: 70px;
        }
        .searce-box,
        .rs-menu > ul{
            opacity: 0;
            visibility: hidden;
            -webkit-transition: all 0.4s ease-in-out 0s;
            -ms-transition: all 0.4s ease-in-out 0s;
            transition: all 0.4s ease-in-out 0s;
        }
    }
    &.hidden-menu{
        .rs-header{
           .searce-box,
            .rs-menu > ul{
                opacity: 1;
                visibility: visible;
            } 
        }
    }
}
.home3,
.home5,
.instructor-home{
    .rs-toolbar .rs-toolbar-left{
        .welcome-message{
            float: left;
            margin-right: 25px;
            &:last-child{
                margin-right: 0;
            }
            a{
                color: @white-color;
                &:hover{
                    color: @primary-color;
                }
            }
        }
    }
}
.home5{
    .rs-toolbar{
        background: #00bcd4;
        .toolbar-share-icon{
            margin-right: 15px;
            ul li a{
                color: #fff;
                font-size: 14px;
                &:hover{
                   color: #92278f; 
                }
            }
        }
        .rs-search{
            color: #fff;
            font-size: 14px;
            &:hover{
               color: #92278f; 
            }
        }
    }
    .rs-header{
        .logo-area{
            padding-top: 23px;
        }
        .main-menu{
            .rs-menu{
                ul{
                    text-align: right;
                    margin-right: 112px;
                }
                > ul > li > a{
                    color: #00bcd4; 
                }
                ul li a:hover,
                ul li.active a,
                ul li.current-menu-item > a{
                    color: #92278f !important; 
                }
            }
            .nav-expander {
                font-size: 20px;
                -webkit-transition: all 0.4s ease;
                -ms-transition: all 0.4s ease;
                transition: all 0.4s ease;
                display: block;
                color: #92278f; 
                position: absolute;
                right: 15px;
                top: 26px;
                &:hover {
                    transform: scale(1.1);
                    color: @primary-color;
                }
            }
        }
        .sticky{
            background: #fff;
        }
        .apply-box{
                position: absolute;
                right: 0;
                min-width: 130px;
                text-align: center;
                line-height: 46px;
                background: #92278f;
                margin-top: 17px;
                border-radius: 30px;
            a{
                color: #fff;
                display: block;
                text-transform: uppercase;
                &:hover{
                   color: #00bcd4;  
                }
            }
        }
    }
}

.instructor-home{
    .rs-header{
        .logo-area{
            padding-top: 23px;
        }
        .main-menu{
            .rs-menu{
                ul{
                    text-align: right;
                    margin-right: 110px;
                }
                > ul > li > a{
                    color: @body-color; 
                }
                ul li a:hover,
                ul li.active a,
                ul li.current-menu-item > a{
                    color: @primary-color !important; 
                }
            }
            .nav-expander {
                font-size: 20px;
                -webkit-transition: all 0.4s ease;
                -ms-transition: all 0.4s ease;
                transition: all 0.4s ease;
                display: block;
                color: #92278f; 
                position: absolute;
                right: 15px;
                top: 26px;
                &:hover {
                    transform: scale(1.1);
                    color: @primary-color;
                }
            }
        }
        .sticky{
            background: #fff;
        }
        .apply-box{
                position: absolute;
                right: 0;
                min-width: 130px;
                text-align: center;
                line-height: 46px;
                background: @primary-color;
                margin-top: 17px;
                border-radius: 30px;
                transition: 0.4s;
                &:hover{
                    background: @hover-color;
                }
            a{
                color: #fff;
                display: block;
                text-transform: uppercase;
            }
        }
    }
    .searce-box{
        padding-top: 28px;
        i{
            color: @primary-color;
        }
    }
}

.university-home {
    .menu-area{
        padding: 16px 0;
        .rs-menu ul{
            text-align: right;
        }
    }
    .rs-header .menu-area .rs-menu {
        .nav-menu > li > a{
            color: #ffffff;
        }
        .searce-box{
            padding-top: 15px;
            a{
                position: relative;
                &:after{
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    height: 14px;
                    width: 1px;
                    background: #ffffff;
                }
            }
        }
    }
    
}


.home5,
.university-home{
    .overly-border::before {
        border-top: 5px solid #92278f;
        border-bottom: 5px solid #92278f;
    }
    .overly-border::after {
        border-right: 5px solid #92278f;
        border-left: 5px solid #92278f;
    }
    .rs-footer .footer-share ul li a,
    .rs-footer .footer-share ul li a:hover,
    .rs-footer .footer-title:after,
    .rs-footer .footer-top .news-form button,
    .rs-footer .footer-top .recent-post-widget .post-item .post-date,
    .rs-footer .footer-contact-desc,
    .rs-latest-news .news-normal-block .news-btn a,
    .owl-nav > div:hover,
    .rs-team .team-item .team-img .normal-text,
    .rs-team .team-item .team-title:after,
    #scrollUp i{ 
        background: #92278f;
    }
    
    .rs-footer .footer-bottom,
    .rs-footer .footer-top .recent-post-widget .post-item + .post-item,
    .rs-footer .footer-top .news-form input{
        border-color: #92278f;
    }
    
    .searce-box i,
    #rs-slider .slide-content .slider-desc,
    .copyright a,
    .rs-footer .footer-top .sitemap-widget li a:hover,
    .rs-footer .footer-title,
    .rs-latest-news .news-list-block .news-list-item .news-title a,
    .rs-latest-news .news-normal-block .news-title a,
    .rs-events .event-item .event-btn a,
    .rs-events .event-item .event-title a{
        color: #92278f;
    }
    .copyright a:hover,
    .rs-footer .footer-top .recent-post-widget .post-item .post-title a:hover{
        color: #5d0b5b;
    }
    .rs-footer .footer-top .news-form button:hover{
        background: #5d0b5b;
    }
    .rs-footer .footer-share ul li a:hover{
        color: #00bcd4;
    }
    .rs-footer .footer-contact-desc .contact-inner i{
        color: #e84b3a;
    }
    .rs-footer{
        color: @body-color;
        .footer-top .sitemap-widget li a,
        .footer-top .recent-post-widget .post-item .post-title a{
            color: @body-color;
        }
    }
    .slider-overlay-2 .slide-content:after{
        display: none;
    }
    .rs-footer .footer-top .news-form input{
        color: #000;
    }
    .rs-footer .footer-contact-desc{
        border-radius: 30px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
    }
    .rs-footer .footer-bottom{
        background: #269aa9;
    }
    .rs-toolbar .rs-toolbar-left .welcome-message i{
        color: #fff;
    }
}
.university-home{
    .searce-box i{
        color: @primary-color;
    }
    #scrollUp i,
    .owl-nav > div:hover,{ 
        background: @primary-color;
    }
}

/* ------------------------------------
    04. Sticky Menu
---------------------------------------*/
.menu-sticky.sticky {
    background: #111111;
    position: fixed !important;
    top: 0px;
    z-index: 999;
    margin: 0 auto !important;
    padding: 0;
    left: 0;
    right: 0;
    -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.2);
    -webkit-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-name: sticky-animation;
    animation-name: sticky-animation;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    .main-menu {
        position: static ;
    }
}

.rs-header-2 .menu-sticky1.sticky1 .nav-menu > li > a {
    height: 80px;
    line-height: 80px;
}

#nav-close {
    transition: @transition;
    &:hover {
        background-color: @primary-color;
        border-color: @primary-color;
        color: #fff;
    }
}

@-webkit-keyframes sticky-animation {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
  }
}
@keyframes sticky-animation {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}


/* ------------------------------------
    05. Slider
---------------------------------------*/
#rs-slider {
    position: relative;
    .slide-content {
        position: absolute;
        left: 0;
        top: 0;
        color: #fff;
        width: 100%;
        height: 100%;
        z-index: 8;
        .slider-title {
            font-size: 60px;
            color: #fff;
            margin-bottom: 15px;
            -webkit-animation-duration: 1.4s;
            animation-duration: 1.4s;
            text-transform: uppercase;
        }
        .slider-desc {
            font-weight: 500;
            margin-bottom: 30px;
            font-size: 20px;
        }
        .sl-readmore-btn {
            border: 2px solid #101010;
            background: #101010;
            display: inline-block;
            padding: 12px 25px;
            text-transform: uppercase;
             font-size: 16px;
            text-transform: uppercase;
            color: #ffffff;
            font-weight: 500;
            text-align: center;
            transition: @transition;
            &:hover {
                background-color: @hover-color;
                border-color: @hover-color;
                color: rgba(255,255,255,0.8);
            }
        }
        .sl-get-started-btn {
            background-color: @primary-color;
            border: 2px solid @primary-color;
            display: inline-block;
            padding: 12px 25px;
            font-size: 16px;
            text-transform: uppercase;
            color: #ffffff;
            font-weight: 500;
            text-align: center;
            transition: @transition;
            &:hover {
                background-color: @hover-color;
                border-color: @hover-color;
                color: rgba(255,255,255,0.8);
            }
        }
    }
    .owl-nav {
        display: none;
    }
    .owl-dots {
        position: absolute;
        right: 50px;
        top: 50%;
        transform: translateY(-50%);
        .owl-dot {
            height: 15px;
            width: 5px;
            background-color: #ffffff;
            + .owl-dot {
                margin-top: 8px;
            } 
            &.active {
                background-color: @primary-color;
            }
        }
    }
}
.slider-overlay-1 {
    .slide-content {
        &:after {
            position: absolute;
            content: '';
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            background: rgba(0,0,0,0.4);
            z-index: -1;
        }
    }
}
.slider-overlay-2 {
    .slide-content {
        &:after {
            position: absolute;
            content: '';
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            background: rgba(255, 109, 0, 0.3);
            z-index: -1;
            background: -moz-linear-gradient(top, rgba(0, 0, 0, 0.55) 0%, rgba(0, 0, 0, 0.5) 50%,rgba(255, 109, 0, 0.3) 100%);
            background: linear-gradient(top, rgba(0, 0, 0, 0.55) 0%, rgba(0, 0, 0, 0.5) 50%,rgba(255, 109, 0, 0.3) 100%);
            background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0.55) 0%, rgba(0, 0, 0, 0.5) 50%,rgba(255, 109, 0, 0.3) 100%);
            background-image: -o-linear-gradient(top, rgba(0, 0, 0, 0.55) 0%, rgba(0, 0, 0, 0.5) 50%,rgba(255, 109, 0, 0.3) 100%);
            background-image: -ms-linear-gradient(top, rgba(0, 0, 0, 0.55) 0%, rgba(0, 0, 0, 0.5) 50%,rgba(0, 0, 0, 0.3) 100%);
            background: -webkit-gradient(linear, right top, right bottom, color-stop(0%,rgba(0, 0, 0, 0.55)), color-stop(50%,rgba(0, 0, 0, 0.5)),color-stop(100%,rgba(0, 0, 0, 0.3)));
            opacity: 0.95;
        }
    }
}
.home5{
    span.red-color{
         color: #e84b3a;  
    }
    span.orange-color{
         color: #fc7f0c;    
    }
    #rs-slider{
        position: relative;
        .slide-content{
            .slider-title{
                color: #92278f;
            }
            .slider-title,
            .slider-desc,
            .sl-get-started-btn{
                position: relative;
                z-index: 11;
            }
            .sl-get-started-btn{
                border: none;
                background: #92278f;
                border-radius: 30px;
                font-weight: 700;
                padding-top: 16px;
                padding-bottom: 16px;
                font-size: 18px;
                &:hover{
                    background: #550b53;    
                }
            }
        }
        .owl-dots{
            display: none !important;
        }
    }
}
.rs-banner-section{
    position: relative;
    overflow: hidden;
    img.banner-image{
        width: 100%;
    }
    .banner-inner{
        position: absolute;
        width: 100%;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        .sl-sub-title{
            margin-bottom: 2px;
            font-size: 56px;
            font-family: @title-font;
            line-height: 70px;
            color: #fff;
            font-weight: 600;
            font-style: italic;
        }
        .banner-title{
            font-size: 75px;
            line-height: 75px;
            margin: 0 0 25px;
            font-weight: 700;
            color: #fff;
            span{
                display: block;
                color: @primary-color;
            }
        }
        .content{
            color: #ffffff;
            font-size: 20px;
            line-height: 26px;
            font-weight: 400;
            margin-bottom: 40px;
        }
        .readon{
            text-transform: uppercase;
            letter-spacing: 3px;
            font-size: 16px;
            padding: 15px 25px;
        }
    }
    .banner-text{
        max-width: 700px;
    }
}
.rs-banner-section2{
    position: relative;
    overflow: hidden;
    img.banner-image{
        width: 100%;
    }
    .banner-inner{
        position: absolute;
        width: 100%;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        .sl-sub-title,
        .banner-title,
        .banner-title2{
            color: #fff;
        }
        .sl-sub-title{
            font-size: 30px;
            font-family: @title-font;
            line-height: 50px;
            font-weight: 400;
        }
        .banner-title{
            margin: 0 0 6px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 90px;
            line-height: 1.1;
            letter-spacing: 5px;
            span{
                display: block;
                line-height: 1;
            }
        }
        .content{
            color: #ffffff;
            font-size: 20px;
            line-height: 26px;
            font-weight: 400;
            margin-bottom: 40px;
        }
        .readon{
            text-transform: uppercase;
            font-size: 17px;
            padding: 15px 45px;
            background: transparent;
            border: 2px solid #fff;
            border-radius: 30px;
            font-weight: 600;
            &:hover{
                background: #ffffff;
                color: @primary-color;
            }

        }
    }
    .animate {
        .circle1 {
            position: absolute;
            right: 40%;
            bottom: 50px;
            height: 150px;
            animation: swing-anim 2s alternate-reverse infinite;
            -webkit-animation: swing-anim 2s alternate-reverse infinite;
        }
        .circle2 {
            position: absolute;
            right: 25%;
            top: 300px;
            text-align: right;
            width: 150px;
            animation: swing-anim2 2s alternate-reverse infinite;
            -webkit-animation: swing-anim2 2s alternate-reverse infinite;
        }
    }
}
.rs-banner-section3{
    background: url(images/bg/home8-banner-bg.jpg);
    background-position: center top;
    background-repeat: no-repeat;
    padding-top: 180px;
    padding-bottom: 100px;
    background-size: cover;
    .countdown-part{
        .sub-title{
            display: block;
            font-size: 18px;
            line-height: 34px;
            color: @primary-color;
            font-weight: 600;
        }
        .title{
            font-size: 60px;
            line-height: 70px;
            font-weight: 600;
            color: @white-color;
            text-transform: uppercase;
        }
        .counter-wrap {
            max-width: 550px;
            .timecounter-inner {
                .time_circles {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    canvas{
                        opacity: 1;
                    }
                    div{
                        position: absolute;
                        text-align: left;
                        padding-right: 0;
                        span {
                            padding: 0;
                            display: block;
                            width: 100%;
                            text-align: center;
                            font-weight: 700;
                            font-size: 40px !important;
                            margin: 0 0 9px 0;
                            color: @primary-color;
                        }
                        h4{
                            color: @white-color;
                            margin: 0;
                            padding: 0;
                            text-align: center;
                            font-weight: 400;
                            text-transform: capitalize;
                            line-height: 17px;
                            font-size: 12px !important;
                        }
                    }
                }
            }
        }
    }
    .register-form{
        background: rgba(255,255,255,.6);
        .form-title{
            position: relative;
            padding: 30px 65px 36px;
            margin-bottom: 50px;
            .title{
                font-weight: 400;
                color: #fff;
                position: relative;
                z-index: 10;
                line-height: 1.4;
                font-size: 22px;
            }
           &:after{
               position: absolute;
               height: 100%;
               width: 100%;
               clip-path: polygon(-115.5% 0,113% 0,76% 100%);
               left: 0;
               background-color: @primary-color;
               top: 0;
               content: "";
           }
        }
        .form-group{
            padding: 0 50px 5px;
            .from-control{
                width: 100%;
                margin-bottom: 20px;
                border-radius: 3px;
                height: 50px;
                line-height: 50px;
                padding-top: 0;
                padding-bottom: 0;
                font-size: 15px;
                padding: 10px 14px;
                border: 1px solid rgba(54,54,54,.1);
                outline: 0;
            }
            input[type=submit]{
                width: 100%;
                margin-bottom: 50px;
                border-radius: 3px;
                height: 50px;
                line-height: 50px;
                font-size: 15px;
                -webkit-appearance: button;
                cursor: pointer;
                background: @primary-color;
                border: 0;
                color: #fff;
                font-size: 15px;
                text-transform: uppercase;
                outline: 0;
                transition: 0.4s;
                font-weight: 600;
                &:hover{
                    color: @white-color;
                    background: @hover-color;
                }
            }
            .select-option {
                position: relative;
                select {
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    appearance: none;
                    cursor: pointer;
                    color: #b1b1b1;
                    opacity: 1;
                    z-index: 11; 
                    position: relative;                   
                }
                &:after {
                    content: '\f107';
                    font-family: FontAwesome;
                    color: #b1b1b1;
                    right: 15px;
                    position: absolute;
                    top: 11px;
                    z-index: 12;
                    font-size: 22px;
                    pointer-events: none;
                }
            }
        }
    }
}
/* ------------------------------------
    06. Breadcrumbs
---------------------------------------*/
.rs-breadcrumbs {
    padding: 100px 0 35px;
    .page-title {
        margin: 80px 0 100px;
        font-size: 36px;
        color: #ffffff;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    ul {
        padding: 0;
        li {
            color: @primary-color;
            display: inline-block;
            font-weight: 400;
            a {
                position: relative;
                padding-right: 30px;
                -webkit-transition: 0.3s;
                transition: 0.3s;
                color: #ffffff;
                &:before, &:after {
                    background-color: rgba(255,255,255,0.8);
                    content: "";
                    height: 15px;
                    width: 2px;
                    position: absolute;
                    right: 7px;
                    top: 2px;
                    transform: rotate(26deg);
                }
                &:before {
                    right: 13px;
                }
                &:hover {
                    color: @primary-color;
                }                
            }
        }
    }
}
.breadcrumbs-overlay {
    position: relative;
    z-index: 1;
    &:after {
        content: '';
        position: absolute;
        background-color: rgba(17, 17, 17, 0.8);
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
    }
} 

/* ------------------------------------
    07. Services
---------------------------------------*/
.rs-services-style7{
    .Services-wrap{
        .Services-item{
            position: relative;
            overflow: hidden;
            &:before{
                position: absolute;
                content: "";
                background: rgba(0,0,0,.4);
                width: 100%;
                height: 100%;
                left: 0;
                top: 0;
                z-index: 1;
                opacity: 0;
                visibility: hidden;
                transition: all .3s ease 0s;
                border-radius: 8px;
            }
            .Services-icon{
                img{
                    border-radius: 8px;
                }
            }
            .Services-desc{
                position: absolute;
                bottom: 25px;
                left: 0;
                text-align: center;
                width: 100%;
                transition: all .5s ease 0s;
                z-index: 10;
                i{
                    webkit-animation-name: rs-animation-scale-up;
                      animation-name: rs-animation-scale-up;
                    display: none;
                    &:before{
                        color: @primary-color;
                        font-size: 40px;
                    }
                }
                .services-title{
                    font-size: 22px;
                    line-height: 30px;
                    font-weight: 500;
                    margin: 0;
                    a{
                        color: @white-color;
                    }
                }
            }
            &:hover{
                &:before{
                    opacity: 1;
                    visibility: visible;
                }
                .Services-desc{
                    bottom: 50%;
                    transform: translateY(50%);
                    i{
                        display: block;
                    }
                }
            }

        }
    }
    .content-part{
        .sub-text{
            display: block;
            font-size: 18px;
            line-height: 34px;
            color: @primary-color;
            font-weight: 600;
        }
        .title{
            font-size: 30px;
            line-height: 40px;
            color: @title-color;
            font-weight: 700;
            margin: 0;
        }
        p{
            font-size: 16px;
            line-height: 27px;
            color: @body-color;
            margin: 0;
        }
    }
}


/* ------------------------------------
    07. Services
---------------------------------------*/
.rs-services-style1 {
    .services-item {
        background-color: #212121;
        padding: 25px 17px 17px;
        position: relative;
        box-shadow: 0 4px 2px -2px #000;
        z-index: 111;
        position: relative;
        top: -30px;
        transition: @transition;
        &:hover {
            background-color: @primary-color;
            .services-icon {
                background-color: @primary-color;
            }
			.services-icon,
			.services-desc p{
				color: #fff;
			}
        }
    }
    .services-desc {
        text-align: center;
        margin-top: 6px;
        p {
            color: #cccccc;
            margin-bottom: 0;            
        }
        .services-title {
            position: relative;
            color: #ffffff;
            font-size: 20px;
            margin-bottom: 7px;
            z-index: 10;
        }
    }
    .services-icon {
        height: 130px;
        width: 130px;
        background-color: #212121;
        line-height: 88px;
        text-align: center;
        position: absolute;
        top: -50px;
        z-index: -1;
        font-size: 40px;
        border-radius: 50%;
        left: 0;
        right: 0;
        margin: 0 auto;
        color: @primary-color;
        transition: @transition;
        i {
            display: inline-block;
        }
    }
}

.home5{
    .rs-services-style1{
        .services-item{
            margin-top: 0;
            padding-bottom: 20px;
            border-radius: 0 0 10px 10px;
            &.blue-color{
                box-shadow: 0px 4px 13px #046875;
            }
            &.blue-color,
            &.blue-color .services-icon{
                background: #00bcd4;
            }
            &.orange-color{
                box-shadow: 0px 4px 13px #9c510b;
            }
            &.orange-color,
            &.orange-color .services-icon{
                background: #fc7f0c;
            }
            &.purple-color{
                box-shadow: 0px 4px 13px #50154f;
            }
            &.purple-color,
            &.purple-color .services-icon{
                background: #92278f;
            }
            &.red-color{
                box-shadow: 0px 4px 13px #75231a;
            }
            &.red-color,
            &.red-color .services-icon{
                background: #e84b3a;
            }
            .services-icon{
                color: #fff;
                top: -50px;
                line-height: 90px;
                font-size: 40px;
            }
            .services-desc{
                position: relative;
                z-index: 111;
                margin-top: 6px;
                .services-title{
                    margin-bottom: 6px;
                    position: relative
                }
                p{
                    color: #eee;
                }
            }
        }
    }
}

/* ------------------------------------
    34. About Us Style8
---------------------------------------*/
.rs-about-style8{
    ul{
        list-style: none;
        margin: 0;
        flex-wrap: wrap;
        display: flex;
        padding-top: 0;
        li{
            flex-basis: 34%;
            position: relative;
            padding-left: 24px;
            padding-bottom: 7px;
            &:before{
                position: absolute;
                top: 0;
                left: 0;
                z-index: 0;
                content: "\f05d";
                font-family: FontAwesome;
                color: #ff3115;
                transition: all .5s ease;
            }
        }
    }
    .author-section{
        .course-author{
            display: flex;
            align-items: center;
        }
        .align-img{
            margin-right: 20px;
            img{
                width: 80px;
                height: 80px;
                border-radius: 50%;
                margin: 0;
            }
        }
        .author-content{
            h4{
                font-size: 20px;
                font-weight: 700;
                line-height: 30px;
                color: @title-color;
            }
            p{
                margin-bottom: 0;
            }
        } 
    }
}

/* ------------------------------------
    08. About Us Style
---------------------------------------*/
.rs-about-style7{
    .content-part{
        .play-btn {
             position: relative;
            .pulse-btn {
                position: absolute;
                background: @hover-color;
                width: 80px;
                height: 80px;
                line-height: 80px;
                border-radius: 50%;
                margin: 0 auto;
                display: block;
                text-align: center;
                left: 50%;
                transform: translateX(-50%);
                bottom: 40px;
                font-size: 30px;
                color: @white-color;
                z-index: 1;
                &:before {
                    content: "";
                    position: absolute;
                    z-index: -1;
                    left: 50%;
                    top: 50%;
                    transform: translateX(-50%) translateY(-50%);
                    display: block;
                    width: 80px;
                    height: 80px;
                    background: transparent;
                    border: 2px solid @hover-color;
                    border-radius: 50%;
                    transition: all 200ms;
                    animation: pulse-border 1500ms ease-out infinite;
                }
                &:after {
                    content: "";
                    position: absolute;
                    z-index: -2;
                    left: 50%;
                    top: 50%;
                    transform: translateX(-50%) translateY(-50%);
                    display: block;
                    width: 80px;
                    height: 80px;
                    background: transparent;
                    border-radius: 50%;
                    transition: all 200ms;
                    border: 2px solid @hover-color;
                }
            }
        }
        .sub-title{
            display: block;
            font-size: 18px;
            color: @primary-color;
            font-weight: 600;
        }
        .title{
            font-size: 30px;
            font-weight: 700;
            line-height: 40px;
            color: @white-color;
            margin: 0;
        }
        p{
           font-size: 15px;
           font-weight: 400;
           line-height: 27px;
           color: @white-color;
           margin: 0; 
        }
    }
}
.rs-about-style8{
    .content-part{
        .play-btn {
            position: relative;
            .pulse-btn {
                position: absolute;
                background: @hover-color;
                width: 80px;
                height: 80px;
                line-height: 80px;
                border-radius: 50%;
                margin: 0 auto;
                display: block;
                text-align: center;
                left: 50%;
                transform: translateX(-50%);
                font-size: 30px;
                color: @white-color;
                z-index: 1;
                &:before {
                    content: "";
                    position: absolute;
                    z-index: -1;
                    left: 50%;
                    top: 50%;
                    transform: translateX(-50%) translateY(-50%);
                    display: block;
                    width: 80px;
                    height: 80px;
                    background: transparent;
                    border: 2px solid @hover-color;
                    border-radius: 50%;
                    transition: all 200ms;
                    animation: pulse-border 1500ms ease-out infinite;
                }
                &:after {
                    content: "";
                    position: absolute;
                    z-index: -2;
                    left: 50%;
                    top: 50%;
                    transform: translateX(-50%) translateY(-50%);
                    display: block;
                    width: 80px;
                    height: 80px;
                    background: transparent;
                    border-radius: 50%;
                    transition: all 200ms;
                    border: 2px solid @hover-color;
                }
            }
        }
       
        .title{
            font-size: 30px;
            font-weight: 700;
            line-height: 40px;
            color: @white-color;
            margin: 0;
        }
    }
}



/* ------------------------------------
    08. About Us
---------------------------------------*/
.rs-about {
    .about-img {
        position: relative;
        &:after {
            content:"";
            position: absolute;
            left: 0;
            bottom: 0;
            height: 100%;
            width: 100%;
            background-color: rgba(33, 33, 33, 0.5);
            opacity: 0;
            -webkit-transition: @transition;
            transition: @transition;
        }
        &:hover {
           &:after {
                opacity: 1;
           }   
        }
        .popup-youtube {
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 10;
            -webkit-transform: translateX(-50%) translateY(-50%);
            transform: translateX(-50%) translateY(-50%);
            &:after {
                position: absolute;
                font-family: FontAwesome;
                content: "\f04b";
                font-size: 36px;
                left: 50%;
                -webkit-transform: translateX(-50%) translateY(-50%);
                transform: translateX(-50%) translateY(-50%);
                transition: all 0.3s ease 0s;
                display: inline-block;
                height: 80px;
                line-height: 80px;
                width: 80px;
                text-align: center;
                background: @primary-color;
                border-radius: 50%;
                padding-left: 8px;
                color: #fff;
            }
        }
    }
    .about-desc {
        margin-bottom: 30px;
        h2 {
            font-size: 30px;
            line-height: normal;
            margin-bottom: 20px;
            text-transform: uppercase;
            position: relative;
        }
        p {
            font-size: 15px;
            line-height: 26px;
            margin-bottom: 0;
        }
    }    
}
.vision-desc p:last-child,
.about-desc p:last-child{
    margin-bottom:  0;
}
.home5{
    .sec-title{
        margin-bottom: 20px;
        h3:after{
            left: 0;
            -webkit-transform: translateX(0);
            transform: translateX(0);
        }
    }
    .acdn-title{
        color: #92278f;
    }
    #accordion{
        .card{
            .card-header{
                .acdn-title:not(.collapsed){
                    background: #92278f
                }
            }
        }  
    }
    
}

.rs-about-2 {
    .sec-title {
        h3 {
            font-size: 30px;
        }
    }
    .about-signature {
        h4{
            margin-bottom: 0;
            font-size: 20px;
        }
        span {
            font-size: 14px;
        }
    }
}
#rs-about{
	margin-top: -30px;
}

/* ------------------------------------
    30. Course Categories
---------------------------------------*/
.rs-courses-style7{
    .item{
        text-align: center;
        margin-bottom: 50px;
        border-radius: 8px;
        position: relative;
        .cource-img{
            img{
                border-radius: 8px;
            }
        }
        .content-part{
            position: absolute;
            bottom: 30px;
            left: 0;
            text-align: center;
            width: 100%;
            transition: all .5s ease 0s;
            z-index: 10;
            .btn-part {
                transition: @transition;
                opacity: 0;
                i{
                    &:before{
                        display: inline-block;
                        width: 60px;
                        height: 60px;
                        line-height: 60px;
                        text-align: center;
                        background-color: @primary-color;
                        color: @white-color;
                        border-radius: 50%;
                        font-size: 30px;
                    }
                }
            }
            .cource-title{
                font-size: 23px;
                margin-bottom: 0;
                margin-top: 12px;
                font-weight: 600;
                a{
                    color: @white-color;
                    transition: @transition;
                }
            }
            .courses-sub{
                font-size: 13px;
                font-weight: 400;
                color: #fff;
                transition: @transition;
            }
        }
        &:after{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            content: "";
            background: #fff;
            opacity: 0;
            visibility: hidden;
            border-radius: 8px;
            transition: @transition;
        }
        &:hover{
            .content-part{
                .btn-part {
                    opacity: 1 !important;
                }
                .cource-title {
                    a {
                        color: @title-color;
                    }
                }
                .courses-sub {
                    color: @body-color;
                }
            }
            &:after{
                opacity: 0.8;
                visibility: visible;
            }
        }       
    }
}


/* ------------------------------------
    09. rs courses style
---------------------------------------*/
.rs-courses {
    .cource-item {
        border: 1px solid #dddddd;
        .cource-img {
            position: relative;
            margin-bottom: 22px;
            img {
                width: 100%;
                transition: @transition;
            }
            .course-value {
                position: absolute;
                bottom: -25px;
                right: 25px;
                height: 50px;
                width: 50px;
                line-height: 50px;
                text-align: center;
                background-color: @primary-color;
                color: #ffffff;
                font-size: 14px;
                font-weight: 600;
                border-radius: 50%;
                z-index: 1;
            }
        }
        .course-body {
            text-align: center;
            padding: 0 20px;
            .course-category {
                display: inline-block;
            }
            .course-title {
                font-size: 20px;
                margin-bottom: 7px;
                a {
                    color: #212121;
                    &:hover {
                        color: rgba(33, 33, 33, 0.6);
                    }
                }
            }
            .review-wrap {
                .rating {
                    display: inline-block;
                    color: @primary-color;
                    margin-right: 10px;
                    padding-right: 10px;
                    border-right: 1px solid #ddd;
                    line-height: 10px;
                }
                .review {
                    line-height: 10px;
                    display: inline-block;
                }
            }
            .course-desc {
                margin-top: 10px;
				p{
					margin: 0 0 22px;
				}
            }
        }
        .course-footer {
            padding: 15px 20px;
            background-color: #f0f0f0;
            text-align: center;
             div {
                display: inline-block;
                text-align: center;
                position: relative;
                line-height: 19px;
                span {
                    display: block;
                    font-size: 14px;
                    color: #212121;
                    font-weight: 500;
                    &.label {
                        font-weight: 700;
                    }
                }
                div {
                    padding-left: 8px;
                    margin-left: 8px;
                    border-left: 1px solid #ccc;
                }
            } 
        }
        &.blue-color{
            .course-footer{
                background: #00bcd4;
            }
        }  
        &.orange-color{
            .course-footer{
                background: #fc7f0c;   
            }
        }  
        &.purple-color{
            .course-footer{
                background: #92278f;    
            }
        }  
        &.red-color{
            .course-footer{
                background: #e84b3a;  

                box-shadow: 0 12px 20px #f4f5ff;  
            }
        } 
    }
    &.rs-courses-style6{
        .cource-item{
            margin-bottom: 30px;
            box-shadow: 0 0 4px rgba(0,0,0,.06) !important;
            background: #ffffff !important;
            min-height: 375px;
            border-radius: 8px;
            .cource-img{
                img{
                    border-radius: 8px;
                }
                   .course-value{
                        bottom: 0;
                        right: 0;
                        top: 20px;
                        height: 30px;
                        width: 85px;
                        line-height: 30px;
                        text-align: center;
                        background-color: @primary-color;
                        color: #fff;
                        font-size: 13px;
                        font-weight: 500;
                        border-radius: 5px 0 0 5px;
                   }
                .img-part{
                      img{
                       width: 50px;
                           height: 50px;
                           border-radius: 50%;
                           border: 2px solid #ff3115;
                           position: absolute;
                           bottom: -20px;
                           left: 50%;
                           transform: translateX(-50%);
                           z-index: 1;
                    }
                }
            }
            .course-body{
                background: #ffffff !important;
               span{
                    font-size: 14px;
                    line-height: 26px;
                    font-weight: 600;
                    a{
                        color: @body-color;
                        &:hover{
                            color: @primary-color;
                        }
                    }
               }
                .title{
                    font-size: 20px;
                    line-height: 30px;
                    font-weight: 600;
                    margin: 0;
                    a{
                        color: @title-color;
                        &:hover{
                           color: @primary-color;
                        }
                    }
                }
            }
            .course-footer{
                background: none;
                .courses-seats{
                    margin-right: 30px;
                    i{
                        &:before{
                            color: @body-color;
                            font-size: 18px;
                            padding-right: 5px;
                        }
                    }
                }
                .review-wrap{
                    .rating{
                        li{
                            color: @primary-color;
                            font-size: 18px;
                        }
                    }
                }
            }
        }
        
    }
    &.rs-courses-style7-new2{
        .cource-item{
            margin-bottom: none;
            box-shadow: 0 0 4px rgba(0,0,0,.06) !important;
            background: #ffffff !important;
            min-height: 345px;
            border-radius: 0 0 8px 4px !important;
            .cource-img{
                img{
                }
                   .course-value{
                        bottom: 0;
                        right: 0;
                        height: 30px;
                        width: 85px;
                        line-height: 30px;
                        text-align: center;
                        background-color: @primary-color;
                        color: #fff;
                        font-size: 13px;
                        font-weight: 500;    
                        border-radius: 5px 0 0 5px;
                   }
                .img-part{
                      img{
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                        border: 2px solid #ff3115;
                        position: absolute;
                        bottom: -20px;
                        left: 45px;
                        transform: translateX(-50%);
                        z-index: 1;
                    }
                }
            }
            .course-body{
                background: #ffffff !important;
                text-align: left;
               span{
                    font-size: 14px;
                    line-height: 26px;
                    font-weight: 600;
                    a{
                        color: @body-color;
                        &:hover{
                            color: @primary-color;
                        }
                    }
               }
                .title{
                    font-size: 20px;
                    line-height: 30px;
                    font-weight: 600;
                    margin: 0;
                    a{
                        color: @title-color;
                        &:hover{
                           color: @primary-color;
                        }
                    }
                }
            }
            .course-footer{
                background: none;
                text-align: left;
                padding-bottom: 20px;
                .courses-seats{
                    margin-right: 30px;
                    i{
                        &:before{
                            color: @body-color;
                            font-size: 18px;
                            padding-right: 5px;
                        }
                    }
                }
                .review-wrap{
                    .rating{
                        li{
                            color: @primary-color;
                            font-size: 18px;
                        }
                    }
                }
                .desc{
                    font-size: 15px;
                    color: @body-color;
                    line-height: 27px;
                    margin: 0;
                }
            }
        }
        
    }

}
.event-item .events-details{
	margin-top: -20px;
	padding: 15px;
    &.white-bg{
        background: #fff;
    }
}
.event-item,
.cource-item {
    .event-img,
    .cource-img {
        position: relative;
        &:after{
            position: absolute;
            content: "";
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: rgba(0,0,0,0.6);
            -webkit-transform: scaleY(0);
            -ms-transform: scaleY(0);
            transform: scaleY(0);
            -webkit-transition: 0.4s;
            -ms-transition: 0.4s;
            transition: 0.4s;
        }
        .image-link{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) !important;
            -webkit-transform: translate(-50%, -50%) !important;
            -ms-transform: translate(-50%, -50%) !important;
            color: @primary-color;
            z-index: 11;
			&:hover{
				color: #fff;
			}
            i{
                font-size: 22px;
                font-weight: 400;
                -webkit-transform: scale(0);
                -ms-transform: scale(0);
                transform: scale(0);
                transition: @transition;
            }
        }
    }

    &:hover{
        .event-img,
        .cource-img{
            &:after{
                -webkit-transform: scaleY(1);
                -ms-transform: scaleY(1);
                transform: scaleY(1);
            }
            .image-link{
                i{
                    -webkit-transform: scale(1);
                    -ms-transform: scale(1);
                    transform: scale(1);
                }
            }
        }
    }
}
.home5 {
    .rs-courses {
        .cource-item{
             border-color: #eee;
             background: #eee;
            border-radius: 0 0 30px 30px;
            .cource-img{
                margin-bottom: 0;
            }
            .course-body{
                padding-top: 30px;
                background: #eee;
                .course-title a{
                    color: #92278f;
                }
            }
            .course-footer {
                border-radius: 30px;
                > div span{
                    color: #fff;
                }
            }
        }
    }
}
.rs-courses-2 {
    padding-bottom: 70px;
    .cource-item {
        margin-bottom: 30px;
        border: 1px solid #dddddd;
        .cource-img {
            position: relative;
            overflow: hidden;
            img {
                -webkit-transition: all .3s ease;
                transition: all .3s ease;
                width: 100%;
            }
            .course-value {
                position: absolute;
                bottom: -25px;
                right: 25px;
                height: 50px;
                width: 50px;
                line-height: 50px;
                text-align: center;
                background-color: @primary-color;
                color: #ffffff;
                font-size: 13px;
                font-weight: 500;
                border-radius: 50%;
            }
        }
        .course-body {
            padding: 20px 25px 25px;
            transition: all 0.5s ease 0s;
            .course-category {
                display: inline-block;
            }
            .course-title {
                font-size: 20px;
                margin-bottom: 7px;
                a {
                    color: #212121;
                    transition: all 0.35s ease 0s;
                    &:hover {
                        color: rgba(33, 33, 33, 0.6);
                    }
                }
            }
            .course-desc {
                margin-top: 12px;
                p {
                    margin-bottom: 0;
                    transition: all 0.35s ease 0s;
                }
            }
        }
        .cource-btn {
            background-color: @primary-color;
            border: 2px solid @primary-color;
            display: inline-block;
            margin-top: 20px;
            padding: 2px 10px;
            font-size: 11px;
            text-transform: uppercase;
            color: #ffffff;
            font-weight: 700;
            transition: all 0.3s ease 0s;
            &:hover {
                background-color: @hover-color;
                border-color: @hover-color;
            }
        }
        &:hover {
            .course-body {
                background-color: #212121;
                .course-title a {
                    color: #fff;
                }
                p {
                    color: rgba(255,255,255,0.7);
                }
            }
            .cource-btn {
                background-color: #fff;
                border-color: #fff;
                color: @primary-color;
                &:hover {
                    background-color: @hover-color;
                    border-color: @hover-color;
                    color: #fff;
                }
            }
            .cource-img {
                img {
                    -webkit-transform: scale(1.1);
                    transform: scale(1.1);
                    opacity: 0.8;
                }
            }
        }
    }
    .view-more {
        bottom: auto;
    }
    .container{
    	overflow: hidden;
    }
    .row.grid{
    	min-height: 500px !important;
    }
}
.rs-courses-3 {
	.row.grid{
		min-height: 500px;
		overflow: hidden;
	}
    .course-item {
        margin-bottom: 30px;
        border: 1px solid #dddddd;
        transition: all 0.35s ease 0s;
        .course-img {
            position: relative;
            margin: -1px -1px 0 -1px;
            &:before {
                background: rgba(0, 0, 0, 0.5);
                background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
                background: -o-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
                background: -moz-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
                background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.6));
                position: absolute;
                content: "";
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                -webkit-transition: all 1s ease;
                transition: all 1s ease;
            }
            img {
                width: 100%;
            }
            .course-value {
                position: absolute;
                bottom: -30px;
                right: 22px;
                height: 60px;
                width: 60px;
                line-height: 60px;
                text-align: center;
                background-color: @primary-color;
                color: #ffffff;
                font-size: 14px;
                font-weight: 600;
                border-radius: 50%;
            }
        }
        .course-toolbar {
            position: absolute;
            width: 100%;
            bottom: 15px;
            padding: 0 20px;
            .course-category {
                margin-bottom: 3px;
                a {
                    font-size: 24px;
                    color: #ffffff;
                    &:hover{
                        color: @primary-color;
                    }
                }
            }
            .course-date, 
            .course-duration {
                display: inline-block;
                font-size: 14px;
                text-transform: uppercase;
                color: #ffffff;
                font-weight: 600;
                i {
                    margin-right: 3px;
                }
            }
            .course-duration {
                margin-left: 12px;
            }
        }
        .course-body {
            .course-title {
                font-size: 20px;
                margin-bottom: 7px;
                a {
                    color: #212121;
                    &:hover {
                        color: rgba(33, 33, 33, 0.6);
                    }
                }
            }
            .course-desc {
                padding: 20px;
                p {
                    margin-bottom: 0;
                    transition: all 0.35s ease 0s;
                }
            }
        }
        &:hover {
            .course-img {
                &:before {
                    background: rgba(0, 0, 0, 0.5);
                    background: -webkit-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
                    background: -o-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
                    background: -moz-linear-gradient(top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
                    background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
                }
            }
            box-shadow: 0 6px 40px rgba(0, 0, 0, 0.1);
        }
    }
    .course-footer {
        padding: 15px 20px;
        background-color: #f0f0f0;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-justify-content: space-between;
        justify-content: space-between;
        font-weight: 600;
		font-size: 15px;
        .course-button {
            a {
                color: #505050;
                display: inline-block;
                position: relative;
                padding-right: 12px;
                &:hover {
                    color: @primary-color;
                }
                &:after {
                    font-family: fontAwesome;
                    content: "\f101";
                    position: absolute;
                    right: 0;
                }
            }
        }
    }
    .view-more {
        bottom: auto;
    }
}
.rs-courses-list {
    .course-item {
        margin-bottom: 40px;
        .course-img {
            position: relative;
            &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(33, 33, 33, 0.8);
                transition: @transition;
                opacity: 0;
            }
            .image-link {
                position: absolute;
                top: 50%;
                left: 50%;
                width: 50px;
                line-height: 50px;
                height: 50px;
                border-radius: 50%;
                text-align: center;
                background-color: @primary-color;
                color: #ffffff;
                font-size: 18px;
                transform: translateX(-50%) translateY(-50%) scale(0.7);
                transition: @transition;
                opacity: 0;
                &:hover {
                    background-color: @hover-color;
                }
            }
            &:hover {
                &:before {
                    opacity: 1;
                }
                .image-link {
                    opacity: 1;
                    transform: translateX(-50%) translateY(-50%) scale(1);
                }
            }        
        }
    }
    .course-category {
        margin-bottom: 0;
        font-size: 15px;
        font-weight: 600;
        a {
            color: #505050;
        }
    }
    .course-title {
        font-size: 24px;
        margin-bottom: 7px;
        a {
            color: #212121;
            &:hover {
                color: @primary-color;
            }
        }
    }
    .course-date, .course-value {
        display: inline-block;
        font-size: 14px;
        color: #777777;
        font-weight: 600;
    }
    .course-value {
        margin-left: 12px;
        span {
            color: @primary-color;
        }
    }
    .course-body {
        margin-top: 12px;
    }
    .course-button {
        a {
            display: inline-block;
            height: 42px;
            line-height: 42px;
            text-align: center;
            min-width: 170px;
            padding: 0 20px;
            background-color: @primary-color;
            color: #fff;
            transition: @transition;
            font-weight: 500;
            &:hover {
                background-color: @hover-color;
            }
        }
    }
}
.gridFilter {
    margin-bottom: 35px;
    button {
        background: transparent;
        border: 0;
        font-size: 15px;
        font-weight: 700;
        outline: none;
        color: #505050;
        cursor: pointer;
        &.active {
            color: @primary-color;
        }
        + button {
            margin-left: 20px;
        }
    }
}
.rs-courses-categories {
	.container{
		overflow: hidden;
		.col-lg-3{
			margin-bottom: 30px;
		}
	}
    .courses-item {
        text-align:center;
        background-color: #f0f0f0;
        padding: 30px 30px 22px;
        //margin-bottom: 30px;
        .courses-title {
            font-size: 20px;
            margin-bottom: 5px;
            margin-top: 18px;
            a {
                color: #212121;
                &:hover {
                    color: @primary-color;
                }
            }
        }
        .courses-amount {
            font-size: 13px;
            font-weight: 600;
            color: #505050;
        }
        i {
            display: inline-block;
            width: 80px;
            height: 80px;
            line-height: 80px;
            text-align: center;
            background-color: @primary-color;
            color: #ffffff;
            border-radius: 50%;
            font-size: 36px;
            transition: 0.5s;
            -webkit-transition: 0.5s;
            -ms-transition: 0.5s;
        }
        &:hover {
            background-color: #d0d0d0;
            i {
                background-color: #212121;
                color: @primary-color;
                transform: rotate(360deg);
            }
        }
    }
}

.rs-learning-objectives {
    .container{
        overflow: hidden;
        .col-lg-3{
            margin-bottom: 30px;
        }
    }
    .courses-item {
        text-align:center;
        background: #f2f2f2;
        border: 1px solid #ddd;
        padding: 65px 15px 15px;
        margin-bottom: 30px;
        transition: 0.4s;
        .courses-title {
            font-size: 20px;
            margin-bottom: 5px;
            margin-top: 18px;
            a {
                color: #212121;
                &:hover {
                    color: @primary-color;
                }
            }
        }
        i {
            color: @primary-color;;
            font-size: 60px;
        }
        &:hover{
            border-color: @primary-color;
        }
    }
}

.sidebar-area {
    .title {
        font-size: 20px;
        color: @title-color;
        font-weight: 700;
        text-transform: uppercase;
        display: block;
        margin-bottom: 25px;
    }
    .search-box {
        margin-bottom: 45px;
        .form-control {
            border-radius: 0;
            box-shadow: none;
            color: #101010;
            padding: 5px 15px;
            height: 45px;
        }
        .box-search {
            position: relative;
        }
        .btn {
            position: absolute;
            right: 3px;
            background: transparent;
            border: none;
            box-shadow: none;
            top: 50%;
            outline: none;
            transform: translateY(-50%);
        }
    }
    .cate-box {
        margin-bottom: 45px;
        ul {
            padding: 0;
            margin: 0;
            li {
                list-style: none;
                border-bottom: 1px solid #ddd;
                padding: 10px 0;
                cursor: pointer;
                a {
                    color: #505050;
                    padding: 0 0 0 5px;
                    span {
                        float: right;
                    }
                }
                &:first-child {
                    padding-top: 0;
                }
                &:hover{
                    i,
                    a{
                        color: @primary-color;
                    }
                }
            }
        }
    }
    .latest-courses {
        margin-bottom: 45px;
        .post-item {
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            overflow: hidden;
            .post-img {
                width: 33.33%;
                transition: @transition;
                float: left;
            }
            .post-desc {
                width: 66.66%;
                padding-left: 20px;
                float: left;
                h4 {
                    margin-bottom: 5px;
                    a {
                        font-size: 16px;
                        color: @title-color;
                        &:hover {
                            color: @primary-color;
                        }             
                    }
                }
                .duration {
                    font-weight: 600;
                    color: #777777;
                }
                .price {
                    margin-left: 8px;
                    font-weight: 600;
                    color: #777777;
                    span {
                        color: @primary-color;
                    }
                }
            }
            + .post-item {
                padding-top: 20px;
                margin-top: 20px;
                border-top: 1px solid #ddd;
            }
            &:hover {
                .post-img {
                    opacity: 0.7;
                }
            }
        }
    }
    .tags-cloud {
        margin-bottom: 39px;
        ul {
            padding: 0;
            margin: 0;

            li {
                float: left;
                list-style: none;
                a {
                    border: 1px solid #d7d7d7;
                    padding: 5px 13px;
                    margin: 0 6px 6px 0;
                    display: block;
                    color: #505050;
                    font-weight: 600;
                    font-size: 13px;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                    line-height: 28px;
                    &:hover {
                        background-color: @primary-color;
                        border-color: @primary-color;
                        color: #ffffff;
                    }
                }
            }
        }
    }
    .newsletter {
        background: #f0f0f0;
        text-align: center;
        padding: 30px 40px;
        h4{
            font-size: 20px;
            color: #212121;
            font-weight: 700;
            text-transform: uppercase;
            display: block;
            margin: 0;
        }
        p {
            font-size: 15px;
            color: #505050;
            margin: 7px 0 20px;
        }
        .box-newsletter {
            position: relative;
            .form-control {
                border-radius: 0;
                box-shadow: none;
                color: #212121;
                padding: 5px 20px;
                height: 50px;
                border-color: @primary-color;
            }
            .btn {
                position: absolute;
                right: 3px;
                background: @primary-color;
                border: none;
                top: 50%;
                transform: translateY(-50%);
                -webkit-transform: translateY(-50%);
                color: #ffffff;
                display: block;
                padding: 11px 17px;
                border-radius: 0;
                text-align: center;
                &:hover {
                    background: @hover-color;
                }
            }
        }
    }
    .course-features-info{
        margin-bottom: 20px;
        background: @title-color;
        padding: 15px;
        color: #fff;
        .desc-title{
            color: @primary-color;
            margin: 10px 0 8px;
        }
        ul{
            li{
                display:block;
                overflow: hidden;
                padding: 10px 0;
                + li{
                    border-top: 1px solid #414141;
                }
                i{
                    color: @primary-color;
                }
                .label{
                    padding-left: 10px;
                }
                .value{
                    float: right;
                    padding-right: 5px;
                }
            }
        }
    }
}

.space-bt30{
	margin-bottom: 30px;
	.event-item{
		margin-bottom: 0 !important;
	}
}
.rs-search-courses {
    padding: 70px 0;
    position: relative;
    margin-top: -100px;
    z-index: 10;
    &:after {
        content: '';
        position: absolute;
        height: 100%;
        width: 90%;
        left: 0;
        right: 0;
        margin: 0 auto;
        top: 0;
        background-color: #212121;
        z-index: -1;
        display: block;
    }
    select, button, input {
        width: 100%;
        height: 60px;
        padding: 0 15px;
        color: #777;
        border: none;
    }
    ::-webkit-input-placeholder { /* Chrome/Opera/Safari */
     color: #000;
     opacity:1 !important;
    }
    ::-moz-placeholder { /* Firefox 19+ */
     color:#777;
     opacity:1 !important;
    }
    :-ms-input-placeholder { /* IE 10+ */
     color: #777;
     opacity:1 !important;
    }

    :-moz-placeholder { /* Firefox 18- */
     color: #777;
     opacity:1 !important
    }
    select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        outline: none;
    }
    .categories, .level {
        position: relative;
        &:before {
            position: absolute;
            font-family: FontAwesome;
            content: "\f0d7";
            right: 15px;
            top: 50%;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
        }        
    }
    .search-btn {
        button {
            background-color: @primary-color;
            border-color: @primary-color;
            color: #fff;
            font-weight: 600;
            cursor: pointer;
            &:hover, &:focus {
                background-color: @hover-color;
            }
        }
    }
}
/* ------------------------------------
    10. Courses Details
---------------------------------------*/
.rs-courses-details {
    .detail-img {
        position: relative;
        margin-bottom: 40px;
        img {
            transition: @transition;
        }
        &:hover {
            img {
                opacity: 0.9;
            }
        }
        .course-seats {
            position: absolute;
            bottom: 25px;
            right: 25px;
            font-size: 15px;
            width: 78px;
            height: 78px;
            line-height: 21px;
            border-radius: 50%;
            text-align: center;
            background-color: @primary-color;
            color: #fff;
            font-weight: 600;
            padding-top: 18px;
            span {
                display: block;
            }
             &.price{
                right: auto;
                left: 25px;
                 padding-top: 0;
                 line-height: 78px;
            }
        }
       
    }
    .course-title {
        font-size: 24px;
        margin-bottom: 30px;
    }
    .course-instructor {
        padding: 20px 25px 16px;
        background-color: #f9f9f9;
        position: relative;
        p {
            margin-bottom: 0;
        }
        .instructor-title {
            font-family: @body-font;
            font-size: 16px;
            margin-bottom: 20px;
        }
    }
    .instructor-inner {
        display: -webkit-flex;
        display: flex;
        .instructor-img {
            img {
                height: 100px;
                width: 100px;
                transition: @transition;
            }
            &:hover {
                img {
                    opacity: 0.8;
                }
            }
        }
        .instructor-body {
            padding-left: 18px;
            .name {
                font-size: 16px;
                font-family: @body-font;
                margin-bottom: 0;
            }
            .designation {
                font-size: 14px;
                font-weight: 600;
            }
            .social-icon {
                margin-top: 15px;
                a {
                    i {
                        font-size: 17px;
                        margin-right: 6px;
                    }
                }
            }
        }
    }
    .short-desc {
        margin-top: 15px;
    }
    .info-list {
        font-weight: 600;
        font-size: 15px;
        ul {
            li {
                color: #777777;
                margin-bottom: 6px;
                span {
                    color: #212121;
                }
            }
        }
    }
    .apply-btn {
        position: absolute;
        right: 16px;
        bottom: 10px;
        a {
            display: inline-block;
            height: 42px;
            line-height: 42px;
            text-align: center;
            min-width: 147px;
            padding: 0 20px;
            background-color: @primary-color;
            color: #fff;
            transition: all 0.3s ease 0s;
            font-weight: 600;
            &:hover {
                background-color: @hover-color;
            }
        }
    }
    .course-desc {
        margin-top: 30px;
        .desc-title {
            font-size: 19px;
            margin-bottom: 12px;
        }
        .desc-text {
            p {
                margin-bottom: 18px;
            }
        }
        .share-area {
            padding: 15px;
            border: 1px solid #ccc;
            h3 {
                margin-bottom: 0;
                font-family: @body-font;
                font-size: 15px;
            }
            .share-inner {
                text-align: right;
                a {
                    display: inline-block;
                    min-width: 88px;
                    height: 30px;
                    line-height: 30px;
                    text-align: center;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    font-size: 13px;
                    color: #505050;
                    padding: 0 12px;
                    font-weight: 600;
                    + a {
                        margin-left: 8px;
                    }
                    &:hover {
                        border-color: @primary-color;
                        color: @primary-color;
                    }
                }
            }
        }
        .course-syllabus{
            padding: 15px 0 35px;
            h3.desc-title{
                padding-bottom: 10px;
            }
            ul.syllabus{
                padding-left: 15px;
                li{
                    display: block;
                    padding-top: 6px;
                    strong{
                        padding-right: 5px;
                        display: block;
                    }
                    span{
                        padding-left: 20px;
                        position: relative;
                        &:before{
                            content: "\f054";
                            font-family: "FontAwesome";
                            font-size: 10px;
                            left: 0;
                            position: absolute;
                            color: #505050;
                            top: 0;
                        }
                    }
                }
            }
            
        }
    }
    .rs-testimonial .testimonial-item {
        text-align: center;
        .testi-desc:after,
        .testi-desc:before{
            display: none;
        }
        .cl-client-rating{
            i{
                color: #ffaa30;
            }
        }
    }
    ul.course-meta-style{
        li{
            float: left;
            padding-right: 30px;
            position: relative;
            &:before{
                content: "";
                position: absolute;
                top: 0;
//                transform: translateY(-50%);
//                -webkit-transform: translateY(-50%);
                right: 15px;
                width: 1px;
                height: 40px;
                background: #e1e1e1;
            }
            &:last-child{
                padding-right: 0;
                &:before{
                    display: none;
                }
            }
            &.author{
                overflow: hidden;
                .author-name,
                .image{
                    float: left;
                }
                .author-name{
                    padding-left: 15px;
                }
            }
            a{
                color: @title-color;
                font-weight: 600;
            }
            i{
                color: #ffaa30;
            }
        }  
    }
    .btn-area{
        text-align: right;
        a{
            background: @primary-color;
            color: #fff;
            display: inline-block;
            text-transform: uppercase;
            padding: 10px 20px;
            &:hover{
                background: @hover-color;
            }
        }
    }
    .course-des-tabs{
        margin-top: 15px;
        .tab-btm{
            .tabs-cyan{
                margin: 0;
                border: 0;                
               .nav-item{
                   width: 25%;
                    a{
                        border: 1px solid #eee;
                        background: #f9f9f9;
                        border-right: 0;
                        color: @title-color;
                        font-weight: 600;
                        &.active{
                            position: relative;
                            background: #fff;
                            border-bottom: none;
                            color: @primary-color;
                            &:before{
                                content: '';
                                position: absolute;
                                left: 0;
                                right: 3px;
                                top: 0;
                                height: 2px;
                                z-index: 10;
                                width: 100%;
                                background: @primary-color;
                            }
                        }
                    }
                   &:last-child{
                       a{
                           border-right: 1px solid #eee;
                       }
                   }
                } 
            }
        }
        .tab-content{
            border-radius: 0;
            border: 1px solid #eee;
            border-top: 0;
            padding: 60px 30px;
            .tab-pane{
                h4.desc-title{
                    padding-bottom: 15px;
                    position: relative;
                    &:before{
                        content: "";
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        height: 3px;
                        width: 50px;
                        background: @primary-color;
                    }
                }
                ul.requirements-list{
                    li{
                        position: relative;
                        padding-left: 20px;
                        &:before{
                            border: none;
                            color: @primary-color;
                            content: "\f101";
                            font-family: fontawesome;
                            font-size: 14px !important;
                            font-size: 9px;
                            margin-right: 10px;
                            padding: 0;
                            left: 0;
                            position: absolute;
                            top: 1px;
                        }
                    }   
                }
            }
            .instructor-list{
                overflow: hidden;
                .image{
                    float: left;
                    padding-right: 20px;
                }
                .author-name{
                    overflow: hidden;
                    h4{
                        margin: 0 0 5px;
                    }
                    span{
                        margin: 0 0 10px;
                        display: block;
                    }
                    i{
                        color: #ffaa30;
                    }
                }
                p.dsc{
                    display: block;
                    clear: both;
                    padding-top: 15px;
                    margin-bottom: 0;
                }
                .social-icon{
                    li{
                        display: inline-block;
                        a{
                            display: block;
                            background: #ff3115;
                            padding: 3px 0;
                            width: 40px;
                            text-align: center;
                            i{
                                color: #fff;
                            }
                            &:hover{
                                background: @primary-color;
                            }
                        }
                    }
                }
            }
        }
    }
}

/* ------------------------------------
    11. Rs-counter
---------------------------------------*/
.rs-counter-style7{
    .rs-counter-list{
         display: flex;
        text-align: left;
       .icon-part{
        margin-top: 30px;
        margin-right: 25px;
            i{
                  &:before{
                      font-size: 60px;
                      color: @primary-color;
                  }
            }
        }
        .text-part{
            .counter-number{
                font-size: 60px;
                color: #ffffff;
                margin-bottom: 6px;
            }
            .counter-desc{
                color: #ffffff;
                font-weight: 500;
                margin-bottom: 0;
                font-size: 20px;
                text-transform: uppercase;
            }
        }          
    }
}
.rs-counter-style8{
    .rs-counter-list{
        display: flex;
        text-align: left;
       .icon-part{
        margin-top: 30px;
        margin-right: 20px;
            i{
                  &:before{
                      font-size: 60px;
                      color: @primary-color;
                  }
            }
        }
        .text-part{
            .counter-number{;
                font-size: 45px;
                color: @title-color;
                margin: 0;
                padding-bottom: 10px;
               
            }
            .counter-desc{
                font-size: 20px;
                color: @body-color;
               
            }
        }          
    }
}



/* ------------------------------------
    11. OUR BEST
---------------------------------------*/
.rs-our-best{
    .rs-calltoaction{
        .sub-title{
            display: block;
            font-weight: 600;
            font-size: 18px;
            text-transform: uppercase;
            margin-bottom: 10px;
            color: @primary-color;
        }
        .title{
            font-size: 30px;
            font-weight: 700;
            line-height: 40px;
            color: @title-color;
            margin: 0;
        }
        .desc{
            font-size: 15px;
            line-height: 27px;
            color: @body-color;
            margin: 0;
        }
    }
}


/* ------------------------------------
    11. Rs-counter
---------------------------------------*/
.rs-counter{
    .rs-counter-list {
        border: 1px solid rgba(255,255,255,0.2);
        margin-bottom: 30px;
        text-align: center;
        padding: 23px;
        transition: .3s all ease;
        .counter-number {
            font-size: 60px;
            color: #ffffff;
            margin-bottom: 6px;
        }
        .counter-desc {
            color: #ffffff;
            font-weight: 500;
            margin-bottom: 0;
            font-size: 20px;
            text-transform: uppercase;
        }
        &:hover {
            border-color: @primary-color;
        }
    }
    .counter-title {
        color: #fff;
        font-size: 30px;
        line-height: 28px;
        margin-bottom: 25px;
    }
    .counter-text p {
        margin-bottom: 35px;
        color: rgba(255,255,255,.6);
    }
    .counter-img {
        box-shadow: 0 10px 30px rgba(255,255,255,.1);
        img {
            border-radius: 8px;
        }
    }
}
.rs-counter-style2{
    .rs-counter-list{
        border: none;
        padding: 0;
    }
    .count-icon {
        i{
            font-size: 60px;
            color: @primary-color;
        }
    }
}
.home5{
    .rs-counter{
        .rs-counter-list{
            border-radius: 30px;
            border: none;
            background: #92278f; 
            .counter-number{
                color: #fff;
            }
            &:hover{
                border-color: #92278f;
            }
            &.blue-color{
               background: #269aa9; 
            }
            &.orange-color{
                background: #fc7f0c;
            }
            &.red-color{
                background: #e84b3a;
            }
        }
    }
}
/* ------------------------------------
    12. Upcoming Events
---------------------------------------*/
.rs-events {
    .event-item {
        .event-img {
            margin-bottom: 20px;
        }
        .event-date {
            font-size: 14px;
            font-weight: 500;
            i {
                font-size: 13px;
                display: inline-block;
                margin-right: 5px;
                color: @primary-color;
            }
            span {
                color: #505050;
            }
        }
        .event-title {
            font-size: 20px;
            margin-bottom: 8px;
            margin-top: 4px;
            a {
                color: #212121;
                &:hover{
                    color: @primary-color;
                }
            }
        }
        .event-meta {
            font-size: 14px;
            > div {
                display: inline-block;
                i {
                    font-size: 14px;
                    display: inline-block;
                    margin-right: 3px;
                    color: @primary-color;
                }
            }
            .event-time {
                margin-right: 10px;
            }
        }
        .event-btn {
            margin-top: 18px;
            a {
                font-weight: 500;                
            }
        }
    }
}

.rs-events-2 {
    .event-item {
        margin-bottom: 30px;
        background: #f9f9f9;
        border: none;
        .row {
            margin: 0;
        }
        [class*="col-"] {
            padding: 0;
        }
        .event-content {
            padding: 15px 20px 20px 0;
        }
        .event-img {
            margin: -1px 0 -1px -1px;
            margin-right: 20px;
            transition: @transition;
            &:hover {
                opacity: 0.8;
            }
        }
        .event-location {
            font-size: 13px;
            i {
                font-size: 14px;
                display: inline-block;
                margin-right: 5px;
                color: @primary-color;
            }
            span {
                color: #505050;
            }
        }
        .event-title {
            font-size: 18px;
            margin-bottom: 4px;
            margin-top: 1px;
            line-height: 25px;
            font-weight: 600;
            text-transform: capitalize;
            a {
                color: #212121;
            }
        }
        .event-meta {
            font-size: 13px;
            > div {
                display: inline-block;
                i {
                    font-size: 15px;
                    display: inline-block;
                    margin-right: 3px;
                    color: @primary-color;
                }
            }
            .event-time {
                margin-left: 10px;
            }
        }
        .event-desc {
            margin-top: 7px;
            p {
                margin-bottom: 15px;                
            }
        }
        .event-btn {
            a {
                display: inline-block;
                height: 40px;
                line-height: 40px;
                text-align: center;
                min-width: 130px;
                padding: 0 15px;
                background: @primary-color;
                color: #fff;
                transition: all 0.3s ease 0s;
                font-weight: 600;
                &:hover {
                    background-color: @hover-color;
                    color: #fff;
                }                
            }
        }
    }
}
.home5{
    .event-item{
        .event-date{
            max-width: 170px;
            margin: 0 auto;
            height: 30px;
            background: #92278f;
            margin-top: -32px;
            position: relative;
            top: -13px;
            border-radius: 15px 15px 0 0;
            padding-top: 2px;
            text-align: center;
            i,
            span{
                color: #fff;
            }
        }
    }
    .red-color{
        .event-date{
            background: #e84b3a;
        }
    }
    .orange-color{
        .event-date{
            background: #fc7f0c;
        }
    }
    .blue-color{
        .event-date{
            background: #00bcd4;
        }
    }
}
.rs-event-details {
    #googleMap {
        height: 270px;
        margin-bottom: 30px;
    }
    .event-title {
        font-size: 24px;
        margin-bottom: 6px;
        a {
            color: #212121;
            &:hover {
                color: @primary-color;
            }
        }
    }
    .event-meta {
        margin-top: 10px;
        margin-bottom: 30px;
        > div {
            display: inline-block;
            color: #666;
            + div {
                margin-left: 14px;
            }
            i {
                margin-right: 3px;
                color: @primary-color;
            }
        }
    }
    .event-img {
        margin-bottom: 25px;
    }
    .share-area {
        .share-inner {
            text-align: right;
            span {
                display: inline-block;
                margin-right: 5px;
                color: #101010;
                font-weight: 600;
            }
            a {
                i {
                    border: 1px solid #ddd;
                    width: 30px;
                    height: 30px;
                    line-height: 30px;
                    text-align: center;
                    color: #212121;
                    transition: @transition;
                    &:hover {
                        border-color: @primary-color;
                        color: @primary-color;
                    }
                }
            }
        }
        .book-btn {
            a {
                display: inline-block;
                height: 40px;
                line-height: 36px;
                text-align: center;
                min-width: 147px;
                padding: 0 20px;
                border: 2px solid @primary-color;
                color: #212121;
                transition: @transition;
                font-weight: 600;
                &:hover {
                    background-color: @primary-color;
                    color: #ffffff;
                }
            }
        }
    }
}


/* ------------------------------------
    14. Countdown
---------------------------------------*/
.rs-latest-news-style7{
     .title{
            font-size: 30px;
            font-weight: 700;
            line-height: 40px;
            color: @title-color;
            margin: 0;
        }
    .rs-latest-list{
        padding: 50px;
        background: #fff;
        -webkit-box-shadow: 0 10px 40px rgba(0,0,0,.1);
        box-shadow: 0 10px 40px rgba(0,0,0,.1);
        border-radius: 10px;
       
        .latest-wrap{
            .news-list-block{
                .news-list-item{
                    display: flex;
                    align-items: center;
                    .news-img{
                        flex: 0 0 50%;
                        max-width: 50%;
                        padding-right: 30px;
                        img{
                            border-radius: 6px;
                        }
                    }
                    .news-content{
                        .news-date{
                            span{
                                font-size: 15px;
                                color: @body-color;
                                font-weight: 500;
                                line-height: 26px;
                                display: block;
                                padding-bottom: 8px;
                            }
                        }
                        .news-title{
                            font-size: 20px;
                            margin-bottom:0px;
                            font-weight: 600;
                            padding-bottom: 18px;
                            a{
                                color: @title-color;
                                &:hover{
                                    color: @primary-color;
                                }
                            }
                        }
                        .news-btn{

                        }
                    }
                }
            }
        }
        .event-item-new{
            display: flex;
            .event-date{
                margin-right: 30px;
                min-width: 125px;
                border-radius: 10px;
                height: 110px;
                background: #f2f2f2;
                text-align: center;
                display: table;
                .vertical-align{
                    display: table-cell;
                    vertical-align: middle;
                    .day{
                        font-size: 50px;
                        font-weight: 600;
                        color: #ff3115;
                        margin: 0 0 6px;
                        line-height: 40px;
                        display: block;
                    }
                    .month{
                        font-size: 20px;
                        color: #505050;
                        font-weight: 500;
                        text-transform: uppercase;
                        display: block;
                    }
                }
            }
            .event-des{
                .title{
                    font-weight: 600;
                    font-size: 20px;
                    margin: 0 0 15px;
                    a{
                        color: @title-color;
                        &:hover{
                            color: @primary-color;
                        }
                    }
                }
                p{
                    font-size: 15px;
                    color: @body-color;
                    line-height: 27px;
                    margin: 0;
                }
            }
        }
    }
}

/* ------------------------------------
    14. PUBLICATION
---------------------------------------*/
.rs-our-publication{
     .owl-stage-outer{
        padding-bottom: 30px;
    }
    .inner-shadow{
        padding: 0 2px;
        .product-item{
            box-shadow: 0 0 3px #ccc;
            text-align: center;
            transition: all .3s ease 0s;
            background-color: #fff;
            padding-bottom: 40px;
            .img-box{
                overflow: hidden;
                margin-bottom: 32px;
                img{
                    transform: scale(1);
                    transition: @transition;                    
                }
            }
            .content-part{
            
                .product-title{
                    font-size: 20px;
                    margin-bottom: 3px;
                    a{
                        color: @title-color;
                        &:hover{
                            color: @primary-color;
                        }
                    }
                }
                .product-price{
                    margin-bottom: 20px;
                    font-size: 16px;
                    color: #444;
                    display: block;
                }
                .product-btn{
                    a{
                        background: @primary-color;
                        color: @white-color;
                        padding: 10px 25px;
                        border-radius: 30px;
                        text-transform: uppercase;
                        &:hover{
                            color: @white-color;
                            background: @hover-color;
                        }
                    }
                }
            }
        }
        &:hover{
            .product-item{
                box-shadow: 0 10px 40px rgba(0,0,0,.1);
                .img-box{
                    img{
                        transform: scale(1.1);
                    }
                }
            }
        }
    }
}


/* ------------------------------------
    14. Countdown
---------------------------------------*/
.rs-countdown-part{
    .countdown-part{
        .sub-title{
            display: block;
            font-size: 18px;
            line-height: 34px;
            color: @primary-color;
            font-weight: 600;
        }
        .title{
            font-size: 60px;
            line-height: 70px;
            font-weight: 600;
            color: @white-color;
        }
        .description{
            font-size: 15px;
            line-height: 27px;
            font-weight: 400;
            color: @white-color;
        }
        .counter-wrap {
            max-width: 550px;
            .timecounter-inner {
                .time_circles {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    canvas{
                        opacity: 1;
                    }
                    div{
                        position: absolute;
                        text-align: left;
                        padding-right: 0;
                        span {
                            padding: 0;
                            display: block;
                            width: 100%;
                            text-align: center;
                            font-weight: 700;
                            font-size: 40px !important;
                            margin: 0 0 9px 0;
                            color: @primary-color;
                        }
                        h4{
                            color: @white-color;
                            margin: 0;
                            padding: 0;
                            text-align: center;
                            font-weight: 400;
                            text-transform: capitalize;
                            line-height: 17px;
                            font-size: 12px !important;
                        }
                    }
                }
            }
        }
    }
    .register-form{
        background: #fff;
        .form-title{
            position: relative;
            padding: 30px 65px 36px;
            margin-bottom: 50px;
            .title{
                font-weight: 400;
                color: #fff;
                position: relative;
                z-index: 10;
                line-height: 1.4;
                font-size: 22px;
            }
           &:after{
               position: absolute;
               height: 100%;
               width: 100%;
               clip-path: polygon(-115.5% 0,113% 0,76% 100%);
               left: 0;
               background-color: @primary-color;
               top: 0;
               content: "";
           }
        }
        .form-group{
            padding: 0 50px 5px;
            .from-control{
                width: 100%;
                margin-bottom: 20px;
                border-radius: 3px;
                height: 50px;
                line-height: 50px;
                padding-top: 0;
                padding-bottom: 0;
                font-size: 15px;
                padding: 10px 14px;
                border: 1px solid rgba(54,54,54,.1);
                outline: 0;
            }
            input[type=submit]{
                width: 100%;
                margin-bottom: 50px;
                border-radius: 3px;
                height: 50px;
                line-height: 50px;
                font-size: 15px;
                -webkit-appearance: button;
                cursor: pointer;
                background: @primary-color;
                border: 0;
                color: #fff;
                font-size: 15px;
                text-transform: uppercase;
                outline: 0;
                &:hover{
                    color: @white-color;
                    background: @hover-color;
                }
            }
            .select-option {
                position: relative;
                select {
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    appearance: none;
                    cursor: pointer;
                    color: #b1b1b1;
                    opacity: 1;
                    z-index: 11; 
                    position: relative;                   
                }
                &:after {
                    content: '\f107';
                    font-family: FontAwesome;
                    color: #b1b1b1;
                    right: 15px;
                    position: absolute;
                    top: 11px;
                    z-index: 12;
                    font-size: 22px;
                    pointer-events: none;
                }
            }
        }
    }
}



/* ------------------------------------
    13. Team Member
---------------------------------------*/
.rs-team-style7{
     .owl-stage-outer{
        padding-bottom: 30px;
    }
    .item{
        .item-team{

            .team-img{
                overflow: hidden;
                border-radius: 10px 10px 0 0;
                position: relative;
                &:after{
                    content: '';
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,.6);
                    transition: .3s all ease;
                    position: absolute;
                    opacity: 0;

                }
                .social-icon{
                   width: 240px;
                   height: 55px;
                   background: #ff3115;
                   line-height: 58px;
                   text-align: center;
                   border-radius: 5px 5px 0 0;
                   bottom: -50px;
                    position: absolute;
                    margin: 0 auto;
                    visibility: hidden;
                    transition: 0.3s;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 10;
                    a{
                        background: #ff3115;
                        line-height: 58px;
                        text-align: center;
                        border-radius: 5px 5px 0 0;
                        margin: 0 15px;
                       i{
                            color: @white-color;
                            margin: 4px;
                           &:hover{
                                 color: @title-color;
                           } 
                       }
                      
                    }

                }
            }
            .team-content{
                border-radius: 0 0 10px 10px;
                padding: 15px;
                background: #fff;
                box-shadow: 0 3px 7px rgba(0,0,0,.06);
                text-align: center;
                .team-name{
                    margin-bottom: 0;
                    font-size: 18px;
                    line-height: 27px;
                    font-weight: 600;
                    a{
                        color: @title-color;
                        &:hover{
                            color: @primary-color;
                        }
                    }
                }
                .sub-title{
                    font-size: 14px;
                    line-height: 27px;
                    font-weight: 400;
                    color: @body-color;
                    display: block;
                }
            }
            &:hover{
                .team-img{
                   &:after{
                     opacity: 1;
                   }
                    .social-icon{
                        bottom: 0;
                        visibility: visible;
                    }
                }
            }
        }
    }
}

/* ------------------------------------
    13. Team Member
---------------------------------------*/
.rs-team-style8{
     .owl-stage-outer{
        padding-bottom: 10px;
    }
    .item{
        .item-team{

            .team-img{
                overflow: hidden;
                border-radius: 10px 10px 0 0;
                position: relative;
                &:after{
                    content: '';
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,.6);
                    transition: .3s all ease;
                    position: absolute;
                    opacity: 0;

                }
                .social-icon{
                   width: 240px;
                   height: 55px;
                   line-height: 58px;
                   text-align: center;
                   border-radius: 5px 5px 0 0;
                   bottom: -50px;
                    position: absolute;
                    margin: 0 auto;
                    visibility: hidden;
                    transition: 0.3s;
                    left: 50%;
                    transform: translateX(-50%);
                    z-index: 10;
                    a{
                        margin: 4px;
                        color: #fff;
                       i{
                            color: @white-color;
                            margin: 4px;
                           &:hover{
                                 color: @title-color;
                           } 
                       }
                      
                    }

                }
            }
            .team-content{
                border-radius: 0 0 10px 10px;
                padding: 15px;
                background: #fff;
                box-shadow: 0 3px 7px rgba(0,0,0,.06);
                text-align: center;
                .team-name{
                    margin-bottom: 0;
                    font-size: 18px;
                    line-height: 27px;
                    font-weight: 600;
                    a{
                        color: @title-color;
                        &:hover{
                            color: @primary-color;
                        }
                    }
                }
                .sub-title{
                    font-size: 14px;
                    line-height: 27px;
                    font-weight: 600;
                    color: @body-color;
                    display: block;
                }
            }
            &:hover{
                .team-img{
                   &:after{
                     opacity: 1;
                   }
                    .social-icon{
                        bottom: 0;
                        visibility: visible;
                    }
                }
            }
        }
    }
}



/* ------------------------------------
    13. Experienced Staffs
---------------------------------------*/
.rs-team {
    .team-item {
        position: relative;
        overflow: hidden;
        .team-img {
            position: relative;
            .normal-text {
                position: absolute;
                bottom: 0;
                padding: 10px 20px;
				min-width: 175px;
                background-color: rgba(34, 34, 34, 0.8);
                transition: .3s ease all;
                .team-name {
                    color: #fff;
                    font-size: 18px;
                    font-weight: 700;
                    margin: 0;
                    display: block;
                }
                .subtitle {
                    color: #fff;
                    font-size: 14px;
                    font-weight: 400;
                    margin: 0;
                    display: block;
                }
            }
            img {
                width: 100%;
            }
        }
        .team-content {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 10%;
            left: 0;
            text-align: center;
            z-index: 11;
            padding: 30px;
            opacity: 0;
            -webkit-transition: 0.3s all ease-out;
            transition: 0.3s all ease-out;
            &:before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                width: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: -1;
                -webkit-transition: .25s opacity ease;
                transition: .25s opacity ease;
            }
        }
        .team-name {
            margin-bottom: 2px;
            a {
                margin-bottom: 6px;
                font-size: 20px;
                color: #fff;
                text-transform: capitalize;
                font-weight: 700;
                position: relative;
                z-index: 111;
                &:hover {
                    color: #fff;
                }
            }
        }
        .team-title {
            position: relative;
            z-index: 111;
            font-size: 14px;
            color: #fff;
            padding-bottom: 12px;
            &:after {
                position: absolute;
                left: 50%;
                bottom: 0;
                width: 50px;
                height: 2px;
                background: @primary-color;
                content: "";
                -webkit-transform: translateX(-50%);
                transform: translateX(-50%);
            }
        }
        .team-desc {
            color: #fff;
            margin-bottom: 0;
            padding-top: 12px;
            margin-top: 15px;
        }
        .team-social {
            position: relative;
            width: 100%;
            text-align: center;
            z-index: 111;
            opacity: 0;
            margin-top: 25px;
            -webkit-transition: .4s all ease;
            transition: .4s all ease;
            .social-icon {
                display: inline-block;
                width: 40px;
                height: 40px;
                line-height: 40px;
                text-align: center;
                background-color: @primary-color;
                margin-right: 6px;
                border-radius: 50%;
                i {
                    font-size: 14px;
                    color: #fff;
                }
                &:hover {
                    background-color: @hover-color;
                }
            }
        }
        &:hover {
            .team-content {
                opacity: 1;
                top: 0;
            }
            .team-social {
                opacity: 1;
            }
            .normal-text {
                opacity: 0;
                visibility: hidden;
            }
        }
    }
}
.rs-team-2 {
    .team-item {
        margin-bottom: 30px;
        .team-img {
            position: relative;
            overflow: hidden;
            .social-icon {
                position: absolute;
                top: 50%;
                width: 100%;
                text-align: center;
                transform: translateY(-50%);
                opacity: 0;
                visibility: hidden;
                transition: .8s all ease;
                a {
                    i {
                        margin-right: 8px;
                        font-size: 20px;
                    }
                }
            }
            &:before {
                content: '';
                position: absolute;
                width: calc(~"100% - 20px");
                height: calc(~"100% - 20px");
                background: transparent;
                border: 5px solid rgba(255, 255, 255, 0.7);
                left: 50%;
                top: 50%;
                transform: translateX(-50%) translateY(-50%);
                transition: .5s all ease;
            }
            &:hover {
                .social-icon {
                    opacity: 1;
                    visibility: visible;
                    a:hover {
                        color: #fff;
                    }
                }
                &:before {
                    transform: translateX(-50%) translateY(-50%) rotate(-270deg);
                    border: 0;
                    background: rgba(33, 33, 33, 0.8);
                }
            }
        }
        .team-body {
            padding: 18px;
            text-align: center;
            background-color: #f0f0f0;
            .name {
                font-size: 20px;
                margin-bottom: 2px;
                color: #212121;
                transition: @transition;
                &:hover{
                    color: @primary-color;
                }
            }
            .designation {
                font-size: 15px;
            }
        }
    }
    &.team-page{
        padding-top: 93px;
    }
    .row.grid{
    	min-height: 400px !important;
    	overflow: hidden;
    }
}
.team-all{
	.row{
		margin-bottom: 30px;
		.team-item{
			margin-bottom: 0;
		}
	}
}

.home5{
    .rs-team{
        .team-item{
            margin-bottom: 30px;
            overflow: inherit;
            .team-desc{
                font-size: 14px;
                line-height: 22px;
            }
             .team-social .social-icon{
                background: #92278f;
                &:hover{
                    background: #5d0b5b;
                }
            }
            
            .team-img .normal-text{
                -webkit-transform: translateX(-50%);
                -ms-transform: translateX(-50%);
                transform: translateX(-50%);
                left: 50%;
                text-align: center;
                border-radius: 30px;
                width: 200px;
                padding: 15px 0 0;
                height: 70px;
                bottom: -30px;
            }
        }
    }
}
.pagination {
    margin-top: 25px;
    .page-item {
        > * {
            width: 50px;
            height: 50px;
            line-height: 50px;
            text-align: center;
            font-size: 18px;
            padding: 0;
            font-weight: 600;
            color: #505050;
            outline: none;
            &:hover, &.active {
                color: @primary-color;
            }
            &.dotted {
                line-height: 40px;
            }
        }
    }
}

/* ------------------------------------
    14. Staffs Single
---------------------------------------*/
.rs-team-single {
    .team-name {
        font-size: 30px;
        margin-bottom: 10px;
    }
    .team-title {
        font-size: 15px;
        line-height: 24px;
        margin-bottom: 5px;
        span {
            display: block;
        }
    }
    .team-contact {
        margin-bottom: 15px;
    }
    .team-icons {
        padding: 30px;
        text-align: center;
        a {
            display: inline-block;
            height: 30px;
            width: 30px;
            line-height: 30px;
            text-align: center;
            background-color: #f0f0f0;
            margin-right: 15px;
            border-radius: 2px;
            transition: @transition;
            &:hover {
                background-color: @primary-color;
                color: #fff;
            }
        }
    }
    .rs-progress {
        height: 8px;
        border: 1px solid @primary-color;
        border-radius: 0;
        overflow: visible;
        padding: 1px;
        background: #fff;
        margin-top: 25px;
        + .rs-progress {
            margin-top: 50px;
        }
        .progress-bar {
            position: relative;
            text-align: left;
            line-height: 4px;
            border-radius: 0;
            box-shadow: none;
            background-color: @primary-color;
            .pb-label {
                position: absolute;
                left: 0px;
                top: -24px;
                color: #666;
                font-size: 15px;
                font-weight: 600;
            }
            .pb-percent {
                position: absolute;
                right: -13px;
                font-weight: 500;
                color: #fff;
                font-size: 10px;
                top: -30px;
                background: @primary-color;
                padding: 8px 5px;
                &:after {
                    content: '';
                    position: absolute;
                    border: 5px solid transparent;
                    left: 50%;
                    border-top-color: @primary-color;
                    top: 20px;
                    -webkit-transform: translateX(-50%);
                    transform: translateX(-50%);
                }
            }
        }
    }    
}
.rs-team-single2{

}
/* ------------------------------------
    15. Calltoaction
---------------------------------------*/
.rs-calltoaction {
    .cta-content {
        .cta-subtitle {
            margin-bottom: 10px;
            color: @primary-color;
        }
        .cta-title {
            margin-bottom: 0;
            color: #ffffff;
            font-size: 32px;
        }
        p{
            color: #ffffff;
        }
    }
    .cta-button {
        display: inline-block;
        text-decoration: none;
        background-color: @primary-color;
        color: #ffffff;
        font-weight: 500;
        min-width: 170px;
        line-height: 45px;
        height: 45px;
        text-align: center;
        text-transform: uppercase;
        &:hover, &:focus, &:active {
            background-color: @hover-color;
            color: rgba(255,255,255,0.7);
            box-shadow: 0 10px 20px rgba(255, 255, 255, 0.04);
        }
    }
}

.rs-webdevelopment-course {
    .cta-subtitle {
        margin-bottom: 10px;
        color: @primary-color;
    }
    .cta-title {
        margin-bottom: 12px;
        color: #ffffff;
        font-size: 32px;
    }
    .cta-button {
        display: inline-block;
        text-decoration: none;
        background-color: @primary-color;
        color: #ffffff;
        font-weight: 500;
        min-width: 170px;
        line-height: 45px;
        height: 45px;
        text-align: center;
        text-transform: uppercase;
        &:hover, 
        &:focus, 
        &:active {
            background-color: @hover-color;
            color: rgba(255,255,255,0.7);
            box-shadow: 0 10px 20px rgba(255, 255, 255, 0.04);
        }
    }
}
/* ------------------------------------
    16. Latest News
---------------------------------------*/
.rs-latest-news {
    .news-normal-block {
        padding: 15px;
        border: 1px solid rgba(34, 34, 34, 0.1);
        transition: @transition;
        &:hover {
            -webkit-box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        .news-img {
            margin-bottom: 20px;
            img {
                width: 100%;
            }
        }
        .news-date {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            i {
                font-size: 15px;
                font-weight: 500;
                display: inline-block;
                margin-right: 4px;
                color: @primary-color;
            }
            span {
                color: #505050;
            }
        }
        .news-title {
            font-size: 20px;
            margin-bottom: 12px;
            a {
                color: #212121;
                &:hover, &:focus {
                    color: @primary-color;
                }
            }
        }
        .news-desc {
            p {
                margin-bottom: 20px;
            }
        }
        .news-btn {
            a {
                display: inline-block;
                margin-left: auto;
                padding: 4px 16px;
                font-size: 13px;
                font-weight: 500;
                text-decoration: none;
                background-color: @primary-color;
                color: #fff;
                &:hover {
                    background-color: @hover-color;
                }           
            }
        }
    }
    .news-list-block {
    	overflow: hidden;
        .news-list-item {
        	overflow: hidden;
            padding: 15px;
            border: 1px solid rgba(34, 34, 34, 0.1);
            transition: @transition;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-wrap: wrap;
            flex-wrap: wrap;
            &:hover {
                -webkit-box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            }
            + .news-list-item {
                margin-top: 23px;
            }
            .news-img {
                -ms-flex: 0 0 33.333333%;
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
                padding-right: 15px;
                float: left;
            }
            .news-content {
                -ms-flex: 0 0 66.666667%;
                flex: 0 0 66.666667%;
                max-width: 66.666667%;
                float: left;
            }
            .news-date {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 10px;
                i {
                    font-size: 15px;
                    font-weight: 500;
                    display: inline-block;
                    margin-right: 4px;
                    color: @primary-color;
                }
                span {
                    color: #505050;
                }
            }
            .news-title {
                font-size: 16px;
                margin-bottom: 7px;
                a {
                    color: #212121;
                    &:hover, &:focus {
                        color: @primary-color;
                    }
                }
            }
            .news-btn {
                a {
                    display: inline-block;
                    margin-left: auto;
                    padding: 8px 20px;
                    font-size: 15px;
                    font-weight: 500;
                    text-decoration: none;
                    background-color: @primary-color;
                    color: #fff;
                    &:hover {
                        background-color: @hover-color;
                    }           
                }
            }
            .news-desc {
                p {
                    margin-bottom: 0;
                }
            }
        }
    }
}
.latest-news-slider {
    .slick-arrow {
        position: absolute;
        z-index: 11;
        bottom: 0;
        right: -104px;
        height: 42px;
        width: 42px;
        font-size: 0;
        border: 0;
        background-color: rgba(0,0,0,0.1);
        color: @primary-color;
        cursor: pointer;
        outline: 0;
        border-radius: 50%;
        transition: @transition;
        line-height: 0;
        &:after {
            font-family: FontAwesome;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translateX(-50%) translateY(-50%);
            font-size: 22px;
        }
        &.slick-prev {
            &:after {
                content: "\f104";                
            }
        }
        &.slick-next {
            right: -160px;
            &:after {
                content: "\f105";
            }
        }
        &:hover {
            background-color: @primary-color;
            color: #fff;
        }
    }
}
.latest-news-nav {
    .slick-track {
        width: 100% !important;
    }
    .slick-slide {
        width: 100% !important;
        margin-bottom: 12px;
        cursor: pointer;
        background-color: #fff;
        border: 1px solid rgba(34, 34, 34, 0.1);
        padding: 8px;
        transition: @transition;
        
        &.slick-current, &:hover {
            background-color: @primary-color;
            border-color: @primary-color;
            img {
                opacity: 0.8;
            }
        }
    }
}
/* ------------------------------------
    17. Our Publications
---------------------------------------*/
.rs-products {
    .owl-stage-outer {
        padding-bottom: 30px;
    }
    .product-item {
        text-align: center;
        transition: @transition;
        background-color: #ffffff;
        &:hover {
            -webkit-box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        .product-img {
            margin-bottom: 17px;
            overflow: hidden;
            img {
                width: 100%;
                transition: @transition;
            }
            &:hover {
                img {
                    -webkit-transform: scale(1.1);
                    transform: scale(1.1);
                }
            }
        }
        .product-title {
            font-size: 20px;
            margin-bottom: 3px;
            a {
                color: #212121;
                &:hover, &:focus, &:active {
                    color: @primary-color;
                }
            }
        }
        .product-price {
            font-size: 16px;
            color: #444;
            display: block;
        }
        .product-btn {
            margin-top: 20px;
            padding-bottom: 25px;
            a {
                display: inline-block;
                margin-left: auto;
                padding: 2px 15px;
                font-size: 15px;
                font-weight: 500;
                text-decoration: none;
                border: 1px solid #212121;
                color: #212121;
                &:hover {
                    border-color: @primary-color;
                    background-color: @primary-color;
                    color: #fff;
                }           
            }
        }
    }
    .view-btn {
        text-align: center;
        margin-top: 20px;
        a {
            display: inline-block;
            margin-left: auto;
            min-width: 170px;
            padding: 8px 20px;
            font-size: 15px;
            font-weight: 500;
            text-decoration: none;
            background-color: @primary-color;
            color: #fff;
            &:hover {
                background-color: @hover-color;
            }           
        }
    }
}
/* ------------------------------------
    18. Testimonial
---------------------------------------*/
.rs-testimonial {
    .owl-stage-outer {
        padding-top: 50px;
        padding-bottom: 55px;
    }
    .testimonial-item {
        padding: 35px;
        background-color: #ffffff;
        .testi-img {
            position: absolute;
            top: -50px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 110px;
            height: 110px;
            padding: 5px;
            background: #fff;
            border-radius: 50%;
            img {
                width: 100px;
                height: 100px;
                border-radius: 50%;                
            }
        }
        .testi-desc {
            padding: 42px 0;
            position: relative;
            .testi-name {
                font-size: 20px;
                margin-bottom: 10px;
            }
            p {
                font-size: 16px;
                line-height: 26px;
                margin-bottom: 0;
            }
            &:before, &:after {
                position: absolute;
                font-family: FontAwesome;
                font-size: 26px;
                color: @primary-color;
            }
            &:before {
                content: "\f10d";
                top: 0;
                left: 0;
            }
            &:after {
                content: "\f10e";
                bottom: 0;
                right: 0;
            }
        }
    }
    .owl-dots {
        position: absolute;
        left: 50%;
        bottom: -8px;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        .owl-dot {
            height: 6px;
            width: 22px;
            background-color: #fff;
            display: inline-block;
            transition: @transition;
            + .owl-dot {
                margin-left: 8px;
            } 
            &.active {
                background-color: @primary-color;
            }
            &:first-child {
                border-radius: 3px 0 0 3px;
            }
            &:last-child {
                border-radius: 0 3px 3px 0;
            }
        }
    }
}
.rs-testimonial-2 {
    .owl-stage-outer {
        padding-top: 55px;
        padding-bottom: 30px;
    }
    .testimonial-item {
        padding: 35px;
        background-color: #f0f0f0;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        margin: 0 3px;
        .testi-img {
            position: absolute;
            top: -50px;
            left: 0;
            right: 0;
            margin: 0 auto;
            width: 110px;
            height: 110px;
            padding: 5px;
            background: #fff;
            box-shadow: 0 3px 14px rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            img {
                width: 100px;
                height: 100px;
                border-radius: 50%;                
            }
        }
        .testi-desc {
            padding: 42px 0;
            position: relative;
            .testi-name {
                font-size: 20px;
                margin-bottom: 15px;
            }
            p {
                font-size: 16px;
                line-height: 26px;
                margin-bottom: 0;
            }
            &:before, &:after {
                position: absolute;
                font-family: FontAwesome;
                font-size: 26px;
                color: @primary-color;
            }
            &:before {
                content: "\f10d";
                top: 0;
                left: 0;
            }
            &:after {
                content: "\f10e";
                bottom: 0;
                right: 0;
            }
        }
    }
    .owl-dots {
        position: absolute;
        left: 50%;
        bottom: 0;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        .owl-dot {
            height: 6px;
            width: 22px;
            background-color: #fff;
            display: inline-block;
            transition: @transition;
            + .owl-dot {
                margin-left: 8px;
            } 
            &.active {
                background-color: @primary-color;
            }
            &:first-child {
                border-radius: 3px 0 0 3px;
            }
            &:last-child {
                border-radius: 0 3px 3px 0;
            }
        }
    }
}
.rs-testimonial-3 {
    .testimonial-style3{
        padding-top: 70px;
        margin: 50px 0 0;
        border: 1px solid #e0e0e0;
        text-align: center;
        position: relative;
        transition: all 0.7s ease 0s;
        background: #e0e0e0;
        &:hover{ 
            border-color: @primary-color;
            .image{
                border-color:@primary-color;
            }
            .testimonial-content{
              background-color: @primary-color; 
            }
            .testimonial-content{
                .testimonial-profile{
                    .name{ 
                        color: #fff;
                    } 
                    .post{
                        color: lightblue;
                    }
                }
                .social-links{
                    li{
                        background-color: #fff;
                        a{
                                color:@primary-color;
                        }
                     }
                 }
            }
        } 
    .image{
        width: 95px;
        height: 95px;
        border-radius: 50%;
        background: #fff;
        position:absolute;
        top: -50px;
        left: 0;
        right: 0;
        margin: 0 auto;
        overflow: hidden;
        transition: all 0.7s ease 0s;
        border:5px solid #f2f2f2;              
        img{
            width: 100%;
            height: auto;
            border-radius: 50%;
        }
    }
    .title{
        font-size: 16px;
        font-weight: 700;
        color: #000;
        text-transform: uppercase;
        margin: 0 0 10px 0;
    }
    .description{
        font-size: 15px;
        color: #000;
        line-height: 25px;
        padding: 0 25px 15px;
        margin: 0;
    }
    .testimonial-content{
        padding: 15px 25px;
        border-top: none;
        text-align: center;
        transition: all 500ms ease 0s;        
        .testimonial-profile{ 
            .name{
                font-size: 16px;
                font-weight: bold;
                color: @primary-color;
                text-transform: uppercase;
                margin-bottom: 5px;
                margin-top: 0;
                transition: all 700ms ease 0s;
            }
            .post{
                font-size: 14px;
                color: #000;
                margin-bottom: 5px;
                text-transform: capitalize;
            }
        }    
        .rating{
            display: inline-block;
            margin-bottom: 14px;
            margin-top: 0;
            list-style: none;
            li{
                display: inline-block;
                font-size: 14px;
                color: #debe0f;
            }
        }
        .social-links{
            padding:0;
            margin:0;
            li{
                list-style:none;
                margin-right:5px;
                display:inline-block;
                background-color: #7f352f;
                    a{
                        width:30px;
                        height:30px;
                        line-height:30px;
                        color:#fff;
                        text-align:center;
                        &:hover{
                            text-decoration: none;
                            color:#150504;
                        }           
                    }
                }
            }
        } 
    }
    .owl-theme .owl-controls{
        width: 100%;
        position: absolute;
        top: 50%;
    }
    .owl-theme .owl-controls .owl-buttons div{
        width: 45px;
        height: 40px;
        line-height: 37px;
        border-radius: 3px;
        background: #fff;
        border: 1px solid #ececec;
        padding: 0;
        opacity: 1;
        transition: all 0.4s ease-in-out 0s;
    }
    .owl-theme .owl-controls .owl-buttons div:hover{
        background: #eabd44;
        border-color: #eabd44;
    }
    .owl-prev,
    .owl-next{
        position: absolute;
        left: -3%;
    }
    .owl-next{
        left: auto;
        right: -3%;
    }
    .owl-prev:before{
        content: "\f104";
        font-family: "fontAwesome";
        font-size: 25px;
        color: #ececec;
    }
    .owl-next:before{ 
        content: "\f105"; 
        font-family: "fontAwesome";
        font-size: 25px;
        color: #ececec;
    }
    .owl-stage-outer{
        padding-top: 50px;
        padding-bottom: 60px;
    }
}
.rs-testimonial-5{
    .testimonial-item{
        background: #92278f;
        border-radius: 30px;
        text-align: center;
        max-width: 750px;
        margin: 0 auto;
        padding: 45px 35px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
        .testi-img{
            top: -125px;
        }
        .testi-desc{
            padding: 0 30px;
            .testi-name{
                font-size: 20px;
                margin-top: 4px;
                span{
                    display: block;
                    font-size: 15px;
                    font-weight: 400;
                    padding-top: 5px;
                }
            }
            &:before,
            .testi-name,
            .testi-name span,
            p{
                color: #fff; 
            }
            &:before{
                display: none;
            }
            &:after{
                display: none;
            }
        }
    }
    
    .owl-stage-outer{
        padding-top: 125px;
        padding-bottom: 35px;
    }
    .owl-dots .owl-dot.active{
        background: #92278f;
    }
}
/* ------------------------------------
    19. Newsletter
---------------------------------------*/
.rs-newslatter {
    .newslatter-title {
        color: #ffffff;
        margin-bottom: 0;
        font-size: 30px;
    }
    .newslatter-form {
        .form-input {
            width: ~"calc(69% - 25px)";
            height: 50px;
            padding: 0 17px;
            border: 0;
            font-size: 16px;
            margin-right: 25px;
        }
        .form-button {
            width: 30%;
            height: 50px;
            border: 0;
            background-color: @primary-color;
            color: #ffffff;
            cursor: pointer;
            font-weight: 500;
            &:hover {
                background-color: @hover-color;
            }
        }
    }
}
/* ------------------------------------
    20. Rs-video
---------------------------------------*/
.rs-video {
    padding: 150px 0;
    position: relative;
    &:after{
        position: absolute;
        content: "";
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.6);
    }
    .video-content {
        text-align: center;
        position: relative;
        z-index: 11;
        a {
            display: inline-block;
            color: #ffffff;
            border: 2px solid @primary-color;
            z-index: 99;
            border: 2px solid @primary-color;
            padding: 5px;
            border-radius: 50%;
            width: 124px;
            height: 124px;
            margin: 0 auto;
            padding: 5px;
            margin-bottom: 30px;
            i {
                font-size: 50px;
                line-height: 110px;
                border: 2px solid #fff;
                height: 110px;
                width: 110px;
                border-radius: 50%;
                text-align: center;
                color: @primary-color;
                display: block;
                background: #fff;
                transition: 0.3s;
                -webkit-transition: 0.3s;
                -ms-transition: 0.3s;
                margin-bottom: 24px;
                padding-left: 4px;
            }
            &:hover {
                i{
                    border-color: @primary-color;
                    background: transparent;
                }
            }
        }
        span {
            display: block;
            font-size: 30px;
            color: #fff;
			font-weight: 700;
        }
    }
}

/* ------------------------------------
    21. Why Choose Us
---------------------------------------*/
.rs-why-choose {
	.sec-title{
		margin-bottom: 44px;
	}
    .choose-item {
        text-align:center;
        .choose-title {
            font-size: 15px;
            margin-bottom: 0;
            margin-top: 25px;
            font-family: @body-font;
            font-weight: 600;
            text-transform: uppercase;
        }
        i {
            display: inline-block;
            width: 80px;
            height: 80px;
            line-height: 80px;
            text-align: center;
            background-color: @primary-color;
            color: #ffffff;
            border-radius: 50%;
            font-size: 36px;
        }
    }
}
/* ------------------------------------
    22. Pricing Table
---------------------------------------*/
.rs-pricing {
    .pricing-plan {
        text-align: center;
        margin-bottom: 30px;
        -webkit-transition: all .25s ease;
        transition: all .25s ease;
        .pricing-head {
            background-color: #f0f0f0;
            padding: 35px;
            border-radius: 4px 4px 0px 0px;
            .name {
                font-family: @title-font;
                font-size: 18px;
                line-height: normal;
                margin-bottom: 10px;
                font-weight: 600;
                color: #444;
                text-transform: uppercase;
            }
            .price {
                color: #444;
                sup {
                    font-size: 20px;
                    line-height: 40px;
                    top: -3px;
                    margin-right: -7px;
                    vertical-align: top;
                    font-weight: 700;
                }
                .duration {
                    font-size: 15px;
                    font-weight: 600;
                    text-transform: uppercase;
                }
            }
            .value {
                font-size: 60px;
                line-height: 60px;
            }
        }
        .pricing-body {
            ul {
                padding-left: 0;
                list-style: none;
                font-size: 13px;
                color: #444;
                margin: 0;
                li {
                    padding: 15px 0;
                    font-size: 15px;
                    font-weight: 700;
                    text-transform: uppercase;
                    color: #303030;
                    &:nth-child(even) {
                        background-color: #f0f0f0;
                        padding: 20px 0;
                    }
                }
            }
        }
        .pricing-footer {
            .pricing-btn {
                display: block;
                padding: 20px;
                background-color: #212121;
                color: #fff;
                font-size: 15px;
                border-radius: 0 0 4px 4px;
                font-weight: 600;
            }
        }
        &:hover, &.featured {
            .pricing-head {
                .price, .name {
                    color: @primary-color;
                }
            }
            .pricing-footer {
                .pricing-btn {
                    background-color: @primary-color;
                    &:hover {
                        background-color: @hover-color;
                    }
                }
            }
        }
    }
}
/* ------------------------------------
    23. Instagram
---------------------------------------*/
.rs-instagram {
    position: relative;
    .instagram-desc {
        max-width: 630px;
        background: @primary-color;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        width: 100%;
        padding: 70px 0;
        z-index: 10;
        text-align: center;
        .title {
            font-size: 30px;
            color: #ffffff;
            margin-bottom: 10px;
        }
        .sub-title {
            font-size: 30px;
            color: #101010;
            margin-bottom: 0; 
        }
    }
    .instagram-item {
        margin-left: -1px;
        a {
            display: block;
            position: relative;
            &:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(17,17,17,0.5);
                opacity: 1;
                -webkit-transition: all 0.3s ease;
                transition: all 0.3s ease;
            }
        }
        &:hover {
            a:after {
                opacity: 0;                
            }
        }
    }
	.rs-carousel:last-child{
		margin-top: -1px;
	}
}
/* ------------------------------------
    24. About Us Page
---------------------------------------*/
.abt-title {
    h2 {
        font-size: 30px;
        line-height: normal;
        margin-bottom: 20px;
        padding-bottom: 16px;
        text-transform: uppercase;
        position: relative;
        &:after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            height: 5px;
            width: 100px;
            background-color: @primary-color;
        }
    }
    &.text-center {
        h2 {
            &:after {
                left: 50%;
                transform: translateX(-50%);
            }            
        }
    }
}
.rs-vision {
    .vision-img {
        position: relative;
        &:after {
            content:"";
            position: absolute;
            left: 0;
            bottom: 0;
            height: 100%;
            width: 100%;
            background-color: rgba(33, 33, 33, 0.5);
            opacity: 0;
            -webkit-transition: @transition;
            transition: @transition;
        }
        &:hover {
           &:after {
                opacity: 1;
           }   
        }
        .popup-youtube {
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 10;
            -webkit-transform: translateX(-50%) translateY(-50%);
            transform: translateX(-50%) translateY(-50%);
            &:after {
                position: absolute;
                font-family: FontAwesome;
                content: "\f04b";
                font-size: 36px;
                left: 50%;
                -webkit-transform: translateX(-50%) translateY(-50%);
                transform: translateX(-50%) translateY(-50%);
                transition: all 0.3s ease 0s;
                display: inline-block;
                height: 80px;
                line-height: 80px;
                width: 80px;
                text-align: center;
                border: 3px solid #fff;
                border-radius: 50%;
                padding-left: 8px;
                color: #fff;
            }
        }
    }
}
.rs-branches {
    .branches-item {
        text-align: center;
        position: relative;
        margin-bottom: 30px;
        img {
            margin-bottom: 15px;
        }
        h3 {
            font-size: 20px;
            margin-bottom: 7px;
            span {
                display: block;
            }
        }
        p {
            margin-bottom: 0;
        }
    }
    [class*="col-"] {
        + [class*="col-"] {
            .branches-item {
                &:after {
                    position: absolute;
                    content: '';
                    width: 64%;
                    height: 2px;
                    background-color: #ff3115;
                    left: -38%;
                    top: 70px;
                }            
            }
        }
    }
}

/* ------------------------------------
    25. Rs Timeline
---------------------------------------*/
.rs-timeline {
    .rs-timeline-icon {
        i {
            color: @primary-color;
        }
    }
    .rs-timeline-content {
        .rs-read-more {
            border-color: @primary-color;
            color: @primary-color;
            &:hover {
                background-color: @primary-color;
                color: #ffffff;
            }
        }
        .rs-date {
            color: @primary-color;
        }
    }
}
.rs-timeline-2 {
    position: relative;
    padding-bottom: 40px;
    .arrows {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        button {
            height: 42px;
            width: 42px;
            font-size: 0;
            margin: 0 5px;
            border: 0;
            background-color: #212121;
            color: @primary-color;
            cursor: pointer;
            outline: 0;
            border-radius: 50%;
            transition: all 0.3s ease 0s;
            &:hover {
                background-color: @primary-color;
                color: #fff;
            }
            &:after {
                font-family: FontAwesome;
                font-size: 22px;
                margin-top: 5px;
                display: inline-block;
            }
            &.arrow__prev {
                &:after {
                    content: "\f104";
                }
            }
            &.arrow__next {
                &:after {
                    content: "\f105";
                }
            }
        }
    }
    ol {
        li:not(:last-child)::after {
            background: @primary-color;
        }
    }
}

/* ------------------------------------
    26. Elements
---------------------------------------*/
.rs-accordion-style1{
    .card {
        border: 0;
        .card-header {
            padding: 0;
            border: 0;
            margin-bottom: 10px;
            background: transparent;
            .acdn-title {
                background-color: rgba(240,240,240,0.8);
                position: relative;
                margin-bottom: 0;
                font-size: 18px;
                height: 50px;
                line-height: 50px;
                padding: 0 20px;
                cursor: pointer;
                font-weight: 500;
                letter-spacing: 0.2px;
                -webkit-transition: .2s background-color ease-in-out;
                transition: .2s background-color ease-in-out;
                &:after {
                    position: absolute;
                    font-family: FontAwesome;
                    content: "\f0da";
                    right: 20px;
                    transition: @transition;
                }
                &:not(.collapsed) {
                    background-color: @primary-color;
                    color: #ffffff;
                    &:after {
                        transform: rotate(90deg);
                        color: #ffffff;
                    }
                }
            }
        }
        .card-body {
            padding: 5px 15px 18px;
        }
    }
}
/* ------------------------------------
    27. Blog Pages
---------------------------------------*/
.blog-page-area{
    .blog-inner{
        overflow: hidden;
        transition: 0.5s;
        -webkit-transition: 0.5s;
        -ms-transition: 0.5s;
        .blog-content{
            margin-top: 6px;
            .date{
                font-size: 13px;
                font-weight: 600;
            }
            ul.blog-meta{
                margin: 0 0 10px;
                li{
                    &:last-child{
                        padding-right: 0;
                    }
                    display: inline-block;
                    font-size: 13px;
                    font-weight: 600;
                    color: @primary-color;
                    padding-right: 10px;
                    i{
                        font-size: 13px;
                    }
                    a{
                        display: block;
                    }
                } 
            }
            p{
                font-size: 15px;
            }
            h4{
                margin: 0 0 8px;
                a{
                    font-size: 20px;
                    font-weight: 700;
                    color: @title-color;
                }
            }
            a.primary-btn{
                margin-top: 12px;
            }
        }
        .blog-images{
            overflow: hidden;
            position: relative;
            i{
                position: absolute;
                top: 50%;
                left: 50%;
                -ms-transform: translate(-50%, -50%); /* IE 9 */
                -webkit-transform: translate(-50%, -50%); /* Safari */
                transform: translate(-50%, -50%);
                z-index: 99;
                width: 35px;
                height: 35px;
                line-height: 35px;
                text-align: center;
                border-radius: 100%;
                border:1px solid @primary-color;
                color: @primary-color;
                opacity: 0;
            }
            &:after{            
                content:"";
                position: absolute;
                height: 100%;
                width: 100%;
                left: 0;
                top: 0;
                right: 0;
                background: rgba(0,0,0,0.6);
                transition: 0.5s;
                -webkit-transition: 0.5s;
                -ms-transition: 0.5s;
                transform: scale(0);
                -webkit-transform: scale(0);
                -ms-transform: scale(0);
            }
            img{
                transform: scale(1);
                transition: 0.5s;
                -webkit-transition: 0.5s;
                -ms-transition: 0.5s;
                width: 100%;
            }
        }
        &:hover{
            .blog-images{
                i{
                    opacity: 1;
                }
                img{
                    transform: scale(1.1);
                    transition: @transition;
                }
                 &:after{
                    content:"";
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    left: 0;
                    top: 0;
                    right: 0;
                    background: rgba(0,0,0,0.8);
                    transition: 0.5s;
                    -webkit-transition: 0.5s;
                    -ms-transition: 0.5s;
                    transform: scale(1) !important;
                    -webkit-transform: scale(1) !important;
                    ms-transform: scale(1) !important;
                }
            }
            .blog-content{
                h4{
                    a{
                        color: @primary-color;
                    }
                }
            }
        }
    }
    .pagination{
        margin-top: 0;
    }
}
/* ------------------------------------
    28 .Blog Details 
---------------------------------------*/
.single-blog-details{
	.single-image{
		margin-bottom: 25px;
		overflow: hidden;
		img{
			filter: grayscale(0%);
			transition: all 0.8s ease-in-out 0s;
			&:hover{
				filter: grayscale(100%);
				transform: scale(1.1);
			}
		}
	}
	.like-section h5,
	h5.top-title{
		font-size: 16px;
		color: @title-color;
		margin: 20px 0 15px;
		padding: 0;
		line-height: 25px;
	}
	.like-section h5 a{
		color: @title-color;
		&:hover{
			color: @primary-color;
		}
	}
	 h3.top-title,
	.like-section h3.title-bg{
		font-size: 20px;
	}
	.like-section span.date{
		font-size: 12px;
	}
    blockquote{
        background: #f0f0f0;
        border: medium none;
        margin: 25px 0 25px;
        padding:24px 25px 24px 63px;
        font-size: 16px;
        color: @title-color;
        font-style: italic;
        position: relative;
        font-weight: 400;
        i{
            position: absolute;
            left: 22px;
            font-size: 25px;
            top: 21px;
        }
    }
    h2{
        font-size: 20px;
        margin: 0 0 25px;
    }
    .share-section{
        border-top: 1px solid #ddd;
        padding-top: 20px;
        padding-bottom: 20px;
        .share-link1{
            padding: 0;
            margin: 0;
            float: right;
            li{
                float: left;
                list-style: none;
                &:first-child a{
                    margin-left: 0;
                }
                a{
                    padding: 7px 8px;
                    border: 1px solid #ddd;
                    font-size: 13px;
                    font-weight: 400;
                    color: @body-color;
                    border-radius: 4px;
                    margin-left: 14px;
                    &:hover{
                        background: @primary-color;
                        color: @title-color;
                        border: 1px solid @primary-color;
                    }
                }
                &:first-child{
                    a{
                        background: @primary-color;
                        color: @title-color;
                        border: 1px solid @primary-color;
                    }
                }
            }
        }
        .life-style{

            span{
                font-size: 13px;
                font-weight: 400;
                color: @body-color;
                margin-right: 12px;
                &:last-child{
                    margin-right: 0;
                }
                i{
                    margin-right: 4px;
                }
                a{
                    font-size: 13px;
                    font-weight: 400;
                    color: @body-color;
                    i{
                        font-size: 13px;
                        margin-right: 4px;
                    }
                    &:hover{
                        color: @primary-color;
                    }
                }
                &.author{
                    a{
                        color: #777777;
                        &:hover{
                            color: @primary-color;
                        }
                    }
                    
                }
            }
        }    
    }
    .share-section2{
        border: 1px solid #ddd;
        padding: 15px 15px 15px 15px;
        span{
            font-size: 15px;
            font-weight: 400;
            color: @title-color;
        }
        .share-link{
            float: right;
            li{
                &:first-child{
                    a{
                        margin-left: 0;
                    }
                }
                float: left;
                list-style: none;
                a{
                    font-size: 13px;
                    font-weight: 400;
                    color: @body-color;
                    padding: 7px 8px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    margin-left: 14px;
                    &:hover{
                        background:@primary-color;
                        color: #fff;
                        border: 1px solid @primary-color;
                    }
                }
            }
        }
    }
    .like-section{
        .col-xs-12{
            h3{
                margin: 14px 0 6px;
                a{
                    font-size: 15px;
                    font-weight: 400;
                    color: @title-color;
                }
            }
            span{
                font-size: 13px;
                font-weight: 400;
                i{
                    font-size: 13px;
                }
            }
            .popular-post-img{
                img{
                    transition: @transition;
                }
            }
            &:hover{
                a{
                    color: @primary-color;
                }
                .popular-post-img{
                    img{
                        opacity: .5;
                    }
                }
            }
        }
    }
    .next-pre-section{
        padding: 23px 0 15px;
        margin: 0;
        overflow: hidden;
        li{
            margin: 0;
            a{
                font-size: 15px;
                color: @body-color;
                i{
                    padding: 0;
                    font-size: 15px;
                    font-weight: 500;
                    color: @title-color;
                }
                &:hover{
                    color: @primary-color;
                    i{
                        color: @primary-color;
                    }
                }
            }
        }
        li.left-arrow{
            float: left;
            width: 50%;
            a{
                font-size: 15px;
                font-weight: 500;
                color: @body-color;
                i{
                    padding-right: 5px;
                }
            }
        }
        li.right-arrow{
            float: right;
            width: 50%;
            text-align: right;
            a{
                font-size: 15px;
                font-weight: 500;
                i{
                    padding-left: 5px;
                }
            }
        }
    }
    .author-comment{
        h3{
            font-size: 20px;
            text-transform: uppercase;
            font-weight: 700;
            margin: 42px 0 6px;
        }
        span{
            font-size: 13px;
            font-weight: 500;
            a{
                font-size: 13px;
                font-weight: 500;
            }
            i{
                font-size: 13px;
            }
        }
        h4{
            font-size: 15px;
            font-weight: 700;
        }
        ul{
            padding: 0;
            margin: 0;
            li{
                margin: 0 0 2px;
                padding: 20px 22px;
                border-bottom: 1px solid #ddd;
                &:nth-child(2){
                    margin-left: 100px;
                    .col-sm-2{
                        padding: 0;
                    }
                }
                &:last-child{
                    border-bottom: none;
                }
                .image-comments{
                    margin-top:0;
                    img{
                        width: 90px;
                        border-radius: 100%;
                    }
                }
                .reply{
                    display: block;
                    position: absolute;
                    text-align: right;
                    width: 95%;
                    a{
                        color: @title-color;
                        &:hover{
                            color:@primary-color;
                        }
                    }
                    i{
                       color: @title-color; 
                    }
                }
                .dsc-comments{
                    h4{
                        margin: 0 0 12px;
                    }
                    a{
                        color: @primary-color;
                    }
                    p{
                        margin: 6px 0 0;
                    }
                }
            }
        }
    }
    .leave-comments-area{
        padding-top: 20px;
        .form-group{
            margin-bottom: 20px;
        }
        h4{
            font-size: 20px;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 19px;
            font-family: @body-font;
        }
        label{
            color: @body-color; 
            font-weight: normal; 
            letter-spacing: 1px;
        }
        input, textarea{
            background: transparent;
            border: 1px solid #ddd;
            box-shadow: none;
            border-radius: 0;
            height: 45px;
            width: 100%;
        }
        textarea{
            height: 150px;
            width: 100%;
            max-width: 100%;
        }
        .btn-send{
            background: @primary-color;
            font-weight: 600;
            color: @white-color;
            font-size: 15px;
            line-height: 24px;
            border: none;
            border-radius: 0px;
            padding: 14px 23px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 14px;
            transition: @transition;
            cursor: pointer;
            &:hover{
                background: @title-color;
                color: @white-color;
            }
        }
    }
}

/* ------------------------------------
    29. Shop Page Start Here 
---------------------------------------*/
.shop-page-area {
    &.single-product-page {
        padding: 100px 0 0;
    }
    .single-details {
        padding: 0 0 35px;
        position: relative;
        overflow: hidden;
        text-align: center;
        transition: @transition;
        margin-bottom: 15px;
        .triangle_left{
            width: 0;
            height: 0;
            border-top: 70px solid transparent;
            border-right: 140px solid @primary-color;
            border-bottom: 70px solid transparent;
            position: absolute;
            top: -75px;
            left: -117px;
            transform: rotate(48deg);
            opacity: 0;
        }
         &:hover {
            background:#f5f5f5;
            .triangle_left{
                opacity: 1;
            }
            h3 {
                a {
                    color: @primary-color;
                }
            }
            .add-to-cart{
                background: @primary-color;
                color: #fff !important;
            }
            .images .overley {
                opacity: 1;
                transform: scaleY(1);
            }
        }
        .images {
            position: relative;
            overflow: hidden;
            margin-bottom:15px;
            a {
                display: block;
                img {
                    transition: all .3s ease-out;
                    width: 100%;
                }
            }
            .overley {
                position: absolute;
                left: 0;
                right: 0;
                text-align: center;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, .6);
                top: 0;
                transform: scaleY(0);
                transform-origin: 0 1 0;
                opacity: 0;
                transition: @transition;
                padding: 30px;
                .winners-details {
                    padding: 20% 0;
                    h4 {
                        margin-bottom: 10px;
                        padding-bottom: 10px;
                        color: #ffffff;
                        font-size: 18px;
                        position: relative;
                        &:after {
                            display: none;
                        }
                    }
                    .product-info {
                        margin: 0;
                        padding: 0px;
                        list-style: none;
                        top: 50%;
                        position: absolute;
                        transform: translateY(-50%);
                        transition: all .9s;
                        text-align: center;
                        left: 0;
                        right: 0;
                        li {
                            display: inline-block;
                            a {
                                color: #ffffff;
                                text-transform: capitalize;
                                text-decoration: none;
                                width: 30px;
                                height: 30px;
                                line-height: 30px;
                                border: 1px solid #ffffff;
                                text-align: center;
                                transition: @transition;
                                &:hover {
                                    color: @primary-color;
                                    border: 1px solid @primary-color;
                                }
                                i {
                                    font-size: 16px;
                                    color: #ffffff;
                                    margin: 0;
                                    transition: @transition;
                                    &:hover {
                                        color: @primary-color;
                                    }
                                }
                            }
                        }
                    }
                    p {
                        margin-bottom: 5px;
                        color: #ffffff;
                        font-size: 13px;
                        i {
                            color: #ffffff;
                            margin-right: 8px;
                        }
                    }
                }
            }
        }
        h4{
            margin-bottom: 10px !important;
            margin-left: 0;
            font-weight: 700;
            a {
                color: @title-color;
                transition: @transition;
                font-size: 20px;
                margin: 0;
                &:hover {
                    color: @primary-color;
                }
            }
        }
        .price-details {
            ul {
                margin: 0;
                padding: 0;
                text-align: center;
                li {
                    display: block;
                    &:first-child {
                        font-weight: 600;
                        font-size: 15px;
                        del {
                            margin-right: 5px;
                            font-weight: 400;
                            font-size: 15px;
                        }
                    }
                    .add-to-cart{
                        border: 1px solid @primary-color;
                        padding: 8px 18px;
                        color: @title-color;
                        font-size: 15px;
                        font-weight: 600;
                        display: inline-block;
                        margin-top: 15px;
                    }
                }
            }
        }
    }
    .topbar-area {
        overflow: hidden;
        padding-bottom: 54px;
        .showing-Short{
            .seclec-box{
                float: right;
            }
        }
        .showing-result {
            ul {
                margin: 0;
                padding: 0;
                list-style: none;
                text-align: left;
                li {
                    display: inline-block;
                    font-weight: 400;
                    color: @body-color;
                    margin-right: 0;
                    font-size: 14px;
                    height: 25px;
                    .form-group {
                        margin: 0;
                        &.seclect-box {
                            select {
                                &.form-control {
                                    display: inline-block;
                                    border: 0;
                                    background: transparent;
                                    border-radius: 0px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .pagination{
        margin-top: 0;
    }
    .classic-tabs.tabs-cyan{
    	display: block;
    }
}
/* ------------------------------------
    30.Shop Single Page Start Here 
---------------------------------------*/
.shop-single-page-area{
    .shop-single-btm-page-area{
        padding-bottom: 74px;
        overflow: hidden;
        padding-top: 60px;
        .sec-title{
            font-size: 30px;
            &:after{
                height: 5px;
            }
        }
        .sec-sub-title{
            margin-bottom: 42px;
            margin-top: 10px;
            font-size: 30px;
        }
    }
    .inner-single-product-slider{
        .inner{
            background: #f5f5f5;
            margin-bottom: 26px;
        }
        .single-product-nav{
            max-width: 300px;
            margin: 0 auto;
            .slick-slide{
                position: relative;
                margin: 0 5px;
                cursor: pointer;
                &:after{
                    background: rgba(0, 0, 0, 0.7);
                    content: "";
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    top: 0;
                }
            }
            .slick-current{
                &:after{
                    opacity:0;
                }
            } 
        }
        .slick-prev:before {
            content: "\f104";
        }
        .slick-prev:before,
        .slick-next:before{
            font-family: FontAwesome;
            opacity: 1;
            transition: all 0.5s ease 0s;
            font-size: 16px;
            line-height: 20px;
            color: @title-color;
        }
        .slick-prev:hover:before,
        .slick-next:hover:before{
            color: @primary-color;
        }
        .slick-next:before {
            content: "\f105";
        }
    }
    .left-area{
        h4{
            margin: 0 0 12px;
            color: @title-color;
            font-size: 20px;
        }
        .cat, 
		.tag{
            font-size: 13px;
            margin: 0 0 5px;
            strong{
                font-weight: 500;
            }
        }
        .cat{
            margin-top: 27px;
        }
    }
    .tab-btm{
        margin-top: 20px;
        vertical-align: top;
        .nav-item{
            position: relative;
            display: block;
            width: 100%;
            a{
                font-size: 15px;
                color: @body-color;
            }
            .active{
                background: #eee;
                &:before{
                    content: "\f105";
                    font-family: FontAwesome;
                    opacity: 1;
                    transition: all 0.5s ease 0s;
                    font-size: 16px;
                    line-height: 20px;
                    position: absolute;
                    top: 14px;
                    z-index: 99;
                    right: 5px;
                }
            }
        }
        .tab-content.card{
            border: none;
        }
        h4{
            margin: 15px 0 19px;
            font-weight: 500;
            color: @body-color;
        }
    }
}
/*-----------------------------------
    25. Check Out css Start Here
-------------------------------------*/
.rs-check-out{
    .title-bg{
        font-size: 24px;
        margin-bottom: 30px;
    }
	.check-out-box{
		padding: 0 0 40px 0;
        label{
            color: @body-color;
            font-weight: 400;
        }
        textarea,
        input{
            border-color: #bfbfbf; 
        }
        input{
            height: 40px;
            padding: 0 15px;
        }
		select{
			width: 100%;
			min-height: 40px;
            display: block;
            padding: 10px 15px;
            color: @body-color;
		}
        .checkbox{
            input{
                height: auto;
                margin-top: 7px;
            }
        }
	}
	.shipping-box{
        h3.title{
            font-size: 24px;
            margin: 0 0 30px;
        }
		.form-group{
			input{
				height: 80px;
			}
		}
	}
    .shipping-box input,
    .checkbox input{
        margin-right: 15px;
    }
	.product-demo{
		width: 100%;
		display: inline-block;
		padding-bottom: 20px;
        padding-top: 8px;
		h5{
			margin: 0;
			font-size: 15px;
			color: #505050;
		}
		.product-image{
			width: 15%;
			float: left;
		}
		.product-name{
			width: 30%;
			float:left;
			transform: translateY(100%);
		}
		.product-quantity{
			width: 25%;
			float: left;
			text-align: center;
			transform: translateY(100%);
		}
		.product-ititial-price{
			width: 30%;
			float: left;
			text-align: center;
			transform: translateY(100%);
		}

	}
	.product-price{
		table{
			border: 1px solid #b2b2b2;
			margin-bottom: 25px;
			tr{
				td{
					padding: 15px 25px;
					border-bottom: 1px solid #b2b2b2;
					input{
						margin-right: 5px;
					}
					&:last-child{
						text-align: right;
					}
					&.no-border{
						border: none;
					}
				}
			}
		}

	}
	.rs-payment-system{
		.payment-radio-btn1{
			padding: 15px 25px;
			border: 1px solid #b2b2b2;
			p{
				margin: 0;
				padding: 15px 0 15px 25px;
				font-size: 13px;
			}
		}
		.payment-radio-btn2{
			padding: 15px 25px;
			border: 1px solid #b2b2b2;
			border-top: none;
		}
		input{
			margin-right: 10px;
		}
		input.btn-send{
			width: 100%;
			margin-top: 25px;
			background-color: @primary-color;
			color: @white-color;
            font-weight: 600;
            text-transform: uppercase;
			border: none;
            height: 50px;
            line-height: 50px;
            text-align: c center;
            transition: 0.4s;
            -webkit-transition: 0.4s;
            -ms-transition: 0.4s;
            cursor: pointer;
			&:hover{
                background: @title-color;
			}
		}

	}
}

/*-----------------------------------
    32. Shipping Area Start Here
------------------------------------*/
.shipping-area {
    .button-area {
        ul {
            li a {
              display: block;
              padding: 15px;
              background: #f8f8f8;
              color: #646464;
              font-size: 18px;
            }
            li.active a {
                background: @primary-color;
                color: #fff; 
            }
        }
    }
    .product-list{
        table {
            margin: 0 0 30px; 
            tr {
                border: 1px solid #e7e7e7;
                padding: 25px;
                display: block;
                margin-bottom: -1px;
                td {
                    padding-right: 52px;
                    img {
                        width: 100%;
                        display: block;
                        max-width: 80px;
                    }
                    .des-pro {
                        display: block;
                        padding-right: 50px;
                        width: 210px;
                        @media screen and (max-width: 991px){
                            width:auto;
                        }
                        h4 {
                            margin: 0 0 10px; 
							font-size: 20px;
                        }
                        p {
                            color: #646464;
                             margin: 0; 
                        }
                    }
                    strong {
                        font-size: 20px;
                        display: block;
                        padding-right: 100px;
                        font-weight: 500; 
                        @media screen and (max-width: 991px){
                            padding-right: 10px;
                        }
                    }
                    .order-pro {
                        position: relative;
                        display: block;
                        margin-right: 100px; 
                        input {
                          width:110px;
                          height: 46px;  
                          box-shadow: none;
                          border: 1px solid #ccc;
                          text-align: center;
                          padding-right: 10px;
                          color: #888888;
                          font-size: 18px;    
                        }
                        div {
                          position: absolute;
                          top: 12px;
                          right: 0;
                          z-index: 999;
                          cursor: pointer; 
                        }
                        div.btn-plus {
                            right: 40px;
                        }
                        div.btn-minus {
                            right: 20px;
                        }
                    }
                    .prize {
                        color: @primary-color;
                        font-size: 18px;
                        font-weight: 500;
                        padding-right: 50px;
                    }
                    i {
                        display: block;
                        width: 30px;
                        height: 30px;
                        border: 1px solid #cccccc;
                        text-align: center;
                        line-height: 28px;
                        font-size: 15px;
                        cursor: pointer; 
                        color: #ccc;    
                        &:hover{
                            background:@primary-color;
                            color: #fff;
                        }
                    }
                }
            }
        }
        .total span {
          font-size: 20px;
          padding-right: 10px; 
        }
        .total strong {
           font-size: 28px;
           font-weight: 400; 
        }
    }
    .next-step{
        text-align: right;
         a {
          padding: 10px 30px;
          border: 1px solid @primary-color;
          background: @primary-color !important;
          color: #fff;
          text-transform: capitalize;
          font-size: 18px;
          background: transparent;
          margin-top: 25px;
          transition: @transition; 
          display: inline-block;
          &:hover{
            background:@title-color !important;
            color: #fff;
            border: 1px solid @title-color !important;
          }
        }
    }
    .form-area {
        h3 {
          font-weight: 500;
          padding: 15px 15px; 
          font-size: 22px;
        }
        form {
            fieldset {
              margin: 0 0 15px;
                label {
                    display: block;
                    width: 100%;
                    color: #333333;
                    font-weight: 400;
                    margin: 0 0 10px; 
                    font-size: 14px;
                }
                input {
                    display: block;
                    width: 100%;
                    margin: 0 0 10px;
                    height: 40px;
                    border-radius: 0;
                    padding: 0 15px; 
                    border:1px solid #ccc;
                }
                select {
                    display: block;
                    width: 100%;
                    margin: 0 0 10px;
                    height: 40px;
                    border-radius: 0;
                    padding: 0 15px;
                    color: #646464;
                    font-size: 13px; 
                    border:1px solid #ccc;
                }
            }
        }
    }
    .order-list {
        h3 {
          padding: 15px 0; 
          font-size: 24px;
        }
        table {
          width: 100%; 
            tr {
                width: 100%;
                display: block; 
                th{
                    font-weight: bold;
                    width: 50%;
                }
                td {
                    border: 1px solid #dedede;
                    padding: 15px 15px; 
                    font-weight: normal;
                    &:first-child {
                        width: 400px;
                        @media screen and (max-width: 480px){
                            width: 65%;
                        }
                    }
                    &:last-child {
                        width: 150px;
                        text-align: center; 
                    }
                }
            }
            .row-bold td {
                border: 1px solid #dedede;
                font-weight: 700; 
            }
        }
    }
    .panel-group{
        .panel {
            border-radius: 0;
            margin: 0; 
        }
        .panel-body {
            padding-left: 40px;
            padding-right: 100px; 
        }
        a {
            .checkbox {
                margin: 0; 
                padding: 10px 0;
                .cr {
                  position: relative;
                  display: inline-block;
                  background: #cccccc;
                  border-radius: 100%;
                  float: left;
                  margin-top: 0px;
                  margin-right: .5em; 
                  width: 15px;
                  height: 15px;
                }
                label {
                  margin: 0 !important;
                  padding: 0 !important;
                  text-transform: capitalize;
                  font-size: 18px;
                  font-weight: 700; 
                    input[type="checkbox"] {
                        display: none; 
                    }
                    input[type="checkbox"] + .cr > .cr-icon {
                        opacity: 1;
                        display: block;
                        color: @primary-color; 
                        width: 15px;
                        height: 15px;
                    }
                    input[type="checkbox"] + .cr > .cr-icon {
                        opacity: 0;
                        transition: all .3s ease-in;
                        display: block;
                        padding: 5px;
                        color: #2962ff; 
                    }
                }
            }
        }
        .panel-default > .panel-heading {
            background: transparent; 
        }
    }
}
.order-pro input[type=number]::-webkit-inner-spin-button,
.order-pro input[type=number]::-webkit-outer-spin-button {
   opacity: 1;
}
.coupon-fields{
    .input-text{
        padding: 5px 8px;
        width: 75%;
        margin-right: 10px;
        margin-bottom: 25px;
        }
    .apply-coupon{
        background: @primary-color;
        border:none;
        color: #fff;
        padding: 6px 8px;
        border:1px solid @primary-color;
        transition: @transition;
        &:hover{
            background:@title-color !important;
            border:1px solid @title-color;
            color: #fff;
        }
    }   
}

/*-------------------------------------
    32. Contact Page Section Start Here
--------------------------------------*/
.contact-page-section{
    #googleMap{
        height: 490px;
    }
	.map-text{
		padding-bottom: 22px;
		h3{
			font-size: 20px;
			margin: 0;
			padding: 25px 0 10px;
            font-weight: 600;
		}
		p{
			font-size: 15px;
			margin: 0;
			padding: 0;
		}
	}
	.contact-address-section{
		text-align: center;
		margin: 50px 0;
        .contact-info{
            background: #f0f0f0;
            border: 1px solid #e1e1e1;
            padding: 25px 0 23px;
            .contact-pd{
                padding: 0 10px;
            }
            i{
                color: @primary-color;
                font-size: 36px;
                margin: 0;
                padding: 0;
            }
            h4{
                font-size: 20px;
                font-weight: 600;
                text-transform: uppercase;
                color: @title-color;
                margin: 0;
                padding: 15px 0;
            }
            a{
                color: @body-color;
                display: block;
                &:hover{
                    color: @primary-color;
                }
            }
            p{
                color: @body-color;
                margin: 0;
                padding: 0;
            }
        }
        
	}
	.contact-comment-section{
		h3{
			font-size: 20px;
			margin: 0;
			padding-bottom: 30px;
            text-transform: uppercase;
            font-weight: 600;
		}
		form{
			.form-group{
                input{
                    height: 43px;
                    padding: 0 15px;
                }
                input,
                textarea{
                    border: none;
                    background: #f5f5f5;
                    border-radius: 0;
                    box-shadow: none;
                }
                label{
                    color: @body-color;
                    font-weight: 400;
                }
				input.btn-send{
					text-transform: uppercase;
					color: @white-color;
					background-color: @primary-color;
					margin-top: 15px;
					border: none;
                    height: 50px;
                    line-height: 50px;
                    text-align: center;
                    font-weight: 600;
                    padding: 0 50px;
                    cursor: pointer;
                    transition: 0.4s;
                    -webkit-transition: 0.4s;
                    -ms-transition: 0.4s;
                    &:hover{
                        background: @title-color;
                    }
				}
			}
		}
	}
}

/* ------------------------------------
    33. Rs Gallery
---------------------------------------*/
.rs-gallery {
    .gallery-item {
        position: relative;
        //margin-bottom: 30px;
        .gallery-desc {
            position: absolute;
            top: 50%;
            width: 100%;
            padding: 25px;
            text-align: center;
            -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
            h3 {
                margin-bottom: 12px;
                font-size: 24px;
                transition: @transition;
                -webkit-transform: translateY(10px);
                transform: translateY(10px);
                opacity: 0;
                a {
                    color: #ffffff;
                }
            }
            p {
                color: #e1e1e1;
                margin-bottom: 30px;
                transition: @transition;
                -webkit-transform: translateY(20px);
                transform: translateY(20px);
                opacity: 0;
            }
            .image-popup {
                display: inline-block;
                width: 50px;
                line-height: 50px;
                height: 50px;
                border-radius: 50%;
                text-align: center;
                background-color: #ff3115;
                color: #ffffff;
                font-size: 18px;
                -webkit-transform: translateY(35px);
                transform: translateY(35px);
                transition: @transition;
                opacity: 0;
            }
        }
        &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(33, 33, 33, 0.8);
            opacity: 0;
            transition: @transition;
        }
        &:hover {
            &:before {
                opacity: 1;
            }
            h3, p, .image-popup {
                -webkit-transform: translateY(0px);
                transform: translateY(0px);
                opacity: 1;
            }
        }
    }
    .row{
		margin-bottom: 30px;
	}
}

.rs-gallery-section{
    .single-gallery{
        position: relative;
        overflow: hidden;
        cursor: pointer;
        &:after{
            content: "";
            width: 80%;
            height: 80%;
            background: #92278f;
            position: absolute;
            top: 10%;
            left: 10%;
            display: block;
            overflow: hidden;
            opacity: 0.8;
            transform: scale(0);
            opacity: 0;
            visibility: hidden;
            transition: 0.4s;
            -webkit-transition: 0.4s;
            -ms-transition: 0.4s;
        }
        .popup-icon{
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            text-align: center;
            transform: translate(-50%, -50%);
            -webkit-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            z-index: 11;
            opacity: 0;
            visibility: hidden;
            transition: 0.3s;
            a{
                font-size: 20px;
                display: inline-block;
                margin: 0 8px;
                color: #fff;
            }
        }
         &:hover{
            &:after{
                opacity: 0.8;
                visibility: visible;
                transform: scale(1);
            }
            .popup-icon{
                opacity: 1;
                visibility: visible;
            }
        }
    }
}
/* ------------------------------------
    34. ScrollUp
---------------------------------------*/
#scrollUp {
    text-align: center;
    bottom: 40px;
    cursor: pointer;
    display: none;
    position: fixed;
    right: 40px;
    z-index: 999;
    i {
        background-color: @primary-color;
        height: 40px;
        font-size: 24px;
        width: 42px;
        color: #ffffff;
        line-height: 36px;
        transition: all 0.3s ease 0s;
        margin-left: 2px;
        &:hover {
            background-color: @hover-color;
            color: rgba(255,255,255,0.7);
        }
    }
}

/* ------------------------------------
    35.404 Page Area Start Here 
---------------------------------------*/
.error-page-area {
    text-align: center;
    .error-page {
        background: #f0f0f0;
        background-position: center center;
        background-size: cover;
        padding: 60px 0 100px;
        h1 {
            font-size: 250px;
            color: @title-color;
            line-height: 230px;
            margin: 0;
        }
        p {
            font-size: 18px;
            color: @title-color;
            font-weight: 500;
            letter-spacing: 3px;
            margin-bottom: 50px;
        }
    }
    .error-page-message {
        margin-top: 0;
        p {
            font-size: 20px;
            color: @title-color;
        }
        .home-page {
            a {
                display: inline-block;
                text-decoration: none;
                font-size: 15px;
                color: #ffffff;
                background: @primary-color;
                padding: 15px 35px;
                transition: @transition;
                font-weight: 700;
                &:hover {
                    background: @title-color;
                }
            }
        }
    }
}

/*-------------------------
    36.Preloader css
---------------------------*/
.book_preload{
    position: fixed;
    width: 100%;
    height: 100%;
    background: @primary-color;
    z-index: 999999;
}

.book {
    top: 50%;
    left: 0;
    -webkit-transform: translateY( -50%);
    transform: translateY(-50%);
    position: relative;
    margin: 0 auto;
    border: 5px solid #ecf0f1;
    width: 100px;
    height: 60px;
}

.book__page {
    position: absolute;
    left: 50%;
    top: -5px;
    margin: 0 auto;
    border-top: 5px solid #ecf0f1;
    border-bottom: 5px solid #ecf0f1;
    border-right: 5px solid #ecf0f1;
    background: @hover-color;
    width: 50px;
    height: 60px;
    -webkit-transform-origin: 0% 50%;
          transform-origin: 0% 50%;
    -webkit-animation: flip 1.2s infinite linear;
          animation: flip 1.2s infinite linear;
    -webkit-animation-fill-mode: forwards;
          animation-fill-mode: forwards;
}
.book__page:nth-child(1) {
  z-index: -1;
  -webkit-animation-delay: 1.4s;
          animation-delay: 1.4s;
}
.book__page:nth-child(2) {
  z-index: -2;
  -webkit-animation-delay: 2.8s;
          animation-delay: 2.8s;
}
.book__page:nth-child(3) {
  z-index: -3;
  -webkit-animation-delay: 4.2s;
          animation-delay: 4.2s;
}

@-webkit-keyframes flip {
  0% {
    -webkit-transform: perspective(600px) rotateY(0deg);
            transform: perspective(600px) rotateY(0deg);
  }
  20% {
    background: @primary-color;
  }
  29.9% {
    background: @primary-color;
  }
  30% {
    -webkit-transform: perspective(200px) rotateY(-90deg);
            transform: perspective(200px) rotateY(-90deg);
    background: @hover-color;
  }
  54.999% {
    opacity: 1;
  }
  55% {
    opacity: 0;
  }
  60% {
    -webkit-transform: perspective(200px) rotateY(-180deg);
            transform: perspective(200px) rotateY(-180deg);
    background: @hover-color;
  }
  100% {
    -webkit-transform: perspective(200px) rotateY(-180deg);
            transform: perspective(200px) rotateY(-180deg);
    background: @hover-color;
  }
}

@keyframes flip {
  0% {
    -webkit-transform: perspective(600px) rotateY(0deg);
            transform: perspective(600px) rotateY(0deg);
  }
  20% {
    background: @primary-color;
  }
  29.9% {
    background: @primary-color;
  }
  30% {
    -webkit-transform: perspective(200px) rotateY(-90deg);
            transform: perspective(200px) rotateY(-90deg);
    background: @hover-color;
  }
  54.999% {
    opacity: 1;
  }
  55% {
    opacity: 0;
  }
  60% {
    -webkit-transform: perspective(200px) rotateY(-180deg);
            transform: perspective(200px) rotateY(-180deg);
    background: @hover-color;
  }
  100% {
    -webkit-transform: perspective(200px) rotateY(-180deg);
            transform: perspective(200px) rotateY(-180deg);
    background: @hover-color;
  }
}

/* ------------------------------------
    37. Rs Footer
---------------------------------------*/
.rs-footer {
    color: #e8e8e8;
    margin-top: 98px;
    .footer-title {
        margin-bottom: 40px;
        padding-bottom: 5px;
        color: #ffffff;
        font-size: 16px;
        position: relative;
        font-weight: 600;
        &:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -5px;
            height: 1px;
            width: 50px;
            background-color: @primary-color;
        }
    }
    .container {
        position: relative;
    }
    .footer-contact-desc {
        margin: 0;
        background: #222;
        text-align: center;
        padding: 35px;
        position: absolute;
        left: 0;
        right: 0;
        margin: -100px auto 0;
        z-index: 111;
        border-radius: 30px;
        box-shadow: 0 0 20px rgba(0,0,0,.7);
        div[class*="col-"] {
                + div[class*="col-"] {
                    .contact-inner {
                        border-left: 1px solid #e2e2e2;
                        &:before, &:after {
                            content: '';
                            position: absolute;
                            height: ~"calc(100% - 40px)";
                            width: 1px;
                            background-color: #e2e2e2;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                        &:before {
                            left: 3px;
                        }
                        &:after {
                            left: -5px;
                        }
                    }
                    
                }
            }
        .contact-inner {
            position: relative;
            i {
                font-size: 28px;
                margin-bottom: 12px;
                color: @primary-color;
            }
            .contact-title {
                margin-bottom: 10px;
                color: #ffffff;
                font-size: 20px;
            }
            .contact-desc {
                color: rgba(255,255,255,0.7);
                margin-bottom: 0;
                font-size: 15px;
            }
        }
    }

    .footer-top {
        padding-top: 140px;
        .recent-post-widget {
            .post-item {
                display: -ms-flexbox;
                display: -webkit-flex;
                display: flex;
                -ms-flex-align: center;
                -webkit-align-items: center;
                align-items: center;
                -webkit-transition: all 0.4s ease 0s;
                transition: all 0.4s ease 0s;
                .post-date {
                    width: 70px;
                    height: 65px;
                    flex: 0 0 70px;
                    text-align: center;
                    float: left;
                    background-color: @primary-color;
                    color: #ffffff;
                    margin-right: 15px;
                    -webkit-transition: all 0.4s ease 0s;
                    transition: all 0.4s ease 0s;
                     span {
                        display: block;
                        &:first-child {
                            margin-top: 10px;
                        }
                        &:last-child {
                            font-size: 15px;                         
                        }
                    }
                }
                .post-title {
                    font-size: 15px;
                    line-height: 24px;
                    margin-bottom: 0;
                    font-weight: 400;
                    a {
                        font-family: 'Montserrat', sans-serif;
                        color: #e8e8e8;
                        &:hover, &:focus {
                            color: #bbbbbb;
                        }                   
                    }
                }
                .post-category {
                    font-size: 15px;
                }
                 .post-item {
                    margin-top: 18px;
                    padding-top: 18px;
                    border-top: 1px solid rgba(102, 102, 102, 0.5);
                }
            }
        }
        .sitemap-widget {
            li {
                width: 50%;
                float: left;
                line-height: 33px;
                a {
                    color: #e8e8e8;
                    display: inline-block;
                    position: relative;
                    &:hover, &:focus {
                        color: @primary-color;
                    }
                    i {
                        padding-right: 10px;
                    }
                }
            }
        }
        .flickr-feed {
            li {
                display: inline-block;
                margin: 2px 3px;
                overflow: hidden;
                position: relative;
                width: 76px;
                img {
                    -webkit-transition: .3s ease all;
                    transition: .3s ease all;
                }
                &:hover {
                    img {
                        opacity: 0.7;
                    }
                }
            }
        }
        .news-form {
            position: relative;
            input {
                background: rgba(0, 0, 0, 0);
                border: 1px solid @primary-color;
                color: #ffffff;
                height: 50px;
                outline: 0 none;
                padding: 5px 15px;
                width: 100%;
            }
            button {
                background: @primary-color;
                border: none;
                color: #ffffff;
                font-size: 18px;
                height: 100%;
                position: absolute;
                right: 0;
                top: 0;
                width: 60px;
                transition: @transition;
                cursor: pointer;
                &:hover {
                    background: @hover-color;
                }
            }
        }
        .about-widget{
            img {
                margin-bottom: 25px;
            }
        }
    }
    .footer-share {
        text-align: center;
        margin-top: 50px;
        ul {
            li {
                display: inline-block;
                a {
                    font-size: 13px;
                    display: block;
                    width: 42px;
                    height: 42px;
                    border-radius: 50%;
                    line-height: 44px;
                    text-align: center;
                    color: #fff;
                    transition: all .3s ease 0s;
                    background: rgba(255,255,255,.15);
                    &:hover {
                        background-color: @primary-color;
                        color: #ffffff;
                    }
                }
                + li {
                    margin-left: 5px;
                }
            }
        }
    }
    .footer-bottom {
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.15);
        padding: 18px 0;
        margin-top: 35px;
        .copyright {
            p {
                opacity: 0.95;
                margin-bottom: 0;
                font-size: 15px;
            }
        }
    }
    &.rs-footer-style8{
       .footer-top{
            .recent-post-widget {
                .post-item {
                     padding-bottom: 15px;
                     padding-top: 15px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
                     &:last-child{
                         border: none;
                     }
                    .post-date {
                         width: 70px;
                         height: 65px;
                         flex: 0 0 70px;
                         text-align: center;
                         color: #fff;
                         margin-right: 15px;
                         transition: all .3s ease 0s;
                         background: #ff3115 !important;
                         line-height: 27px;
                         span {
                            display: block;
                            &:first-child {
                                margin-top: 10px;
                            }
                            &:last-child {
                                font-size: 15px;                         
                            }
                        }
                    }
                    .post-title {
                        font-size: 15px;
                        line-height: 24px;
                        margin-bottom: 0;
                        font-weight: 400;
                        a {
                            font-family: 'Montserrat', sans-serif;
                            color: @white-color;
                            &:hover, &:focus {
                                color: @primary-color !important;
                            }                   
                        }
                    }             
                }
            }
            .form-inner{
                position: relative;
                max-width: 280px;
              &:before{
                    content: "\f1d9";
                    font-family: FontAwesome;
                    font-style: normal;
                    font-weight: 400;
                    text-decoration: inherit;
                    position: absolute;
                    right: 30px;
                    top: 10px;
                    color: #fff;
                    pointer-events: none;
                    z-index: 11;
                    font-size: 20px;
                }
                input[type=email]{
                    font-size: 14px;
                    padding: 12px 0 12px 16px;
                    border: none;
                    border-radius: 25px!important;
                    height: 46px;
                    position: relative;
                    display: block;
                    line-height: 1.428571429;
                    color: #555;
                    background-color: #fff;
                    outline: 0;
                    width: 100%;
                }
                input[type=submit]{
                    position: absolute;
                    border-radius: 25px;
                    top: 2px;
                    right: 2px;
                    background: #ff3115;
                    color: #fff;
                    padding: 10px 16px;
                    border: 0;
                    transition: .2s;
                    font-size: 0;
                    height: 42px;
                    width: 74px;
                    min-width: auto!important;
                }
            }
       }
    }
    &.rs-footer-style7{
        position: relative;
        background: #f2f2f2;
        .footer-top{
             padding-top: 70px !important;
            .about-widget{
                img{

                }
                p{

                }
                .margin-remove{

                }
            }
            .footer-title{
                color: @title-color;
                &:after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -5px;
                    height: 1px;
                    width: 50px;
                    background: @primary-color;
                }
            }
           .recent-post-widget {
               .post-item {
                    padding-bottom: 15px;
                    padding-top: 15px;
                    border-bottom: 1px solid #e9e2e2;
                    &:last-child{
                        border: none;
                    }
                   .post-date {
                        width: 70px;
                        height: 65px;
                        flex: 0 0 70px;
                        text-align: center;
                        color: #fff;
                        margin-right: 15px;
                        transition: all .3s ease 0s;
                        background: #ff3115 !important;
                        line-height: 27px;
                        span {
                           display: block;
                           &:first-child {
                               margin-top: 10px;
                           }
                           &:last-child {
                               font-size: 15px;                         
                           }
                       }
                   }
                   .post-title {
                       font-size: 15px;
                       line-height: 24px;
                       margin-bottom: 0;
                       font-weight: 400;
                       a {
                           font-family: 'Montserrat', sans-serif;
                           color: #505050;
                           &:hover, &:focus {
                               color: @primary-color !important;
                           }                   
                       }
                   }             
               }
           }
            .sitemap-widget {
                li {
                    width: 50%;
                    float: left;
                    line-height: 33px;
                    a {
                        color: #505050;
                        display: inline-block;
                        position: relative;
                        &:hover, &:focus {
                            color: @primary-color !important;
                        }
                        i {
                            padding-right: 10px;
                        }
                    }
                }
            }
            .flickr-feed {
                li {
                    display: inline-block;
                    margin: 2px 3px;
                    overflow: hidden;
                    position: relative;
                    width: 76px;
                    img {
                        -webkit-transition: .3s ease all;
                        transition: .3s ease all;
                    }
                    &:hover {
                        img {
                            opacity: 0.7;
                        }
                    }
                }
            }
            .form-inner{
                position: relative;
                max-width: 280px;
              &:before{
                    content: "\f1d9";
                    font-family: FontAwesome;
                    font-style: normal;
                    font-weight: 400;
                    text-decoration: inherit;
                    position: absolute;
                    right: 30px;
                    top: 10px;
                    color: #fff;
                    pointer-events: none;
                    z-index: 11;
                    font-size: 20px;
                }
                input[type=email]{
                    font-size: 14px;
                    padding: 12px 0 12px 16px;
                    border: none;
                    border-radius: 25px!important;
                    height: 46px;
                    position: relative;
                    display: block;
                    line-height: 1.428571429;
                    color: #555;
                    background-color: #fff;
                    outline: 0;
                    width: 100%;
                }
                input[type=submit]{
                    position: absolute;
                    border-radius: 25px;
                    top: 2px;
                    right: 2px;
                    background: #ff3115;
                    color: #fff;
                    padding: 10px 16px;
                    border: 0;
                    transition: .2s;
                    font-size: 0;
                    height: 42px;
                    width: 74px;
                    min-width: auto!important;
                }
            }
            .about-widget{
                img {
                    margin-bottom: 25px;
                }
            }        
        }
        .footer-share {
            text-align: center;
            margin-top: 50px;
            ul {
                li {
                    display: inline-block;
                    a {
                        background: none !important;
                        color: @title-color !important;
                        &:hover {
                            color: @primary-color !important;

                        }
                    }
                     li {
                        margin-left: 5px;
                    }
                }
            }
        }

        &:before{
            background: #f2f2f2;
            transform: skewY(175deg);
            content: "";
            height: 100%;
            right: 0;
            position: absolute;
            top: -100px;
            width: 100%;
            z-index: -1;
        }
        .footer-contact-desc{
            padding-top: 0;
            border-bottom: 1px solid #e9e2e2;
            transform: translateY(0);
            position: static;
            border-radius: 0;
            box-shadow: none;
            margin: -100px auto 0;
            background: none;
            .contact-inner{
                i{
                    &:before{
                        color: @primary-color;
                    }
                }
                .contact-title{
                    color: @title-color;
                }
                .contact-desc{
                    color: @body-color;
                }
            }
            div[class*="col-"] {
                + div[class*="col-"] {
                    .contact-inner {
                        border-left: 1px solid #e2e2e2;
                        &:before, &:after {
                            content: '';
                            position: absolute;
                            height: ~"calc(100% - 40px)";
                            width: 1px;
                            background-color: #e2e2e2;
                            top: 50%;
                            transform: translateY(-50%);
                        }
                        &:before {
                            left: 3px;
                        }
                        &:after {
                            left: -5px;
                        }
                    }
                    
                }
            }
        }
    }
    
}
.copyright_style7{
        text-align: center;
        border-top: 1px solid #e9e2e2 !important;
        padding: 18px 0;
        margin-top: 35px;
        background: none !important;
        .copyright {
            p {
                opacity: 0.95;
                margin-bottom: 0;
                font-size: 15px;
                a{
                    color: @primary-color;
                    &:hover{
                        color: @title-color;
                    }
                }
            }
        }   
}
.rs-footer-2{
    .footer-share{
        margin-top: 20px;
    }
}

@-webkit-keyframes rs-animation-scale-up {
    0% {
        opacity: 0;
        -webkit-transform: scale(.2)
    }
    100% {
        opacity: 1;
        -webkit-transform: scale(1)
    }
}

@keyframes rs-animation-scale-up {
    0% {
        opacity: 0;
        transform: scale(.2)
    }
    100% {
        opacity: 1;
        transform: scale(1)
    }
}

@media only screen and (max-width: 991px) {
    .instructor-home .rs-menu-toggle {
        color: @title-color !important;
    }
    .instructor-home .rs-menu-toggle i{
        color: @primary-color;
    }
}

/*---------------------
    Pulse Animation
---------------------*/
@keyframes pulse-border {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
        opacity: 0;
    }
}


/*------------------------------
    Swing Animation
------------------------------*/
@keyframes swing-anim {
    from {
        transform: rotate(60deg);
        -webkit-transform: rotate(60deg);
    }
    to {
        transform: rotate(-60deg);
        -webkit-transform: rotate(-60deg);
    }
}
@-webkit-keyframes swing-anim {
    from {
        transform: rotate(60deg);
        -webkit-transform: rotate(60deg);
    }
    to {
        transform: rotate(-60deg);
        -webkit-transform: rotate(-60deg);
    }
}
@keyframes swing-anim2 {
    from {
        transform: rotate(40deg);
        -webkit-transform: rotate(40deg);
    }
    to {
        transform: rotate(-40deg);
        -webkit-transform: rotate(-40deg);
    }
}
@-webkit-keyframes swing-anim2 {
    from {
        transform: rotate(40deg);
        -webkit-transform: rotate(40deg);
    }
    to {
        transform: rotate(-40deg);
        -webkit-transform: rotate(-40deg);
    }
}
