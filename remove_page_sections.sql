-- Remove Page Sections System - Database Cleanup
-- This script removes all database structures related to the page sections system

-- Step 1: Drop the page_sections table
DROP TABLE IF EXISTS `page_sections`;

-- Step 2: Remove any page sections related menu items from sidebar
DELETE FROM `sidebar_sub_menus` 
WHERE `url` = 'admin/front/pagesections' 
   OR `activate_controller` = 'pagesections';

-- Step 3: Clean up any page sections related settings (if any exist)
DELETE FROM `sch_settings` 
WHERE `name` LIKE '%page_section%' 
   OR `name` LIKE '%page_sections%';

-- Step 4: Remove any page sections related system settings
DELETE FROM `system_settings` 
WHERE `type` LIKE '%page_section%' 
   OR `type` LIKE '%page_sections%';

-- Verification queries (run these to check cleanup)
-- SELECT COUNT(*) as page_sections_table_exists FROM information_schema.tables WHERE table_name = 'page_sections';
-- SELECT * FROM sidebar_sub_menus WHERE url LIKE '%pagesection%' OR activate_controller LIKE '%pagesection%';
