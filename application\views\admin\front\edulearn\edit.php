<script src="<?php echo base_url(); ?>backend/plugins/ckeditor/ckeditor.js"></script>
<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-pencil"></i> <?php echo $title; ?>
        </h1>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title">
                            Edit: <?php echo isset($section_info['name']) ? $section_info['name'] : ucwords(str_replace('_', ' ', $section_name)); ?> 
                            <small>(Edulearn Homepage)</small>
                        </h3>
                        <div class="box-tools pull-right">
                            <a href="<?php echo base_url('admin/front/edulearn'); ?>" class="btn btn-default btn-sm">
                                <i class="fa fa-arrow-left"></i> Back to List
                            </a>
                        </div>
                    </div>
                    
                    <div class="box-body">
                        <?php if ($this->session->flashdata('msg')): ?>
                            <?php echo $this->session->flashdata('msg'); ?>
                        <?php endif; ?>
                        
                        <!-- Content Status Indicator -->
                        <?php if ($content && isset($content['school_id']) && $content['school_id'] > 0): ?>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> You are editing <strong>customized content</strong> for your school.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i> You are creating <strong>new customized content</strong> for your school. The form is pre-filled with default content.
                            </div>
                        <?php endif; ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <form id="content-form" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="section_name" value="<?php echo $section_name; ?>">
                                    
                                    <!-- Section Info -->
                                    <div class="form-group">
                                        <label class="control-label">Section Information</label>
                                        <div class="well well-sm">
                                            <strong>Section:</strong> <?php echo isset($section_info['name']) ? $section_info['name'] : $section_name; ?><br>
                                            <strong>Type:</strong> <?php echo isset($section_info['type']) ? ucfirst($section_info['type']) : 'Text'; ?><br>
                                            <strong>Group:</strong> <?php echo isset($section_info['group']) ? $section_info['group'] : 'General'; ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Content Title -->
                                    <div class="form-group">
                                        <label for="content_title" class="control-label">
                                            Content Title
                                            <?php if (isset($section_info['type']) && $section_info['type'] == 'text'): ?>
                                                <span class="text-info">(Main content for this section)</span>
                                            <?php endif; ?>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="content_title" 
                                               name="content_title" 
                                               value="<?php echo isset($content['content_title']) ? htmlspecialchars($content['content_title']) : ''; ?>"
                                               placeholder="Enter section title">
                                    </div>
                                    
                                    <!-- Content Text -->
                                    <div class="form-group">
                                        <label for="content_text" class="control-label">
                                            Content Text
                                            <?php if (isset($section_info['type']) && $section_info['type'] == 'textarea'): ?>
                                                <span class="text-info">(Main content for this section)</span>
                                            <?php endif; ?>
                                        </label>
                                        <?php if (isset($section_info['type']) && $section_info['type'] == 'textarea'): ?>
                                            <textarea class="form-control" 
                                                      id="content_text" 
                                                      name="content_text" 
                                                      rows="6"
                                                      placeholder="Enter section content"><?php echo isset($content['content_text']) ? htmlspecialchars($content['content_text']) : ''; ?></textarea>
                                        <?php else: ?>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="content_text" 
                                                   name="content_text" 
                                                   value="<?php echo isset($content['content_text']) ? htmlspecialchars($content['content_text']) : ''; ?>"
                                                   placeholder="Enter section content">
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Content Image -->
                                    <div class="form-group">
                                        <label for="content_image" class="control-label">Content Image (Optional)</label>
                                        <input type="file" 
                                               class="form-control" 
                                               id="content_image" 
                                               name="content_image" 
                                               accept="image/*">
                                        <small class="help-block">
                                            Supported formats: JPG, PNG, GIF. Maximum size: 2MB.
                                        </small>
                                        
                                        <?php if (isset($content['content_image']) && !empty($content['content_image'])): ?>
                                            <div class="current-image mt-2">
                                                <label>Current Image:</label><br>
                                                <img src="<?php echo base_url('uploads/school_content/edulearn/' . $content['content_image']); ?>" 
                                                     alt="Current Image" 
                                                     class="img-thumbnail" 
                                                     style="max-width: 200px; max-height: 150px;">
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="form-group">
                                        <button type="submit" id="submit-btn" name="submit" value="submit" class="btn btn-primary">
                                            <i class="fa fa-save"></i> Save Changes
                                        </button>
                                        <button type="button" class="btn btn-info" id="preview-btn">
                                            <i class="fa fa-eye"></i> Preview
                                        </button>
                                        <a href="<?php echo base_url('admin/front/edulearn'); ?>" class="btn btn-default">
                                            Cancel
                                        </a>
                                        <div id="form-status" class="mt-2"></div>
                                    </div>
                                </form>
                            </div>
                            
                            <div class="col-md-4">
                                <!-- Help Panel -->
                                <div class="box box-info">
                                    <div class="box-header with-border">
                                        <h3 class="box-title"><i class="fa fa-lightbulb-o"></i> Tips</h3>
                                    </div>
                                    <div class="box-body">
                                        <h5>Content Guidelines:</h5>
                                        <ul class="list-unstyled">
                                            <li><i class="fa fa-check text-success"></i> Keep titles concise and descriptive</li>
                                            <li><i class="fa fa-check text-success"></i> Use clear, engaging language</li>
                                            <li><i class="fa fa-check text-success"></i> Optimize images for web (under 2MB)</li>
                                            <li><i class="fa fa-check text-success"></i> Preview before saving</li>
                                        </ul>
                                        
                                        <h5>Section-Specific Tips:</h5>
                                        <?php if (strpos($section_name, 'slider') !== false): ?>
                                            <p><strong>Slider Content:</strong> Use compelling headlines and brief, action-oriented descriptions to capture visitor attention.</p>
                                        <?php elseif (strpos($section_name, 'service') !== false): ?>
                                            <p><strong>Service Content:</strong> Highlight key benefits and features that set your school apart.</p>
                                        <?php elseif (strpos($section_name, 'about') !== false): ?>
                                            <p><strong>About Content:</strong> Share your school's story, values, and what makes you unique.</p>
                                        <?php elseif (strpos($section_name, 'counter') !== false): ?>
                                            <p><strong>Statistics:</strong> Use impressive numbers that showcase your school's achievements and scale.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- Reset Panel -->
                                <?php if ($content && isset($content['school_id']) && $content['school_id'] > 0): ?>
                                <div class="box box-warning">
                                    <div class="box-header with-border">
                                        <h3 class="box-title"><i class="fa fa-undo"></i> Reset Content</h3>
                                    </div>
                                    <div class="box-body">
                                        <p>Reset this section back to default content?</p>
                                        <form method="post" action="<?php echo base_url('admin/front/edulearn/reset_to_default'); ?>">
                                            <input type="hidden" name="section_name" value="<?php echo $section_name; ?>">
                                            <button type="submit" class="btn btn-warning btn-sm" onclick="return confirm('Are you sure? This will remove your customizations.')">
                                                <i class="fa fa-undo"></i> Reset to Default
                                            </button>
                                        </form>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="previewModalLabel">
                    <i class="fa fa-eye"></i> Content Preview
                </h4>
            </div>
            <div class="modal-body">
                <div class="preview-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="preview-section">
                                <h4 id="preview-title"></h4>
                                <div id="preview-text"></div>
                                <div id="preview-image"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize rich text editor for textarea fields
    <?php if (isset($section_info['type']) && $section_info['type'] == 'textarea'): ?>
    if (typeof CKEDITOR !== 'undefined') {
        CKEDITOR.replace('content_text', {
            height: 200,
            toolbar: [
                ['Bold', 'Italic', 'Underline'],
                ['NumberedList', 'BulletedList'],
                ['Link', 'Unlink'],
                ['RemoveFormat']
            ]
        });
    }
    <?php endif; ?>
    
    // AJAX form submission
    $('#content-form').on('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        $('#submit-btn').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Saving...');
        $('#form-status').html('');
        
        // Create FormData object to handle file uploads
        var formData = new FormData(this);
        
        // Add CKEDITOR content if available
        <?php if (isset($section_info['type']) && $section_info['type'] == 'textarea'): ?>
        if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.content_text) {
            formData.set('content_text', CKEDITOR.instances.content_text.getData());
        }
        <?php endif; ?>
        
        $.ajax({
            url: '<?php echo base_url('admin/front/edulearn/save_content_ajax'); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                // Reset button state
                $('#submit-btn').prop('disabled', false).html('<i class="fa fa-save"></i> Save Changes');
                
                if (response.status == 1) {
                    // Success message
                    $('#form-status').html('<div class="alert alert-success mt-3"><i class="fa fa-check-circle"></i> ' + response.message + '</div>');
                    
                    // Auto-hide success message after 3 seconds
                    setTimeout(function() {
                        $('#form-status').fadeOut();
                    }, 3000);
                } else {
                    // Error message
                    $('#form-status').html('<div class="alert alert-danger mt-3"><i class="fa fa-exclamation-circle"></i> ' + response.message + '</div>');
                }
            },
            error: function() {
                // Reset button state
                $('#submit-btn').prop('disabled', false).html('<i class="fa fa-save"></i> Save Changes');
                
                // Error message
                $('#form-status').html('<div class="alert alert-danger mt-3"><i class="fa fa-exclamation-circle"></i> An error occurred while saving. Please try again.</div>');
            }
        });
    });

    // Preview functionality
    $('#preview-btn').click(function() {
        var title = $('#content_title').val();
        var text = '';

        // Get text content from CKEditor if available, otherwise from input/textarea
        <?php if (isset($section_info['type']) && $section_info['type'] == 'textarea'): ?>
        if (typeof CKEDITOR !== 'undefined' && CKEDITOR.instances.content_text) {
            text = CKEDITOR.instances.content_text.getData();
        } else {
            text = $('#content_text').val();
        }
        <?php else: ?>
        text = $('#content_text').val();
        <?php endif; ?>

        // Update preview content
        $('#preview-title').text(title || 'No title');
        $('#preview-text').html(text || 'No content');

        // Handle image preview
        var imageFile = $('#content_image')[0].files[0];
        if (imageFile) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-image').html('<img src="' + e.target.result + '" class="img-responsive" style="max-width: 300px; margin-top: 10px;">');
            };
            reader.readAsDataURL(imageFile);
        } else {
            // Show current image if exists
            <?php if (isset($content['content_image']) && !empty($content['content_image'])): ?>
            $('#preview-image').html('<img src="<?php echo base_url('uploads/school_content/edulearn/' . $content['content_image']); ?>" class="img-responsive" style="max-width: 300px; margin-top: 10px;">');
            <?php else: ?>
            $('#preview-image').html('');
            <?php endif; ?>
        }

        // Show preview modal
        $('#previewModal').modal('show');
    });
});
</script>
