<?php
if (!$form_admission) {
    ?>
    <div class="alert alert-danger">
        <?php echo $this->lang->line('admission_form_disable_please_contact_to_administrator'); ?>
    </div>
    <?php
    return;
}
?>

<?php

if ($this->session->flashdata('msg')) {
    $message = $this->session->flashdata('msg');
    echo $message;
}
?>

<div class="row">
    <div class="col-md-12 text-center" style="padding:25px;">
        <h3 class="entered mt0"><?php echo $this->lang->line('edit_online_admission'); ?></h3>
    </div>

</div>

<div class="" style="background:var(--theme);padding: 25px;">
        <form id="form1" class="spaceb60 spacet40 onlineform" action="<?php echo base_url() . 'welcome/editonlineadmission/' . $reference_no; ?>" name="employeeform" method="post" accept-charset="utf-8" enctype="multipart/form-data">
        <div class="printcontent card" style="padding:25px ;margin-bottom: 20px;">
            <!-- Basic Details Section -->
            <div class="row">
                <h4 class="pagetitleh2"><?php echo $this->lang->line('basic_details'); ?></h4>
                <!-- Class -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('class'); ?><small class="req"> *</small></label>
                        <select id="class_id" name="class_id" class="form-control">
                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                            <?php foreach ($classlist as $class): ?>
                                <option value="<?php echo $class['id']; ?>" <?php echo ($class_id == $class['id']) ? "selected" : ""; ?>>
                                    <?php echo $class['class']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span class="text-danger"><?php echo form_error('class_id'); ?></span>
                    </div>
                </div>

                <!-- First Name -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('first_name'); ?><small class="req"> *</small></label>
                        <input id="firstname" name="firstname" type="text" class="form-control" value="<?php echo set_value('firstname', $firstname); ?>" autocomplete="off" />
                        <span class="text-danger"><?php echo form_error('firstname'); ?></span>
                    </div>
                </div>

                <!-- Middle Name (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('middlename')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('middle_name'); ?></label>
                            <input id="middlename" name="middlename" type="text" class="form-control" value="<?php echo set_value('middlename', $middlename); ?>" autocomplete="off" />
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Last Name (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('lastname')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('last_name'); ?></label>
                            <input id="lastname" name="lastname" type="text" class="form-control" value="<?php echo set_value('lastname', $lastname); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('lastname'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div><!-- ./row -->

            <!-- Gender, DOB, Mobile, Email -->
            <div class="row">
                <!-- Gender -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('gender'); ?><small class="req"> *</small></label>
                        <select class="form-control" name="gender">
                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                            <?php foreach ($genderList as $key => $value): ?>
                                <option value="<?php echo $key; ?>" <?php echo ($gender == $key) ? "selected" : ""; ?>>
                                    <?php echo $value; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span class="text-danger"><?php echo form_error('gender'); ?></span>
                    </div>
                </div>

                <!-- Date of Birth -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('date_of_birth'); ?><small class="req"> *</small></label>
                        <input type="text" class="form-control date2" id="dob" name="dob" value="<?php echo $dob; ?>" readonly="readonly" />
                        <span class="text-danger"><?php echo form_error('dob'); ?></span>
                    </div>
                </div>

                <!-- Mobile Number (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('mobile_no')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('mobile_number'); ?></label>
                            <input type="text" class="form-control" id="mobileno" name="mobileno" value="<?php echo set_value('mobileno', $mobileno); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('mobileno'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Email (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('student_email')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('email'); ?><small class="req"> *</small></label>
                            <input type="text" class="form-control" id="email" name="email" value="<?php echo set_value('email', $email); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('email'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div><!-- ./row -->

            <!-- Category, Religion, Caste, House -->
            <div class="row">
                <!-- Category (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('category')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('category'); ?></label>
                            <select id="category_id" name="category_id" class="form-control">
                                <option value=""><?php echo $this->lang->line('select'); ?></option>
                                <?php foreach ($categorylist as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo ($category_id == $category['id']) ? "selected" : ""; ?>>
                                        <?php echo $category['category']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Religion (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('religion')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('religion'); ?></label>
                            <input id="religion" name="religion" type="text" class="form-control" value="<?php echo set_value('religion', $religion); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('religion'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Caste (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('cast')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('caste'); ?></label>
                            <input id="cast" name="cast" type="text" class="form-control" value="<?php echo set_value('cast', $cast); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('cast'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- House (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('is_student_house')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('house'); ?></label>
                            <select class="form-control" name="house">
                                <option value=""><?php echo $this->lang->line('select'); ?></option>
                                <?php foreach ($houses as $hvalue): ?>
                                    <option value="<?php echo $hvalue['id']; ?>" <?php echo ($house_id == $hvalue['id']) ? "selected" : ""; ?>>
                                        <?php echo $hvalue['house_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="text-danger"><?php echo form_error('house'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div><!-- ./row -->

            <!-- Blood Group, Height, Weight, Measurement Date -->
            <div class="row">
                <!-- Blood Group (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('is_blood_group')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('blood_group'); ?></label>
                            <select class="form-control" name="blood_group">
                                <option value=""><?php echo $this->lang->line('select'); ?></option>
                                <?php foreach ($bloodgroup as $bgvalue): ?>
                                    <option value="<?php echo $bgvalue; ?>" <?php echo ($blood_group == $bgvalue) ? "selected" : ""; ?>>
                                        <?php echo $bgvalue; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span class="text-danger"><?php echo form_error('blood_group'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Height (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('student_height')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('height'); ?></label>
                            <input type="text" name="height" class="form-control" value="<?php echo set_value('height', $height); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('height'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Weight (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('student_weight')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('weight'); ?></label>
                            <input type="text" name="weight" class="form-control" value="<?php echo set_value('weight', $weight); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('weight'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Measurement Date (Conditional) -->
                <?php if ($this->customlib->getfieldstatus('measurement_date')): ?>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('measurement_date'); ?></label>
                            <input type="text" id="measure_date" name="measure_date" class="form-control date2" value="<?php echo set_value('measure_date', $measurement_date); ?>" />
                            <span class="text-danger"><?php echo form_error('measure_date'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div><!-- ./row -->

            <!-- Student Photo (Conditional) -->
            <?php if ($this->customlib->getfieldstatus('student_photo')): ?>
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="exampleInputFile"><?php echo $this->lang->line('student_photo'); ?></label>
                            <input class="filestyle form-control" type="file" name="file" id="file" size="20" />
                            <span class="text-danger"><?php echo form_error('file'); ?></span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Custom Fields -->
            <div class="row">
                <?php echo display_onlineadmission_custom_fields('students', $id); ?>
            </div>
        </div><!-- ./printcontent -->

        <!-- Parent Details Section -->
        <?php if ($this->customlib->getfieldstatus('father_name') || $this->customlib->getfieldstatus('father_phone') || $this->customlib->getfieldstatus('father_occupation') || $this->customlib->getfieldstatus('father_pic') || $this->customlib->getfieldstatus('mother_name') || $this->customlib->getfieldstatus('mother_phone') || $this->customlib->getfieldstatus('mother_occupation') || $this->customlib->getfieldstatus('mother_pic')): ?>
            <div class="printcontent card" style="padding:25px ;margin-bottom: 20px;">
                <div class="row">
                    <h4 class="pagetitleh2"><?php echo $this->lang->line('parent_detail'); ?></h4>
                    <!-- Father Name (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('father_name')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('father_name'); ?></label>
                                <input id="father_name" name="father_name" type="text" class="form-control" value="<?php echo set_value('father_name', $father_name); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('father_name'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Father Phone (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('father_phone')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('father_phone'); ?></label>
                                <input id="father_phone" name="father_phone" type="text" class="form-control" value="<?php echo set_value('father_phone', $father_phone); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('father_phone'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Father Occupation (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('father_occupation')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('father_occupation'); ?></label>
                                <input id="father_occupation" name="father_occupation" type="text" class="form-control" value="<?php echo set_value('father_occupation', $father_occupation); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('father_occupation'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Father Photo (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('father_pic')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="exampleInputFile"><?php echo $this->lang->line('father_photo'); ?></label>
                                <input class="filestyle form-control" type="file" name="father_pic" id="file" size="20" />
                                <span class="text-danger"><?php echo form_error('file'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->

                <!-- Mother Details -->
                <div class="row">
                    <!-- Mother Name (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('mother_name')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('mother_name'); ?></label>
                                <input id="mother_name" name="mother_name" type="text" class="form-control" value="<?php echo set_value('mother_name', $mother_name); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('mother_name'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Mother Phone (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('mother_phone')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('mother_phone'); ?></label>
                                <input id="mother_phone" name="mother_phone" type="text" class="form-control" value="<?php echo set_value('mother_phone', $mother_phone); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('mother_phone'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Mother Occupation (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('mother_occupation')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('mother_occupation'); ?></label>
                                <input id="mother_occupation" name="mother_occupation" type="text" class="form-control" value="<?php echo set_value('mother_occupation', $mother_occupation); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('mother_occupation'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Mother Photo (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('mother_pic')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="exampleInputFile"><?php echo $this->lang->line('mother_photo'); ?></label>
                                <input class="filestyle form-control" type="file" name="mother_pic" id="file" size="20" />
                                <span class="text-danger"><?php echo form_error('file'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->
            </div><!-- ./printcontent -->
        <?php endif; ?>

        <!-- Guardian Details Section -->
        <?php if ($this->customlib->getfieldstatus('if_guardian_is')): ?>
            <div class="printcontent card" style="padding:25px ;margin-bottom: 20px;">
                <div class="row">
                    <h4 class="pagetitleh2"><?php echo $this->lang->line('guardian_details'); ?></h4>
                    <!-- Guardian Is -->
                    <div class="form-group col-md-12">
                        <label><?php echo $this->lang->line('if_guardian_is'); ?><small class="req"> *</small>&nbsp;&nbsp;&nbsp;</label>
                        <label class="radio-inline">
                            <input type="radio" name="guardian_is" value="father" <?php echo ($guardian_is == "father") ? "checked" : ""; ?>> <?php echo $this->lang->line('father'); ?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="guardian_is" value="mother" <?php echo ($guardian_is == "mother") ? "checked" : ""; ?>> <?php echo $this->lang->line('mother'); ?>
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="guardian_is" value="other" <?php echo ($guardian_is == "other") ? "checked" : ""; ?>> <?php echo $this->lang->line('other'); ?>
                        </label>
                        <span class="text-danger"><?php echo form_error('guardian_is'); ?></span>
                    </div>

                    <!-- Guardian Name (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('guardian_name')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('guardian_name'); ?><small class="req"> *</small></label>
                                <input id="guardian_name" name="guardian_name" type="text" class="form-control" value="<?php echo set_value('guardian_name', $guardian_name); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('guardian_name'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Guardian Relation (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('guardian_relation')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('guardian_relation'); ?><small class="req"> *</small></label>
                                <input id="guardian_relation" name="guardian_relation" type="text" class="form-control" value="<?php echo set_value('guardian_relation', $guardian_relation); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('guardian_relation'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Guardian Email (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('guardian_email')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('guardian_email'); ?></label>
                                <input id="guardian_email" name="guardian_email" type="text" class="form-control" value="<?php echo set_value('guardian_email', $guardian_email); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('guardian_email'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Guardian Photo (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('guardian_photo')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="exampleInputFile"><?php echo $this->lang->line('guardian_photo'); ?></label>
                                <input class="filestyle form-control" type="file" name="guardian_pic" id="file" size="20" />
                                <span class="text-danger"><?php echo form_error('file'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->

                <!-- Guardian Phone, Occupation, Address -->
                <div class="row">
                    <!-- Guardian Phone -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('guardian_phone'); ?><small class="req"> *</small></label>
                            <input id="guardian_phone" name="guardian_phone" type="text" class="form-control" value="<?php echo set_value('guardian_phone', $guardian_phone); ?>" autocomplete="off" />
                            <span class="text-danger"><?php echo form_error('guardian_phone'); ?></span>
                        </div>
                    </div>

                    <!-- Guardian Occupation (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('guardian_occupation')): ?>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('guardian_occupation'); ?></label>
                                <input id="guardian_occupation" name="guardian_occupation" type="text" class="form-control" value="<?php echo set_value('guardian_occupation', $guardian_occupation); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('guardian_occupation'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Guardian Address (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('guardian_address')): ?>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('guardian_address'); ?></label>
                                <textarea id="guardian_address" name="guardian_address" class="form-control" rows="1" autocomplete="off"><?php echo set_value('guardian_address', $guardian_address); ?></textarea>
                                <span class="text-danger"><?php echo form_error('guardian_address'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->
            </div><!-- ./printcontent -->
        <?php endif; ?>

        <!-- Student Address Details Section -->
        <?php if ($this->customlib->getfieldstatus('current_address') || $this->customlib->getfieldstatus('permanent_address')): ?>
            <div class="printcontent card" style="padding:25px ;margin-bottom: 20px;">
                <div class="row">
                    <h4 class="pagetitleh2"><?php echo $this->lang->line('student_address_details'); ?></h4>
                    <!-- Current Address (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('current_address')): ?>
                        <div class="col-md-6">
                            <?php if ($this->customlib->getfieldstatus('guardian_address')): ?>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" id="autofill_current_address" onclick="return auto_fill_guardian_address();" autocomplete="off">
                                        <?php echo $this->lang->line('if_guardian_address_is_current_address'); ?>
                                    </label>
                                </div>
                            <?php else: ?>
                                <div class="checkbox"><label>&nbsp;</label></div>
                            <?php endif; ?>
                            <div class="form-group">
                                <label><?php echo $this->lang->line('current_address'); ?></label>
                                <textarea id="current_address" name="current_address" class="form-control" rows="1" autocomplete="off"><?php echo set_value('current_address', $current_address); ?></textarea>
                                <span class="text-danger"><?php echo form_error('current_address'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Permanent Address (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('permanent_address')): ?>
                        <div class="col-md-6">
                            <?php if ($this->customlib->getfieldstatus('current_address')): ?>
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" id="autofill_address" onclick="return auto_fill_address();">
                                        <?php echo $this->lang->line('if_permanent_address_is_current_address'); ?>
                                    </label>
                                </div>
                            <?php else: ?>
                                <div class="checkbox"><label>&nbsp;</label></div>
                            <?php endif; ?>
                            <div class="form-group">
                                <label><?php echo $this->lang->line('permanent_address'); ?></label>
                                <textarea id="permanent_address" name="permanent_address" class="form-control" rows="1" autocomplete="off"><?php echo set_value('permanent_address', $permanent_address); ?></textarea>
                                <span class="text-danger"><?php echo form_error('permanent_address'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->
            </div><!-- ./printcontent -->
        <?php endif; ?>

        <!-- Miscellaneous Details Section -->
        <?php if ($this->customlib->getfieldstatus('bank_account_no') || $this->customlib->getfieldstatus('bank_name') || $this->customlib->getfieldstatus('ifsc_code') || $this->customlib->getfieldstatus('national_identification_no') || $this->customlib->getfieldstatus('local_identification_no') || $this->customlib->getfieldstatus('rte') || $this->customlib->getfieldstatus('previous_school_details') || $this->customlib->getfieldstatus('student_note')): ?>
            <div class="printcontent card" style="padding:25px ;margin-bottom: 20px;">
                <div class="row">
                    <h4 class="pagetitleh2"><?php echo $this->lang->line('miscellaneous_details'); ?></h4>
                    <!-- Bank Account Number (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('bank_account_no')): ?>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('bank_account_number'); ?></label>
                                <input id="bank_account_no" name="bank_account_no" type="text" class="form-control" value="<?php echo $bank_account_no; ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('bank_account_no'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Bank Name (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('bank_name')): ?>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('bank_name'); ?></label>
                                <input id="bank_name" name="bank_name" type="text" class="form-control" value="<?php echo set_value('bank_name', $bank_name); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('bank_name'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- IFSC Code (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('ifsc_code')): ?>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('ifsc_code'); ?></label>
                                <input id="ifsc_code" name="ifsc_code" type="text" class="form-control" value="<?php echo set_value('ifsc_code', $ifsc_code); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('ifsc_code'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->

                <!-- National ID, Local ID, RTE -->
                <div class="row">
                    <!-- National Identification Number (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('national_identification_no')): ?>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('national_identification_number'); ?></label>
                                <input id="adhar_no" name="adhar_no" type="text" class="form-control" value="<?php echo set_value('adhar_no', $adhar_no); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('adhar_no'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Local Identification Number (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('local_identification_no')): ?>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('local_identification_number'); ?></label>
                                <input id="samagra_id" name="samagra_id" type="text" class="form-control" value="<?php echo set_value('samagra_id', $samagra_id); ?>" autocomplete="off" />
                                <span class="text-danger"><?php echo form_error('samagra_id'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- RTE (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('rte')): ?>
                        <div class="col-md-4">
                            <label><?php echo $this->lang->line('rte'); ?></label>
                            <div class="radio" style="margin-top: 2px;">
                                <label>
                                    <input class="radio-inline" type="radio" name="rte" value="Yes" <?php echo set_value('rte', $rte) == "Yes" ? "checked" : ""; ?>>
                                    <?php echo $this->lang->line('yes'); ?>
                                </label>
                                <label>
                                    <input class="radio-inline" type="radio" name="rte" value="No" <?php echo set_value('rte', $rte) == "No" ? "checked" : ""; ?>>
                                    <?php echo $this->lang->line('no'); ?>
                                </label>
                            </div>
                            <span class="text-danger"><?php echo form_error('rte'); ?></span>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->

                <!-- Previous School Details, Student Note -->
                <div class="row">
                    <!-- Previous School Details (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('previous_school_details')): ?>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('previous_school_details'); ?></label>
                                <textarea class="form-control" rows="1" name="previous_school" autocomplete="off"><?php echo set_value('previous_school', $previous_school); ?></textarea>
                                <span class="text-danger"><?php echo form_error('previous_school'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Student Note (Conditional) -->
                    <?php if ($this->customlib->getfieldstatus('student_note')): ?>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?php echo $this->lang->line('note'); ?></label>
                                <textarea class="form-control" rows="1" name="note" autocomplete="off"><?php echo set_value('note', $note); ?></textarea>
                                <span class="text-danger"><?php echo form_error('note'); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div><!-- ./row -->
            </div><!-- ./printcontent -->
        <?php endif; ?>

        <!-- Upload Documents Section -->
        <?php if ($this->customlib->getfieldstatus('upload_documents')): ?>
            <div class="printcontent card" style="padding:25px ;margin-bottom: 20px;">
                <div class="row">
                    <h4 class="pagetitleh2"><?php echo $this->lang->line('upload_documents'); ?></h4>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('upload_documents'); ?></label>
                            <input id="document" name="document" type="file" class="form-control filestyle" value="<?php echo set_value('document'); ?>" />
                            <span class="text-danger"><?php echo form_error('document'); ?></span>
                        </div>
                    </div>
                </div>
            </div><!-- ./printcontent -->
        <?php endif; ?>

        <!-- Submit Button -->
        <div class="form-group pull-right">
            <input type="hidden" id="admission_id" name="admission_id" value="<?php echo $id; ?>">
            <button type="submit" class="onlineformbtn btn btn-md btn-success"><?php echo $this->lang->line('edit_and_save'); ?></button>
        </div>
    </form>
</div>

<script type="text/javascript">
 var date_format = '<?php echo $result = strtr($this->customlib->getSchoolDateFormat(), ['d' => 'dd', 'm' => 'mm', 'Y' => 'yyyy']) ?>';

 $(document).ready(function () {

    var class_id = $('#class_id').val();
    var section_id = '<?php echo set_value('section_id', 0) ?>';

    getSectionByClass(class_id, section_id);

    $(document).on('change', '#class_id', function (e) {
        $('#section_id').html("");
        var class_id = $(this).val();
        getSectionByClass(class_id, 0);
    });

    $('.date2').datepicker({
        autoclose: true,
        format: date_format,
        todayHighlight: true
    });

    function getSectionByClass(class_id, section_id) {

        if (class_id !== "") {
            $('#section_id').html("");

            var div_data = '';
            var url = "";

            $.ajax({
                type: "POST",
                url: base_url + "welcome/getSections",
                data: {'class_id': class_id},
                dataType: "json",
                beforeSend: function () {
                    $('#section_id').addClass('dropdownloading');
                },
                success: function (data) {
                    $.each(data, function (i, obj)
                    {
                        var sel = "";
                        if (section_id === obj.section_id) {
                            sel = "selected";
                        }
                        div_data += "<option value=" + obj.id + " " + sel + ">" + obj.section + "</option>";
                    });
                    $('#section_id').append(div_data);
                },
                complete: function () {
                    $('#section_id').removeClass('dropdownloading');
                }
            });
        }
    }
});

 function auto_fill_guardian_address() {
    if ($("#autofill_current_address").is(':checked'))
    {
        $('#current_address').val($('#guardian_address').val());
    }
}

function auto_fill_address() {
    if ($("#autofill_address").is(':checked'))
    {
        $('#permanent_address').val($('#current_address').val());
    }
}

$('input:radio[name="guardian_is"]').change(
    function () {
        if ($(this).is(':checked')) {
            var value = $(this).val();
            if (value === "father") {
                $('#guardian_name').val($('#father_name').val());
                $('#guardian_phone').val($('#father_phone').val());
                $('#guardian_occupation').val($('#father_occupation').val());
                $('#guardian_relation').val("Father");
            } else if (value === "mother") {
                $('#guardian_name').val($('#mother_name').val());
                $('#guardian_phone').val($('#mother_phone').val());
                $('#guardian_occupation').val($('#mother_occupation').val());
                $('#guardian_relation').val("Mother");
            } else {
                $('#guardian_name').val("");
                $('#guardian_phone').val("");
                $('#guardian_occupation').val("");
                $('#guardian_relation').val("");
            }
        }
    });
</script>

<script type="text/javascript">
    function refreshCaptcha(){
        $.ajax({
            type: "POST",
            url: "<?php echo base_url('site/refreshCaptcha'); ?>",
            data: {},
            success: function(captcha){
                $("#captcha_image").html(captcha);
            }
        });
    }
</script>

<script type="text/javascript">
    $(document).ready(function(){
        $(document).on('submit','#checkstatusform',function(e){
   e.preventDefault(); // avoid to execute the actual submit of the form.
   var form = $(this);
   var url = form.attr('action');
   var form_data = form.serializeArray();

   $.ajax({
     url: url,
     type: "POST",
     dataType:'JSON',
           data: form_data, // serializes the form's elements.
           beforeSend: function () {

           },
              success: function(response) { // your success handler
                if(response.status==0){
                    $.each(response.error, function(key, value) {
                        $('#error_' + key).html(value);
                    });
                }else if(response.status==2){
                    $('#error_dob' ).html("");
                    $('#error_refno' ).html("");
                    $('#invaliderror').html(response.error);
                } else{
                    var admission_id= response.id;
                    window.location.href="<?php echo base_url() . 'welcome/online_admission_review/' ?>"+admission_id ;
                }
            },
             error: function() { // your error handler

             },
             complete: function() {

             }
         });
});
    });
</script>

<script>
    function openmodal(){
      $('#error_dob' ).html("");
      $('#error_refno' ).html("");
      $('#invaliderror').html("");
      $('#student_dob').val("");
      $('#student_dob').html("");
      $('#refno' ).val("");
      $(':input').val('');
  }

  function auto_fill_guardian_address() {
    if ($("#autofill_current_address").is(':checked'))
    {
        $('#current_address').val($('#guardian_address').val());
    }
}

function auto_fill_address() {
    if ($("#autofill_address").is(':checked'))
    {
        $('#permanent_address').val($('#current_address').val());
    }
}
</script>