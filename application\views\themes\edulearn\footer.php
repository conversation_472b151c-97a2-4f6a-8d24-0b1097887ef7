<!-- Footer Start -->
<footer id="rs-footer" class="rs-footer">
    <div class="footer-top">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-12 col-sm-12 footer-widget">
                    <div class="about-widget">
                        <div class="logo-footer">
                            <a href="<?php echo base_url(); ?>">
                                <?php if (!empty($school_setting->logo)) { ?>
                                    <img src="<?php echo base_url('uploads/school_content/logo/' . $school_setting->logo); ?>" alt="<?php echo $school_setting->name; ?>">
                                <?php } else { ?>
                                    <img src="<?php echo base_url('backend/themes/edulearn/images/logo-footer.png'); ?>" alt="<?php echo $school_setting->name; ?>">
                                <?php } ?>
                            </a>
                        </div>
                        <div class="des">
                           
                        </div>
                        <ul class="footer-social">
                            <?php if (!empty($front_setting->fb_url)) { ?>
                                <li><a href="<?php echo $front_setting->fb_url; ?>" target="_blank"><i class="fa fa-facebook"></i></a></li>
                            <?php } ?>
                            <?php if (!empty($front_setting->twitter_url)) { ?>
                                <li><a href="<?php echo $front_setting->twitter_url; ?>" target="_blank"><i class="fa fa-twitter"></i></a></li>
                            <?php } ?>
                            <?php if (!empty($front_setting->google_plus)) { ?>
                                <li><a href="<?php echo $front_setting->google_plus; ?>" target="_blank"><i class="fa fa-google-plus"></i></a></li>
                            <?php } ?>
                            <?php if (!empty($front_setting->linkedin_url)) { ?>
                                <li><a href="<?php echo $front_setting->linkedin_url; ?>" target="_blank"><i class="fa fa-linkedin"></i></a></li>
                            <?php } ?>
                            <?php if (!empty($front_setting->youtube_url)) { ?>
                                <li><a href="<?php echo $front_setting->youtube_url; ?>" target="_blank"><i class="fa fa-youtube"></i></a></li>
                            <?php } ?>
                            <?php if (!empty($front_setting->instagram_url)) { ?>
                                <li><a href="<?php echo $front_setting->instagram_url; ?>" target="_blank"><i class="fa fa-instagram"></i></a></li>
                            <?php } ?>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3 col-md-12 col-sm-12 footer-widget">
                    <h4 class="widget-title">Quick Links</h4>
                    <ul class="recent-posts">
                        <?php
                        if (!empty($main_menus)) {
                            $count = 0;
                            foreach ($main_menus as $main_menu) {
                                if ($count >= 6) break; // Limit to 6 links
                                
                                if ($main_menu['open_new_tab']) {
                                    $target = "target='_blank'";
                                } else {
                                    $target = "";
                                }

                                if (!empty($main_menu['ext_url'])) {
                                    $link = $main_menu['ext_url_link'];
                                } else {
                                    $link = base_url(isset($main_menu['page_slug']) ? $main_menu['page_slug'] : '');
                                }
                                ?>
                                <li><a href="<?php echo $link; ?>" <?php echo $target; ?>><?php echo $main_menu['menu']; ?></a></li>
                                <?php
                                $count++;
                            }
                        }
                        ?>
                        <li><a href="<?php echo base_url('online_admission'); ?>">Online Admission</a></li>
                        <li><a href="<?php echo base_url('site/userlogin'); ?>">Student Portal</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-12 col-sm-12 footer-widget">
                    <h4 class="widget-title">Contact Info</h4>
                    <ul class="address-widget">
                        <li>
                            <i class="flaticon-location"></i>
                            <div class="desc"><?php echo $school_setting->address; ?></div>
                        </li>
                        <li>
                            <i class="flaticon-call"></i>
                            <div class="desc">
                                <a href="tel:<?php echo $school_setting->phone; ?>"><?php echo $school_setting->phone; ?></a>
                            </div>
                        </li>
                        <li>
                            <i class="flaticon-email"></i>
                            <div class="desc">
                                <a href="mailto:<?php echo $school_setting->email; ?>"><?php echo $school_setting->email; ?></a>
                            </div>
                        </li>
                        <?php if (!empty($school_setting->website)) { ?>
                        <li>
                            <i class="flaticon-web"></i>
                            <div class="desc">
                                <a href="<?php echo $school_setting->website; ?>" target="_blank"><?php echo $school_setting->website; ?></a>
                            </div>
                        </li>
                        <?php } ?>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-12 col-sm-12 footer-widget">
                    <h4 class="widget-title">School Hours</h4>
                    <ul class="opening-hours">
                        <li>
                            <span class="days">Monday - Friday</span>
                            <span class="time">8:00 AM - 3:00 PM</span>
                        </li>
                        <li>
                            <span class="days">Saturday</span>
                            <span class="time">8:00 AM - 12:00 PM</span>
                        </li>
                        <li>
                            <span class="days">Sunday</span>
                            <span class="time">Closed</span>
                        </li>
                    </ul>
                    
                    <!-- Newsletter Signup -->
                    <div class="newsletter-widget">
                        <h5>Newsletter</h5>
                        <p>Subscribe to get updates</p>
                        <form class="newsletter-form" method="post" action="<?php echo base_url('welcome/newsletter'); ?>">
                            <input type="email" name="email" placeholder="Enter your email" required>
                            <button type="submit"><i class="fa fa-paper-plane"></i></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="footer-bottom">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 col-md-8 col-sm-8">
                    <div class="copyright">
                        <p>&copy; <?php echo date('Y'); ?> <?php echo $school_setting->name; ?>. All Rights Reserved.</p>
                    </div>
                </div>
                <div class="col-lg-6 col-md-4 col-sm-4">
                    <div class="footer-right">
                        <ul>
                            <li><a href="<?php echo base_url('page/privacy-policy'); ?>">Privacy Policy</a></li>
                            <li><a href="<?php echo base_url('page/terms-conditions'); ?>">Terms & Conditions</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<!-- Footer End -->

<!-- start scrollUp  -->
<div id="scrollUp">
    <i class="fa fa-angle-up"></i>
</div>
<!-- End scrollUp  -->
