<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-map-marker"></i> <?php echo $this->lang->line('live_tracking'); ?>
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('live_tracking'); ?> - <?php echo $vehicle_details['vehicle_no']; ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('vehicle_number'); ?></label>
                                    <p><?php echo $vehicle_details['vehicle_no']; ?></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('vehicle_model'); ?></label>
                                    <p><?php echo $vehicle_details['vehicle_model']; ?></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('driver_name'); ?></label>
                                    <p><?php echo $vehicle_details['driver_name']; ?></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('driver_contact'); ?></label>
                                    <p><?php echo $vehicle_details['driver_contact']; ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div id="map" style="height: 500px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
<script>
    // Firebase configuration
    const firebaseConfig = {
        apiKey: "YOUR_FIREBASE_API_KEY",
        authDomain: "YOUR_FIREBASE_AUTH_DOMAIN",
        databaseURL: "YOUR_FIREBASE_DATABASE_URL",
        projectId: "YOUR_FIREBASE_PROJECT_ID",
        storageBucket: "YOUR_FIREBASE_STORAGE_BUCKET",
        messagingSenderId: "YOUR_FIREBASE_MESSAGING_SENDER_ID",
        appId: "YOUR_FIREBASE_APP_ID"
    };
    
    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
    const database = firebase.database();
    
    let map;
    let marker;
    const vehicleId = "<?php echo $vehicle_details['id']; ?>";
    
    function initMap() {
        // Default location (school location)
        const defaultLocation = { lat: 0, lng: 0 }; // Set your school's coordinates
        
        map = new google.maps.Map(document.getElementById("map"), {
            zoom: 15,
            center: defaultLocation,
        });
        
        marker = new google.maps.Marker({
            position: defaultLocation,
            map: map,
            title: "<?php echo $vehicle_details['vehicle_no']; ?>",
            icon: {
                url: "<?php echo base_url('uploads/bus_icon.png'); ?>",
                scaledSize: new google.maps.Size(50, 50)
            }
        });
        
        // Listen for location updates from Firebase
        const locationRef = database.ref('vehicle_locations/' + vehicleId);
        locationRef.on('value', (snapshot) => {
            const data = snapshot.val();
            if (data) {
                const position = {
                    lat: data.latitude,
                    lng: data.longitude
                };
                
                marker.setPosition(position);
                map.setCenter(position);
                
                // Update additional info if needed
                $('#last_updated').text(new Date(data.timestamp).toLocaleString());
                $('#vehicle_speed').text(data.speed + ' km/h');
            }
        });
    }
</script>