<div class="content-wrapper">
    <section class="content-header">
        <h1>
            <i class="fa fa-map-marker"></i> <?php echo $this->lang->line('live_tracking'); ?>
        </h1>
    </section>
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('live_tracking'); ?> - <?php echo $vehicle_details['vehicle_no']; ?></h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('vehicle_number'); ?></label>
                                    <p><?php echo $vehicle_details['vehicle_no']; ?></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('vehicle_model'); ?></label>
                                    <p><?php echo $vehicle_details['vehicle_model']; ?></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('driver_name'); ?></label>
                                    <p><?php echo $vehicle_details['driver_name']; ?></p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><?php echo $this->lang->line('driver_contact'); ?></label>
                                    <p><?php echo $vehicle_details['driver_contact']; ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div id="map" style="height: 500px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
<script>
    // Firebase configuration from database
    <?php if (!empty($firebase_config)) { ?>
    const firebaseConfig = {
        apiKey: "<?php echo $firebase_config['apiKey']; ?>",
        authDomain: "<?php echo $firebase_config['authDomain']; ?>",
        databaseURL: "<?php echo $firebase_config['databaseURL']; ?>",
        projectId: "<?php echo $firebase_config['projectId']; ?>",
        storageBucket: "<?php echo $firebase_config['storageBucket']; ?>",
        messagingSenderId: "<?php echo $firebase_config['messagingSenderId']; ?>",
        appId: "<?php echo $firebase_config['appId']; ?>"
    };
    <?php } else { ?>
    // Firebase configuration not found
    console.error('Firebase configuration not found for this school');
    const firebaseConfig = null;
    <?php } ?>

    // Initialize Firebase
    let database = null;
    if (firebaseConfig && !firebase.apps.length) {
        firebase.initializeApp(firebaseConfig);
        database = firebase.database();
    } else if (firebaseConfig) {
        database = firebase.database();
    }
    
    let map;
    let marker;
    const vehicleId = "<?php echo $vehicle_details['id']; ?>";
    
    function initMap() {
        // Default location (school location)
        const defaultLocation = { lat: 0, lng: 0 }; // Set your school's coordinates
        
        map = new google.maps.Map(document.getElementById("map"), {
            zoom: 15,
            center: defaultLocation,
        });
        
        marker = new google.maps.Marker({
            position: defaultLocation,
            map: map,
            title: "<?php echo $vehicle_details['vehicle_no']; ?>",
            icon: {
                url: "<?php echo base_url('uploads/bus_icon.png'); ?>",
                scaledSize: new google.maps.Size(50, 50)
            }
        });
        
        // Listen for location updates from Firebase
        if (database) {
            const locationRef = database.ref('vehicle_locations/' + vehicleId);
            locationRef.on('value', (snapshot) => {
                const data = snapshot.val();
                if (data) {
                    const position = {
                        lat: data.latitude,
                        lng: data.longitude
                    };

                    marker.setPosition(position);
                    map.setCenter(position);

                    // Update additional info if needed
                    $('#last_updated').text(new Date(data.timestamp).toLocaleString());
                    $('#vehicle_speed').text(data.speed + ' km/h');
                }
            });
        } else {
            console.warn('Firebase not configured. Live tracking is not available.');
            // Show a message to the user
            $('#live_tracking_status').html('<div class="alert alert-warning"><i class="fa fa-warning"></i> Firebase not configured. Live tracking is not available.</div>');
        }
    }
</script>