<!DOCTYPE html>
<html dir="">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo $page['title']; ?></title>
    <meta name="title" content="<?php if (isset($page['meta_title'])) {echo $page['meta_title'];}?>">
    <meta name="keywords" content="<?php if (isset($page['meta_keyword'])) {echo $page['meta_keyword'];}?>">
    <meta name="description" content="<?php if (isset($page['meta_description'])) {echo $page['meta_description'];}?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="<?php echo base_url($front_setting->fav_icon); ?>" type="image/x-icon">
    <!-- ======== Page title ============ -->
    <title></title>
    <!--<< Favcion >>-->
    <link rel="shortcut icon" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/img/favicon.svg">
    <!--<< Bootstrap min.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/bootstrap.min.css">
    <!--<< All Min Css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/all.min.css">
    <!--<< Animate.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/animate.css">
    <!--<< Icomoon.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/icomoon.css">
    <!--<< Magnific Popup.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/magnific-popup.css">
    <!--<< MeanMenu.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/meanmenu.css">
    <!--<< Swiper Bundle.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/swiper-bundle.min.css">
    <!--<< Nice Select.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/nice-select.css">
    <!--<< Main.css >>-->
    <link rel="stylesheet" href="<?php echo base_url('backend/themes/kidsa/') ?>assets/css/main.css"> 

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> 

    <script type="text/javascript">
        var base_url = "<?php echo base_url() ?>";
    </script> 
    <style> 
        <?php 
        $bgcolor = '#025da0';
        $bgcolor = '#025da0';
        $headercolor = '#fff';;
        $custom_css = $this->db->get_where('frontend_cssjs', array('school_id' => $this->school_id)); 
        if($custom_css->num_rows() == 1 ){
            $r = $custom_css->row_array(); 

            //echo $r['custom_css'];
        }
        ?>

        :root {
          --body: #fff;
          --black: #000;
          --white: #fff;
          --theme: #F39F5F;
          --theme2: #70A6B1;
          --header: <?php echo $headercolor ?>;;
          --text: #5C707E;
          --text-2: #ffffffcc;
          --border: #E5E5E5;
          --border2: #242449;
          --border3: #5262FF;
          --bg: <?php echo $bgcolor ?>;
          --bg2: #bebebe;
          --bg3: #70A6B1;
          --box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.06);
        }
    </style>
    <?php if ($front_setting->is_active_rtl) { ?>
        <link href="<?php echo $base_assets_url; ?>rtl/bootstrap-rtl.min.css" rel="stylesheet">
        <link href="<?php echo $base_assets_url; ?>rtl/style-rtl.css" rel="stylesheet">
    <?php } ?>

    <?php if ($this->module_lib->hasModule('online_course') && $this->module_lib->hasActive('online_course')) { ?>
        <link rel="stylesheet" href="<?php echo $base_assets_url; ?>css/online-course.css"/>        
    <?php } ?>

</head>
<body>


        <?php echo $header;  ?>  

        
        <div class="">
            <?php echo $content; ?>
        </div>  
        
        <?php echo $footer; ?>

        <!--<< All JS Plugins >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/jquery-3.7.1.min.js"></script>
        <!--<< Viewport Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/viewport.jquery.js"></script>
        <!--<< Bootstrap Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/bootstrap.bundle.min.js"></script>
        <!--<< Waypoints Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/jquery.waypoints.js"></script>
        <!--<< Counterup Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/jquery.counterup.min.js"></script>
        <!--<< Swiper Slider Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/swiper-bundle.min.js"></script>
        <!--<< MeanMenu Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/jquery.meanmenu.min.js"></script>
        <!--<< Magnific Popup Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/jquery.magnific-popup.min.js"></script>
        <!--<< Wow Animation Js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/wow.min.js"></script>
        <!-- contact form Js -->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/contact-from.js"></script>
        <!--<< Main.js >>-->
        <script src="<?php echo base_url('backend/themes/kidsa/') ?>assets/js/main.js"></script> 

        <script type="text/javascript">
            $(function () {
                jQuery('img.svg').each(function () {
                    var $img = jQuery(this);
                    var imgID = $img.attr('id');
                    var imgClass = $img.attr('class');
                    var imgURL = $img.attr('src');

                    jQuery.get(imgURL, function (data) {
                                                    // Get the SVG tag, ignore the rest
                        var $svg = jQuery(data).find('svg');

                                                    // Add replaced image's ID to the new SVG
                        if (typeof imgID !== 'undefined') {
                            $svg = $svg.attr('id', imgID);
                        }
                                                    // Add replaced image's classes to the new SVG
                        if (typeof imgClass !== 'undefined') {
                            $svg = $svg.attr('class', imgClass + ' replaced-svg');
                        }

                                                    // Remove any invalid XML tags as per http://validator.w3.org
                        $svg = $svg.removeAttr('xmlns:a');

                                                    // Check if the viewport is set, else we gonna set it if we can.
                        if (!$svg.attr('viewBox') && $svg.attr('height') && $svg.attr('width')) {
                            $svg.attr('viewBox', '0 0 ' + $svg.attr('height') + ' ' + $svg.attr('width'))
                        }

                                                    // Replace image with new SVG
                        $img.replaceWith($svg);

                    }, 'xml');

                });
            }); 

        </script>
    </body>
    </html>