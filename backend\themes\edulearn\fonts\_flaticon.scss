    /*
    Flaticon icon font: Flaticon
    Creation date: 02/02/2018 08:55
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-tool-2:before { content: "\f100"; }
.flaticon-ribbon:before { content: "\f101"; }
.flaticon-school-1:before { content: "\f102"; }
.flaticon-graduation:before { content: "\f103"; }
.flaticon-school:before { content: "\f104"; }
.flaticon-document:before { content: "\f105"; }
.flaticon-book-1:before { content: "\f106"; }
.flaticon-tool-1:before { content: "\f107"; }
.flaticon-tool:before { content: "\f108"; }
.flaticon-book:before { content: "\f109"; }
.flaticon-people:before { content: "\f10a"; }
.flaticon-diploma:before { content: "\f10b"; }
    
    $font-Flaticon-tool-2: "\f100";
    $font-Flaticon-ribbon: "\f101";
    $font-Flaticon-school-1: "\f102";
    $font-Flaticon-graduation: "\f103";
    $font-Flaticon-school: "\f104";
    $font-Flaticon-document: "\f105";
    $font-Flaticon-book-1: "\f106";
    $font-Flaticon-tool-1: "\f107";
    $font-Flaticon-tool: "\f108";
    $font-Flaticon-book: "\f109";
    $font-Flaticon-people: "\f10a";
    $font-Flaticon-diploma: "\f10b";