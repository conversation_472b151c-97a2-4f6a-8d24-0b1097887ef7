<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Edulearn extends Admin_Controller
{

    function __construct()
    {
        parent::__construct();
        $this->load->model('page_content_model');
        $this->load->library('upload');
    }

    public function index()
    {
        if (!$this->rbac->hasPrivilege('page_content', 'can_view')) {
            access_denied();
        }
        
        $this->session->set_userdata('top_menu', 'Front CMS');
        $this->session->set_userdata('sub_menu', 'admin/front/edulearn');
        
        $data = array();
        $data['title'] = 'Edulearn Theme Management';
        $data['title_list'] = 'Manage Homepage Content';
        
        // Get current page content for this school
        $data['page_content'] = $this->page_content_model->getPageContentBySections($this->school_id, 'home');
        
        // Get default content for reference
        $data['default_content'] = $this->page_content_model->getPageContentBySections(0, 'home');
        
        // Define all sections for the edulearn homepage
        $data['sections'] = $this->getHomepageSections();
        
        $this->load->view('layout/header');
        $this->load->view('admin/front/edulearn/index', $data);
        $this->load->view('layout/footer');
    }

    public function edit()
    {
        if (!$this->rbac->hasPrivilege('page_content', 'can_edit')) {
            access_denied();
        }
        
        $this->session->set_userdata('top_menu', 'Front CMS');
        $this->session->set_userdata('sub_menu', 'admin/front/edulearn');
        
        $section_name = $this->input->get('section');
        
        if (!$section_name) {
            $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Section not specified.</div>');
            redirect('admin/front/edulearn');
        }
        
        $data = array();
        $data['title'] = 'Edit Homepage Content';
        $data['title_list'] = 'Edit Content Section';
        $data['section_name'] = $section_name;
        
        // Get current content for editing
        $school_specific_content = $this->page_content_model->getSchoolSpecificContent($this->school_id, 'home', $section_name);
        $default_content = $this->page_content_model->getSectionContent(0, 'home', $section_name);

        // For form loading: use school-specific if exists, otherwise use default
        $form_content = $school_specific_content ? $school_specific_content : $default_content;
        
        $data['content'] = $form_content;
        $data['sections'] = $this->getHomepageSections();
        $data['section_info'] = isset($data['sections'][$section_name]) ? $data['sections'][$section_name] : array();
        
        if ($this->input->post()) {
            $result = $this->updateContent();
            
            if ($result) {
                $this->session->set_flashdata('msg', '<div class="alert alert-success text-left">Content updated successfully.</div>');
            } else {
                $this->session->set_flashdata('msg', '<div class="alert alert-danger text-left">Failed to update content.</div>');
            }
            
            redirect('admin/front/edulearn');
        }
        
        $this->load->view('layout/header');
        $this->load->view('admin/front/edulearn/edit', $data);
        $this->load->view('layout/footer');
    }

    private function updateContent()
    {
        $section_name = $this->input->post('section_name');
        $content_title = $this->input->post('content_title');
        $content_text = $this->input->post('content_text');
        
        // Handle image upload if present
        $content_image = '';
        if (!empty($_FILES['content_image']['name'])) {
            $content_image = $this->handleImageUpload();
        }
        
        $data = array(
            'school_id' => $this->school_id,
            'page_slug' => 'home',
            'section_name' => $section_name,
            'content_title' => $content_title,
            'content_text' => $content_text,
            'status' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        if ($content_image) {
            $data['content_image'] = $content_image;
        }
        
        // Check if school-specific content already exists for this school
        $existing = $this->page_content_model->getSchoolSpecificContent($this->school_id, 'home', $section_name);

        if ($existing) {
            // Update existing school-specific content
            $data['id'] = $existing['id'];
            $result = $this->page_content_model->update($data);
        } else {
            // Insert new school-specific content
            $data['created_at'] = date('Y-m-d H:i:s');
            $result = $this->page_content_model->insert($data);
        }
        
        return $result;
    }

    private function handleImageUpload()
    {
        $config['upload_path'] = './uploads/school_content/edulearn/';
        $config['allowed_types'] = 'gif|jpg|png|jpeg';
        $config['max_size'] = 2048; // 2MB
        $config['encrypt_name'] = TRUE;
        
        // Create directory if it doesn't exist
        if (!is_dir($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }
        
        $this->upload->initialize($config);
        
        if ($this->upload->do_upload('content_image')) {
            $upload_data = $this->upload->data();
            return $upload_data['file_name'];
        }
        
        return '';
    }

    public function reset_to_default()
    {
        if (!$this->rbac->hasPrivilege('page_content', 'can_edit')) {
            access_denied();
        }
        
        $section_name = $this->input->post('section_name');
        
        // Delete school-specific content to fall back to default
        $this->page_content_model->deleteSchoolContent($this->school_id, 'home', $section_name);
        
        $this->session->set_flashdata('msg', '<div class="alert alert-success text-left">Content reset to default successfully.</div>');
        redirect('admin/front/edulearn');
    }

    public function save_content_ajax()
    {
        if (!$this->rbac->hasPrivilege('page_content', 'can_edit')) {
            echo json_encode(array('status' => 0, 'message' => 'Access denied'));
            return;
        }
        
        if ($this->input->is_ajax_request()) {
            $result = $this->updateContent();
            
            if ($result) {
                echo json_encode(array(
                    'status' => 1, 
                    'message' => 'Content updated successfully.'
                ));
            } else {
                echo json_encode(array(
                    'status' => 0, 
                    'message' => 'Failed to update content.'
                ));
            }
        } else {
            echo json_encode(array(
                'status' => 0, 
                'message' => 'Invalid request method.'
            ));
        }
    }

    public function toggle_status()
    {
        if (!$this->rbac->hasPrivilege('page_content', 'can_edit')) {
            access_denied();
        }

        if ($this->input->post()) {
            $section_name = $this->input->post('section_name');
            $status = $this->input->post('status');

            if ($section_name && isset($status)) {
                // Get current content or create new entry
                $current_content = $this->page_content_model->getSectionContent($this->school_id, 'home', $section_name);

                if ($current_content && $current_content['school_id'] == $this->school_id) {
                    // Update existing school-specific content
                    $data = array(
                        'id' => $current_content['id'],
                        'status' => $status ? 1 : 0
                    );
                    $result = $this->page_content_model->update($data);
                } else {
                    // Create new school-specific content with status
                    $default_content = $this->page_content_model->getSectionContent(0, 'home', $section_name);
                    if ($default_content) {
                        $data = array(
                            'school_id' => $this->school_id,
                            'page_slug' => 'home',
                            'section_name' => $section_name,
                            'content_title' => $default_content['content_title'],
                            'content_text' => $default_content['content_text'],
                            'content_image' => $default_content['content_image'],
                            'status' => $status ? 1 : 0,
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        $result = $this->page_content_model->insert($data);
                    }
                }

                if ($result) {
                    echo json_encode(array('status' => 1, 'message' => 'Status updated successfully.'));
                } else {
                    echo json_encode(array('status' => 0, 'message' => 'Failed to update status.'));
                }
            } else {
                echo json_encode(array('status' => 0, 'message' => 'Invalid parameters.'));
            }
        } else {
            echo json_encode(array('status' => 0, 'message' => 'Invalid request method.'));
        }
    }

    private function getHomepageSections()
    {
        return array(
            // Slider sections
            'slider_1_title' => array('name' => 'Slider 1 Title', 'type' => 'text', 'group' => 'Hero Slider'),
            'slider_1_content' => array('name' => 'Slider 1 Content', 'type' => 'textarea', 'group' => 'Hero Slider'),
            'slider_2_title' => array('name' => 'Slider 2 Title', 'type' => 'text', 'group' => 'Hero Slider'),
            'slider_2_content' => array('name' => 'Slider 2 Content', 'type' => 'textarea', 'group' => 'Hero Slider'),
            'slider_3_title' => array('name' => 'Slider 3 Title', 'type' => 'text', 'group' => 'Hero Slider'),
            'slider_3_content' => array('name' => 'Slider 3 Content', 'type' => 'textarea', 'group' => 'Hero Slider'),
            
            // Services sections
            'service_1_title' => array('name' => 'Service 1 Title', 'type' => 'text', 'group' => 'Services'),
            'service_1_content' => array('name' => 'Service 1 Content', 'type' => 'textarea', 'group' => 'Services'),
            'service_2_title' => array('name' => 'Service 2 Title', 'type' => 'text', 'group' => 'Services'),
            'service_2_content' => array('name' => 'Service 2 Content', 'type' => 'textarea', 'group' => 'Services'),
            'service_3_title' => array('name' => 'Service 3 Title', 'type' => 'text', 'group' => 'Services'),
            'service_3_content' => array('name' => 'Service 3 Content', 'type' => 'textarea', 'group' => 'Services'),
            'service_4_title' => array('name' => 'Service 4 Title', 'type' => 'text', 'group' => 'Services'),
            'service_4_content' => array('name' => 'Service 4 Content', 'type' => 'textarea', 'group' => 'Services'),
            
            // About section
            'about_section_title' => array('name' => 'About Section Title', 'type' => 'text', 'group' => 'About Us'),
            'about_section_subtitle' => array('name' => 'About Section Subtitle', 'type' => 'textarea', 'group' => 'About Us'),
            'about_title' => array('name' => 'About Title', 'type' => 'text', 'group' => 'About Us'),
            'about_content' => array('name' => 'About Content', 'type' => 'textarea', 'group' => 'About Us'),
            'about_video_url' => array('name' => 'About Video URL', 'type' => 'text', 'group' => 'About Us'),
            'history_title' => array('name' => 'History Title', 'type' => 'text', 'group' => 'About Us'),
            'history_content' => array('name' => 'History Content', 'type' => 'textarea', 'group' => 'About Us'),
            'mission_title' => array('name' => 'Mission Title', 'type' => 'text', 'group' => 'About Us'),
            'mission_content' => array('name' => 'Mission Content', 'type' => 'textarea', 'group' => 'About Us'),
            'vision_title' => array('name' => 'Vision Title', 'type' => 'text', 'group' => 'About Us'),
            'vision_content' => array('name' => 'Vision Content', 'type' => 'textarea', 'group' => 'About Us'),
            
            // Counter section
            'counter_1_number' => array('name' => 'Counter 1 Number', 'type' => 'text', 'group' => 'Statistics'),
            'counter_1_title' => array('name' => 'Counter 1 Title', 'type' => 'text', 'group' => 'Statistics'),
            'counter_2_number' => array('name' => 'Counter 2 Number', 'type' => 'text', 'group' => 'Statistics'),
            'counter_2_title' => array('name' => 'Counter 2 Title', 'type' => 'text', 'group' => 'Statistics'),
            'counter_3_number' => array('name' => 'Counter 3 Number', 'type' => 'text', 'group' => 'Statistics'),
            'counter_3_title' => array('name' => 'Counter 3 Title', 'type' => 'text', 'group' => 'Statistics'),
            'counter_4_number' => array('name' => 'Counter 4 Number', 'type' => 'text', 'group' => 'Statistics'),
            'counter_4_title' => array('name' => 'Counter 4 Title', 'type' => 'text', 'group' => 'Statistics'),
            
            // Call to Action
            'cta_title' => array('name' => 'CTA Title', 'type' => 'text', 'group' => 'Call to Action'),
            'cta_content' => array('name' => 'CTA Content', 'type' => 'textarea', 'group' => 'Call to Action'),
            
            // News section
            'news_section_title' => array('name' => 'News Section Title', 'type' => 'text', 'group' => 'Latest News'),
            'news_section_subtitle' => array('name' => 'News Section Subtitle', 'type' => 'textarea', 'group' => 'Latest News'),
            'news_1_title' => array('name' => 'News 1 Title', 'type' => 'text', 'group' => 'Latest News'),
            'news_1_content' => array('name' => 'News 1 Content', 'type' => 'textarea', 'group' => 'Latest News'),
            'news_2_title' => array('name' => 'News 2 Title', 'type' => 'text', 'group' => 'Latest News'),
            'news_2_content' => array('name' => 'News 2 Content', 'type' => 'textarea', 'group' => 'Latest News'),
            'news_3_title' => array('name' => 'News 3 Title', 'type' => 'text', 'group' => 'Latest News'),
            'news_3_content' => array('name' => 'News 3 Content', 'type' => 'textarea', 'group' => 'Latest News'),
        );
    }
}
