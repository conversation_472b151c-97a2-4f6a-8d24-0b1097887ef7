<!DOCTYPE html>
<html lang="<?php echo ($this->customlib->getRTL() != "") ? "ar" : "en"; ?>" <?php echo ($this->customlib->getRTL() != "") ? "dir='rtl'" : ""; ?>>
<head>
    <!-- meta tag -->
    <meta charset="utf-8">
    <title><?php echo $this->customlib->getAppName(); ?> | <?php echo isset($page_title) ? $page_title : 'Home'; ?></title>
    
    <!-- responsive tag -->
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    
    <!-- favicon -->
    <?php if (!empty($school_setting->fav_icon)) { ?>
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo base_url('uploads/school_content/logo/' . $school_setting->fav_icon); ?>">
    <?php } else { ?>
        <link rel="shortcut icon" type="image/x-icon" href="<?php echo base_url('backend/themes/edulearn/images/fav.png'); ?>">
    <?php } ?>
    
    <!-- bootstrap v4 css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/bootstrap.min.css'); ?>">
    <!-- font-awesome css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/font-awesome.min.css'); ?>">
    <!-- animate css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/animate.css'); ?>">
    <!-- owl.carousel css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/owl.carousel.css'); ?>">
    <!-- slick css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/slick.css'); ?>">
    <!-- magnific popup css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/magnific-popup.css'); ?>">
    <!-- Offcanvas CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/off-canvas.css'); ?>">
    <!-- flaticon css  -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/fonts/flaticon.css'); ?>">
    <!-- flaticon2 css  -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/fonts/fonts2/flaticon.css'); ?>">
    <!-- rsmenu CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/rsmenu-main.css'); ?>">
    <!-- rsmenu transitions CSS -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/rsmenu-transitions.css'); ?>">
    <!-- style css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/style.css'); ?>">
    <!-- responsive css -->
    <link rel="stylesheet" type="text/css" href="<?php echo base_url('backend/themes/edulearn/css/responsive.css'); ?>">
    
    <!-- jQuery -->
    <script src="<?php echo base_url('backend/themes/edulearn/js/jquery.min.js'); ?>"></script>
    
    <script type="text/javascript">
        var base_url = "<?php echo base_url(); ?>";
        var currency_symbol = "<?php echo $this->customlib->getSchoolCurrencyFormat(); ?>";
    </script>
    
    <style>
        <?php 
        // Custom CSS for school branding
        $primary_color = '#007bff';
        $secondary_color = '#6c757d';
        
        // Get custom CSS if exists
        $school_id = isset($this->school_id) ? $this->school_id : (isset($school_setting->id) ? $school_setting->id : 0);
        $custom_css = $this->db->get_where('frontend_cssjs', array('school_id' => $school_id)); 
        if($custom_css->num_rows() == 1 ){
            $css_row = $custom_css->row_array(); 
            if(!empty($css_row['custom_css'])) {
                echo $css_row['custom_css'];
            }
        }
        ?>
        
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
        }
        
        /* Custom theme colors */
        .rs-header .rs-header-top {
            background-color: var(--primary-color);
        }
        
        .rs-toolbar {
            background-color: var(--secondary-color);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            opacity: 0.9;
        }
    </style>
    
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
        <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body class="home1">
    <!--Preloader area start here-->
    <div class="book_preload">
        <div class="book">
            <div class="book__page"></div>
            <div class="book__page"></div>
            <div class="book__page"></div>
        </div>
    </div>
    <!--Preloader area end here-->
    
    <!--Full width header Start-->
    <div class="full-width-header">
        <?php $this->load->view('themes/edulearn/header'); ?>
        
        <!-- Main content area -->
        <main>
            <?php 
            if (isset($content) && !empty($content)) {
                echo $content; // Content is already loaded in the controller
            }
            ?>
        </main>
        
        <?php $this->load->view('themes/edulearn/footer'); ?>
    </div>
    
    <!-- All JavaScript files -->
    <script src="<?php echo base_url('backend/themes/edulearn/js/modernizr-2.8.3.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/bootstrap.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/rsmenu-main.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/owl.carousel.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/slick.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/jquery.magnific-popup.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/waypoints.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/jquery.counterup.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/imagesloaded.pkgd.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/isotope.pkgd.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/wow.min.js'); ?>"></script>
    <script src="<?php echo base_url('backend/themes/edulearn/js/main.js'); ?>"></script>
    
    <?php
    // Load custom JavaScript if exists
    if($custom_css->num_rows() == 1 ){
        $css_row = $custom_css->row_array(); 
        if(!empty($css_row['custom_js'])) {
            echo '<script>' . $css_row['custom_js'] . '</script>';
        }
    }
    ?>
</body>
</html>
